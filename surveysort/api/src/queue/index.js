import { Queue } from 'bullmq'
import IORedis from 'ioredis'

export const connection = new IORedis(process.env.REDIS_CONNECTION_URL, {
  maxRetriesPerRequest: null,
})

export const transactionalQueue = new Queue('transactionalQueue', { connection })
export const analyticsQueue = new Queue('analyticsQueue', { connection })
export const gradingQueue = new Queue('gradingQueue', { connection })
export const notificationQueue = new Queue('notificationQueue', { connection })
export const reportExportRequestsQueue = new Queue('reportExportRequestsQueue', { connection })

// Add scheduler for notification digests
export const scheduleDigestNotifications = async () => {
  // Daily digest at midnight
  await notificationQueue.add(
    'sendDailyDigest',
    {}, // job data
    {
      jobId: 'daily-digest', // unique job id to prevent duplicates
      repeat: {
        pattern: '0 0 * * *' // Midnight every day
      }
    }
  )

  // Weekly digest Sunday midnight
  await notificationQueue.add(
    'sendWeeklyDigest',
    {}, // job data
    {
      jobId: 'weekly-digest', // unique job id to prevent duplicates
      repeat: {
        pattern: '0 0 * * 0' // Midnight every Sunday
      }
    }
  )
}
