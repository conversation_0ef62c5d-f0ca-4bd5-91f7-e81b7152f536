import { ForbiddenError } from '@redwoodjs/graphql-server'

import { db } from 'src/lib/db'

const checkSurveyAccess = async (surveyId, currentUser) => {
  const survey = await db.survey.findUnique({
    where: { id: surveyId },
    select: {
      accountId: true,
      account: { select: { name: true } },
    },
  })

  if (
    !survey ||
    (survey.accountId !== currentUser.accountId &&
      survey.account.name !== 'Demo Account')
  ) {
    throw new ForbiddenError("You don't have permission to access this survey.")
  }
}

export const createQuestion = async ({ input }) => {
  const { currentUser } = context

  const questionsCount = await db.question.count({
    where: {
      surveyId: input.surveyId,
      isDeleted: false
    }
  })
  // Ensure the type is properly formatted
  const formattedInput = {
    ...input,
    type: input.type.toUpperCase(), // Ensure uppercase
    questionConfig: {
      ...input.questionConfig,
      // Convert any special types as needed
      choices: Array.isArray(input.questionConfig?.choices)
        ? input.questionConfig.choices
        : [],
    },
    pageNumber: input.pageNumber || 0,
    order: input.order || questionsCount,
    isDeleted: false,
  }

  return db.question.create({
    data: formattedInput,
  })
}

export const updateQuestion = async ({ id, input }) => {
  const { currentUser } = context

  const question = await db.question.findUnique({
    where: { id },
    select: { surveyId: true, pageNumber: true },
  })

  if (!question) {
    throw new Error('Question not found')
  }

  await checkSurveyAccess(question.surveyId, currentUser)

  // Ensure pageNumber is never null by using the existing value if not provided
  const pageNumber = input.pageNumber ?? question.pageNumber

  // Sanitize randomization inputs
  if (input.questionConfig) {
    const config = input.questionConfig
    if (config.randomize) {
      // Handle multiple choice randomization
      if (Array.isArray(config.randomizeChoices)) {
        config.randomizeChoices = config.randomizeChoices.map(i => parseInt(i)).filter(i => !isNaN(i))
      }
      // Handle matrix row randomization
      if (Array.isArray(config.randomizeRows)) {
        config.randomizeRows = config.randomizeRows.map(i => parseInt(i)).filter(i => !isNaN(i))
      }
    } else {
      // Clear randomization settings when disabled
      config.randomizeChoices = null
      config.randomizeRows = null
    }
  }
  console.log('updateQuestion input:', input)
  return db.question.update({
    data: {
      ...input,
      pageNumber,
    },
    where: { id, isDeleted: false },
  })
}

export const deleteQuestion = async ({ id }) => {
  const { currentUser } = context

  const question = await db.question.findUnique({
    where: { id },
    select: { surveyId: true },
  })

  if (!question) {
    throw new Error('Question not found')
  }

  await checkSurveyAccess(question.surveyId, currentUser)

  return db.question.update({
    where: { id },
    data: { isDeleted: true },
  })
}

export const surveyQuestions = async ({ surveyId }) => {
  const { currentUser } = context
  await checkSurveyAccess(surveyId, currentUser)

  // Just get questions ordered by pageNumber and order
  return db.question.findMany({
    where: {
      surveyId,
      isDeleted: false
    },
    orderBy: [
      { pageNumber: 'asc' },
      { order: 'asc' }
    ],
  })
}

export const updateQuestions = async ({ input }) => {
  const { currentUser } = context
  const { questions } = input

  const questionIds = questions.map((q) => q.id)
  const questionsWithSurveys = await db.question.findMany({
    where: { id: { in: questionIds } },
    select: {
      id: true,
      surveyId: true,
    },
  })

  for (const question of questionsWithSurveys) {
    await checkSurveyAccess(question.surveyId, currentUser)
  }

  const updatedQuestions = await db.$transaction(
    questions.map((question) =>
      db.question.update({
        data: question,
        where: { id: question.id, isDeleted: false },
      })
    )
  )
  return updatedQuestions
}

export const reorderQuestions = async ({ input }) => {
  const { currentUser } = context
  const { surveyId, questions } = input

  await checkSurveyAccess(surveyId, currentUser)

  return await db.$transaction(async (prisma) => {
    // First validate all questions belong to this survey
    const existingQuestions = await prisma.question.findMany({
      where: {
        surveyId,
        id: { in: questions.map(q => q.id) },
        isDeleted: false,
      },
    })

    if (existingQuestions.length !== questions.length) {
      throw new Error('Some questions not found or do not belong to this survey')
    }

    // Update all questions in parallel within the transaction
    const updatePromises = questions.map(({ id, pageNumber, order }) => {
      return prisma.question.update({
        where: {
          id,
          surveyId, // Additional safety check
          isDeleted: false,
        },
        data: {
          pageNumber,
          order,
        },
      })
    })

    try {
      await Promise.all(updatePromises)
    } catch (error) {
      console.error('Error updating questions:', error)
      throw new Error('Failed to update question order')
    }

    // Return questions in their new order
    return prisma.question.findMany({
      where: {
        surveyId,
        isDeleted: false
      },
      orderBy: [
        { pageNumber: 'asc' },
        { order: 'asc' }
      ],
    })
  })
}

export const Question = {
  survey: (_obj, { root }) => {
    return db.question
      .findUnique({ where: { id: root?.id, isDeleted: false } })
      .survey()
  },
}
