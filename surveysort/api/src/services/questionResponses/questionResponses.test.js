import {
  surveyQuestionResponses,
  surveyQuestionResponse,
  createSurveyQuestionResponse,
  updateSurveyQuestionResponse,
  deleteSurveyQuestionResponse,
} from './surveyQuestionResponses'

// Generated boilerplate tests do not account for all circumstances
// and can fail without adjustments, e.g. Float.
//           Please refer to the RedwoodJS Testing Docs:
//       https://redwoodjs.com/docs/testing#testing-services
// https://redwoodjs.com/docs/testing#jest-expect-type-considerations

describe('surveyQuestionResponses', () => {
  scenario('returns all surveyQuestionResponses', async (scenario) => {
    const result = await surveyQuestionResponses()

    expect(result.length).toEqual(
      Object.keys(scenario.surveyQuestionResponse).length
    )
  })

  scenario('returns a single surveyQuestionResponse', async (scenario) => {
    const result = await surveyQuestionResponse({
      id: scenario.surveyQuestionResponse.one.id,
    })

    expect(result).toEqual(scenario.surveyQuestionResponse.one)
  })

  scenario('creates a surveyQuestionResponse', async (scenario) => {
    const result = await createSurveyQuestionResponse({
      input: {
        surveyResponseId: scenario.surveyQuestionResponse.two.surveyResponseId,
        questionId: scenario.surveyQuestionResponse.two.questionId,
        questionNumber: 9968545,
        response: { foo: 'bar' },
        timeToSubmit: 2730523.61784621,
        isResponseCopyPasted: true,
        submissionTime: '2024-07-26T13:30:46.914Z',
      },
    })

    expect(result.surveyResponseId).toEqual(
      scenario.surveyQuestionResponse.two.surveyResponseId
    )
    expect(result.questionId).toEqual(
      scenario.surveyQuestionResponse.two.questionId
    )
    expect(result.questionNumber).toEqual(9968545)
    expect(result.response).toEqual({ foo: 'bar' })
    expect(result.timeToSubmit).toEqual(2730523.61784621)
    expect(result.isResponseCopyPasted).toEqual(true)
    expect(result.submissionTime).toEqual(new Date('2024-07-26T13:30:46.914Z'))
  })

  scenario('updates a surveyQuestionResponse', async (scenario) => {
    const original = await surveyQuestionResponse({
      id: scenario.surveyQuestionResponse.one.id,
    })
    const result = await updateSurveyQuestionResponse({
      id: original.id,
      input: { questionNumber: 121627 },
    })

    expect(result.questionNumber).toEqual(121627)
  })

  scenario('deletes a surveyQuestionResponse', async (scenario) => {
    const original = await deleteSurveyQuestionResponse({
      id: scenario.surveyQuestionResponse.one.id,
    })
    const result = await surveyQuestionResponse({ id: original.id })

    expect(result).toEqual(null)
  })
})
