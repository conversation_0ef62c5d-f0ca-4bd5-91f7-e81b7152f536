import { db } from 'src/lib/db'
import { gradingQueue } from 'src/queue/index'
import { logger } from 'src/lib/logger'

export const surveyQuestionResponse = ({ id }) => {
  return db.surveyQuestionResponse.findUnique({
    where: { id },
  })
}

// export const createSurveyQuestionResponse = async ({ input }) => {
//   if (input.surveyResponseId && input.questionNumber > 0) {
//     const surveyResponseResult = await db.surveyResponse.findUnique({
//       where: { id: input.surveyResponseId },
//     })
//     /* check if the quetion number already exists . if so then update that */
//     let found = false
//     for (let i = 0; i < surveyResponseResult.questionResponses.length; i++) {
//       if (
//         surveyResponseResult.questionResponses[i].questionNumber ===
//         input.questionNumber
//       ) {
//         surveyResponseResult.questionResponses[i] = input
//         found = true
//         break
//       }
//     }
//     if (!found) {
//       //append
//       surveyResponseResult.questionResponses.push(input)
//     }
//     // Determine if the survey is complete
//     const lastQuestionNumber = 10 // Replace with the actual last question number
//     const isComplete = surveyResponseResult.questionResponses.some(
//       (qr) => qr.questionNumber === lastQuestionNumber
//     )

//     // TODO: MARK survey as complete if the answer to the last question  is submitted
//     if (surveyResponseResult) {
//       return db.surveyResponse.update({
//         data: surveyResponseResult,
//         where: { id: input.surveyResponseId },
//       })
//     }
//   }
//   throw new Error(
//     'surveyResponseId not found or question number <=1. survey question response  create failed.'
//   )
// }

export const createQuestionResponse = async ({ input }) => {
  console.log('Creating new question response', { 
    surveyResponseId: input.surveyResponseId,
    questionNumber: input.questionNumber,
    pageNumber: input.pageNumber
  })

  if (input.surveyResponseId) {
    const surveyResponseResult = await db.surveyResponse.findUnique({
      where: { id: input.surveyResponseId },
    })

    console.log('Creating new question response', { 
      surveyResponseId: input.surveyResponseId,
      questionNumber: input.questionNumber,
      pageNumber: input.pageNumber
    })

    await gradingQueue.add('gradeSurveyResponse', {
      surveyResponseId: input.surveyResponseId,
      surveyId: surveyResponseResult.surveyId,
    })
    console.log('Grading job added to queue', { 
      surveyResponseId: input.surveyResponseId,
      surveyId: surveyResponseResult.surveyId
    })
    if (!surveyResponseResult) {
      logger.error('Survey response not found', { surveyResponseId: input.surveyResponseId })
      console.log('Survey response not found', { surveyResponseId: input.surveyResponseId })
      throw new Error('Survey response not found')
    }

    const surveyResult = await db.survey.findUnique({
      where: { id: surveyResponseResult.surveyId },
      include: { questions: { where: { isDeleted: false } } },
    })

    if (!surveyResult) {
      console.error('Survey not found', { surveyId: surveyResponseResult.surveyId })
      logger.error('Survey not found', { surveyId: surveyResponseResult.surveyId })
      throw new Error('Survey not found')
    }

    let questionResponses = surveyResponseResult.questionResponses || []
    logger.debug('Processing question responses', {
      existingResponsesCount: questionResponses.length,
      newQuestionNumber: input.questionNumber
    })

    // Check if the question number already exists and update it if so
    let found = false

    for (let i = 0; i < questionResponses.length; i++) {
      if (questionResponses[i].questionNumber === input.questionNumber &&
        questionResponses[i].pageNumber === input.pageNumber
      ) {
        questionResponses[i] = { ...questionResponses[i], ...input }
        found = true
        break
      }
    }

    if (!found) {
      // Append new question response
      questionResponses.push(input)
      try {
        logger.info('Adding response to grading queue', {
          surveyResponseId: input.surveyResponseId,
          surveyId: input.surveyId
        })
        console.log('Adding response to grading queue', {
          surveyResponseId: input.surveyResponseId,
          surveyId: input.surveyId
        })
        await gradingQueue.add('gradeSurveyResponse', {
          surveyResponseId: input.surveyResponseId,
          surveyId: input.surveyId,
        })
      } catch (error) {
        logger.error('Failed to add grading job to queue', {
          error: error.message,
          surveyResponseId: input.surveyResponseId
        })
        console.error('Error adding grading job to queue:', error)
      }
    }

    // Determine if the survey is complete
    const totalQuestions = surveyResult.questions.length
    const isComplete = questionResponses.length === totalQuestions
    
    logger.debug('Survey completion status', {
      totalQuestions,
      answeredQuestions: questionResponses.length,
      isComplete
    })

    // Update the survey response
    const updatedSurveyResponse = await db.surveyResponse.update({
      where: { id: input.surveyResponseId },
      data: {
        questionResponses,
        isComplete,
      },
    })

    const updatedQuestionResponse = updatedSurveyResponse.questionResponses.find(
      (response) => response.pageNumber === input.pageNumber && response.questionNumber === input.questionNumber
    )

    return {
      id: updatedSurveyResponse.id,
      questionId: updatedQuestionResponse ? updatedQuestionResponse.questionId : null,
    }

  }

  logger.error('Invalid survey response creation attempt', {
    surveyResponseId: input.surveyResponseId,
    questionNumber: input.questionNumber
  })
  throw new Error(
    'surveyResponseId not found or question number <= 0. Survey question response creation failed.'
  )
}

// export const SurveyQuestionResponse = {
//   surveyResponse: (_obj, { root }) => {
//     return db.surveyQuestionResponse
//       .findUnique({ where: { id: root?.id } })
//       .surveyResponse()
//   },
//   surveyQuestion: (_obj, { root }) => {
//     return db.surveyQuestionResponse
//       .findUnique({ where: { id: root?.id } })
//       .surveyQuestion()
//   },
// }
