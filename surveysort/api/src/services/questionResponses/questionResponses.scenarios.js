export const standard = defineScenario({
  surveyQuestionResponse: {
    one: {
      data: {
        questionNumber: 9337035,
        response: { foo: 'bar' },
        timeToSubmit: 7706950.*********,
        isResponseCopyPasted: true,
        submissionTime: '2024-07-26T13:30:47.102Z',
        surveyResponse: {
          create: {
            surveyId: 'String',
            ipAddress: 'String',
            fingerprint: 'String',
            screenResolution: 'String',
            timezone: 'String',
            userAgent: 'String',
            submissionTime: '2024-07-26T13:30:47.102Z',
            lastActiveTime: '2024-07-26T13:30:47.102Z',
            account: { create: {} },
          },
        },
        surveyQuestion: {
          create: {
            title: 'String',
            type: 'MULTIPLE_CHOICE',
            survey: { create: { title: 'String', account: { create: {} } } },
          },
        },
      },
    },
    two: {
      data: {
        questionNumber: 4783616,
        response: { foo: 'bar' },
        timeToSubmit: 1133914.**********,
        isResponseCopyPasted: true,
        submissionTime: '2024-07-26T13:30:47.102Z',
        surveyResponse: {
          create: {
            surveyId: 'String',
            ipAddress: 'String',
            fingerprint: 'String',
            screenResolution: 'String',
            timezone: 'String',
            userAgent: 'String',
            submissionTime: '2024-07-26T13:30:47.102Z',
            lastActiveTime: '2024-07-26T13:30:47.102Z',
            account: { create: {} },
          },
        },
        surveyQuestion: {
          create: {
            title: 'String',
            type: 'MULTIPLE_CHOICE',
            survey: { create: { title: 'String', account: { create: {} } } },
          },
        },
      },
    },
  },
})
