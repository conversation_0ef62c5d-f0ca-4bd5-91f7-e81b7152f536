import { db } from 'src/lib/db'
import { logger } from 'src/lib/logger'
import stripe from 'src/lib/stripe'
import { context } from '@redwoodjs/graphql-server'

// Add trial limits constant
export const TRIAL_LIMITS = {
  aiQuestions: 10,
  responseGradings: 10,
  insightConversations: 5
}

export const getPlans = async () => {
  try {
    // First, get all active products with expanded default price
    const stripeProducts = await stripe.products.list({
      active: true,
      expand: ['data.default_price']
    })

    if (!stripeProducts?.data) {
      console.error('No products returned from Stripe')
      return []
    }

    // Get all prices in a separate call
    const stripePrices = await stripe.prices.list({
      active: true,
      expand: ['data.product']
    })

    // Get current subscription if user is logged in
    let currentSubscription = null
    if (context.currentUser?.accountId) {
      const account = await db.account.findUnique({
        where: { id: context.currentUser.accountId },
        select: { stripeCustomerId: true }
      })

      if (account?.stripeCustomerId) {
        const subscriptions = await stripe.subscriptions.list({
          customer: account.stripeCustomerId,
          status: 'all',
          expand: ['data.items.data.price']
        })
        currentSubscription = subscriptions.data.find(sub => 
          ['active', 'trialing'].includes(sub.status)
        )
      }
    }

    // Transform products into plans with metered components
    return stripeProducts.data.map(product => {
      const basePrice = product.default_price
      if (!basePrice) {
        console.error(`No default price for product ${product.id}`)
        return null
      }

      // Get all prices for this product
      const productPrices = stripePrices.data.filter(
        price => price.product.id === product.id
      )

      const meteredPrices = productPrices.filter(
        p => p.recurring?.usage_type === 'metered'
      )

      // Calculate annual discount if both monthly and yearly prices exist
      const monthlyPrice = productPrices.find(
        p => p.recurring?.interval === 'month' && p.metadata.type === 'base'
      )
      const yearlyPrice = productPrices.find(
        p => p.recurring?.interval === 'year' && p.metadata.type === 'base'
      )

      let discount = null
      if (monthlyPrice && yearlyPrice) {
        const monthlyAnnual = monthlyPrice.unit_amount * 12
        const yearlyAmount = yearlyPrice.unit_amount
        discount = ((monthlyAnnual - yearlyAmount) / monthlyAnnual) * 100
      }

      // Safely calculate prices, defaulting to null if not available
      const monthlyAmount = monthlyPrice?.unit_amount ? monthlyPrice.unit_amount / 100 : null
      const yearlyPricePerMonth = yearlyPrice?.unit_amount ? yearlyPrice.unit_amount / 1200 : null
      const yearlyPriceTotal = yearlyPrice?.unit_amount ? yearlyPrice.unit_amount / 100 : null

      return {
        priceId: basePrice.id,
        productId: product.id,
        name: product.name,
        description: product.description,
        monthlyPrice: monthlyAmount,
        yearlyPrice: yearlyPricePerMonth,
        yearlyPriceTotal: yearlyPriceTotal,
        interval: basePrice.recurring?.interval || 'month',
        currency: basePrice.currency,
        features: product.metadata.features?.split(',') || [],
        isCurrent: Boolean(currentSubscription?.items.data.some(
          item => item.price.product.id === product.id
        )),
        meteredComponents: meteredPrices.map(price => ({
          name: price.metadata.component_name || '',
          includedQuantity: parseInt(price.metadata.included_quantity || '0'),
          unitAmount: price.unit_amount || 0
        })),
        discount: discount,
        isEnterprise: product.metadata.isEnterprise === 'true',
        hideFromPricing: product.metadata.hideFromPricing === 'true',
        contactSales: product.metadata.contactSales === 'true'
      }
    }).filter(Boolean) // Remove any null entries
  } catch (error) {
    console.error('Error fetching plans:', error)
    throw error
  }
}

export const createCheckoutSession = async ({ priceId }) => {
  const { currentUser } = context
  const account = await db.account.findUnique({
    where: { id: currentUser.accountId }
  })

  // Get the base price and its metered components
  const price = await stripe.prices.retrieve(priceId, {
    expand: ['product']
  })

  // Get metered prices for this product
  const meteredPrices = await stripe.prices.list({
    product: price.product.id,
    type: 'recurring',
    recurring: { usage_type: 'metered' }
  })

  // Create line items including metered components
  const lineItems = [
    { price: priceId, quantity: 1 },
    ...meteredPrices.data.map(price => ({
      price: price.id,
      quantity: 1
    }))
  ]

  const session = await stripe.checkout.sessions.create({
    mode: 'subscription',
    line_items: lineItems,
    success_url: `${process.env.FE_URL}/subscription-success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.FE_URL}/subscription-canceled`,
    customer: account.stripeCustomerId,
    subscription_data: {
      trial_from_plan: true,
      metadata: {
        accountId: account.id
      }
    }
  })

  return { sessionId: session.id, url: session.url }
}

// Helper to check if account can use paid features
export const canUseFeature = async (accountId, feature) => {
  const account = await db.account.findUnique({
    where: { id: accountId },
    select: { 
      stripeCustomerId: true,
      trialEndsAt: true 
    }
  })

  if (!account?.stripeCustomerId) return false

  const subscriptions = await stripe.subscriptions.list({
    customer: account.stripeCustomerId,
    status: 'all'
  })

  const subscription = subscriptions.data.find(sub => 
    ['active', 'trialing'].includes(sub.status)
  )

  if (!subscription) return false

  // If on trial, check trial limits
  if (subscription.status === 'trialing') {
    const usage = await getCurrentUsage(accountId)
    if (!usage) return false

    switch (feature) {
      case 'aiQuestions':
        return usage.aiQuestions < TRIAL_LIMITS.aiQuestions
      case 'responseGradings':
        return usage.responseGradings < TRIAL_LIMITS.responseGradings
      case 'insightConversations':
        return usage.insightConversations < TRIAL_LIMITS.insightConversations
      default:
        return false
    }
  }

  // If paid subscription, check plan limits
  return subscription.status === 'active'
}

// Helper to get current usage for an account
export const getCurrentUsage = async (accountId) => {
  const account = await db.account.findUnique({
    where: { id: accountId },
    select: { stripeCustomerId: true }
  })

  if (!account?.stripeCustomerId) return null

  // Get current subscription to determine billing period
  const subscriptions = await stripe.subscriptions.list({
    customer: account.stripeCustomerId,
    status: 'all',
    expand: ['data.items.data.price']
  })

  const subscription = subscriptions.data.find(sub => 
    ['active', 'trialing'].includes(sub.status)
  )

  if (!subscription) return null

  const periodStart = new Date(subscription.current_period_start * 1000)

  // Get usage only for current billing period
  const usage = await db.account.findUnique({
    where: { id: accountId },
    include: {
      surveys: {
        where: {
          isGeneratedByAi: true,
          createdAt: { gte: periodStart }
        },
        include: {
          questions: true,
          SurveyResponseEnrichedData: {
            where: { 
              isGradingComplete: true,
              createdAt: { gte: periodStart }
            }
          },
          surveyInsightConversations: {
            where: {
              createdAt: { gte: periodStart }
            }
          }
        }
      }
    }
  })

  return {
    periodStart,
    periodEnd: new Date(subscription.current_period_end * 1000),
    aiQuestions: usage.surveys.reduce((sum, survey) => 
      sum + survey.questions.length, 0),
    responseGradings: usage.surveys.reduce((sum, survey) => 
      sum + survey.SurveyResponseEnrichedData.length, 0),
    insightConversations: usage.surveys.reduce((sum, survey) => 
      sum + survey.surveyInsightConversations.length, 0)
  }
}

// Add usage reporting logic here
export const reportUsageForAccount = async (accountId) => {
  const account = await db.account.findUnique({
    where: { id: accountId },
    select: { 
      stripeCustomerId: true,
      lastUsageReportAt: true
    }
  })

  if (!account?.stripeCustomerId) return null

  const subscriptions = await stripe.subscriptions.list({
    customer: account.stripeCustomerId,
    status: 'active',
    expand: ['data.items.data.price']
  })

  const subscription = subscriptions.data[0]
  if (!subscription) return null

  const periodStart = new Date(subscription.current_period_start * 1000)
  const lastReportTime = account.lastUsageReportAt || periodStart

  const usageItems = subscription.items.data.filter(
    item => item.price.recurring?.usage_type === 'metered'
  )

  const usageReports = []

  for (const item of usageItems) {
    try {
      const componentName = item.price.metadata.component_name
      const blockSize = parseInt(item.price.metadata.block_size, 10) || 100

      // Get raw usage
      const rawUsage = await calculateUsageSince(
        accountId,
        componentName,
        lastReportTime
      )

      // Convert to blocks of 100
      const usageBlocks = Math.ceil(rawUsage / blockSize)
      const reportableUsage = usageBlocks * blockSize

      if (reportableUsage > 0) {
        // Create idempotency key
        const idempotencyKey = `usage_${accountId}_${item.id}_${Math.floor(Date.now() / 1000)}`

        const usageRecord = await stripe.subscriptionItems.createUsageRecord(
          item.id,
          {
            quantity: reportableUsage,
            timestamp: Math.floor(Date.now() / 1000),
            action: 'increment'
          },
          { idempotencyKey }
        )

        usageReports.push({
          componentName,
          rawUsage,
          reportableUsage,
          recordId: usageRecord.id
        })

        logger.info(`Reported ${componentName} usage`, {
          accountId,
          rawUsage,
          reportableUsage,
          recordId: usageRecord.id
        })
      }
    } catch (error) {
      logger.error(`Failed to report usage`, {
        accountId,
        itemId: item.id,
        error: error.message
      })
      // Don't throw - continue with other components
      usageReports.push({
        componentName: item.price.metadata.component_name,
        error: error.message
      })
    }
  }

  // Update last report time only if all reports succeeded
  if (usageReports.every(report => !report.error)) {
    await db.account.update({
      where: { id: accountId },
      data: { lastUsageReportAt: new Date() }
    })
  }

  return usageReports
}

// Helper to calculate usage since a timestamp
const calculateUsageSince = async (accountId, componentName, since) => {
  const startDate = new Date(since)
  
  switch (componentName) {
    case 'AI Questions':
      return await db.survey.count({
        where: {
          accountId,
          isGeneratedByAi: true,
          createdAt: { gt: startDate }
        }
      })

    case 'Response Gradings':
      return await db.surveyResponseEnrichedData.count({
        where: {
          surveyResponse: {
            survey: { accountId }
          },
          isGradingComplete: true,
          createdAt: { gt: startDate }
        }
      })

    case 'Insight Conversations':
      return await db.surveyInsightConversation.count({
        where: {
          accountId,
          createdAt: { gt: startDate }
        }
      })

    default:
      return 0
  }
}

export const createTrialSubscription = async (accountId, email) => {
  try {
    // Create Stripe customer
    const customer = await stripe.customers.create({
      email,
      metadata: {
        accountId: accountId,
        converted_to_paid: 'false'
      }
    })

    // Create trial subscription with base product only
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      trial_period_days: 7,
      items: [
        { price: process.env.STRIPE_TRIAL_PRICE_ID }
      ],
      metadata: {
        accountId,
        is_trial: 'true'
      }
    })

    // Update account with Stripe info (no trial limits)
    await db.account.update({
      where: { id: accountId },
      data: {
        stripeCustomerId: customer.id,
        trialStartedAt: new Date(),
        trialEndsAt: new Date(subscription.trial_end * 1000)
      }
    })

    return subscription
  } catch (error) {
    logger.error('Error creating trial subscription:', {
      error,
      accountId,
      email
    })
    throw error
  }
}

// Update checkTrialLimits to use constant
export const checkTrialLimits = async (accountId, feature) => {
  const account = await db.account.findUnique({
    where: { id: accountId },
    select: {
      trialEndsAt: true,
      stripeCustomerId: true
    }
  })

  if (!account) return false

  // If not on trial, use regular subscription checks
  if (!account.trialEndsAt || account.trialEndsAt < new Date()) {
    return canUseFeature(accountId, feature)
  }

  const usage = await getCurrentUsage(accountId)

  switch (feature) {
    case 'aiQuestions':
      return usage.aiQuestions < TRIAL_LIMITS.aiQuestions
    case 'responseGradings':
      return usage.responseGradings < TRIAL_LIMITS.responseGradings
    case 'insightConversations':
      return usage.insightConversations < TRIAL_LIMITS.insightConversations
    default:
      return false
  }
}

export const getCurrentUsageAndLimits = async (accountId) => {
  const usage = await getCurrentUsage(accountId)
  if (!usage) return null

  const account = await db.account.findUnique({
    where: { id: accountId },
    select: { stripeCustomerId: true }
  })

  const subscriptions = await stripe.subscriptions.list({
    customer: account.stripeCustomerId,
    status: 'all',
    expand: ['data.items.data.price.product']
  })

  const subscription = subscriptions.data.find(sub => 
    ['active', 'trialing'].includes(sub.status)
  )

  if (!subscription) return null

  const basePrice = subscription.items.data.find(
    item => item.price.metadata.type === 'base'
  )

  return {
    usage,
    limits: {
      aiQuestions: parseInt(basePrice?.price.product.metadata.included_ai_questions || 0),
      responseGradings: parseInt(basePrice?.price.product.metadata.included_gradings || 0),
      insightConversations: parseInt(basePrice?.price.product.metadata.included_conversations || 0)
    }
  }
}

export const checkUsageLimits = async (accountId, feature) => {
  const { usage, limits } = await getCurrentUsageAndLimits(accountId)
  if (!usage || !limits) return false

  switch (feature) {
    case 'aiQuestions':
      return usage.aiQuestions < limits.aiQuestions
    case 'responseGradings':
      return usage.responseGradings < limits.responseGradings
    case 'insightConversations':
      return usage.insightConversations < limits.insightConversations
    default:
      return false
  }
}

// Add trial-specific functions
export const isTrialActive = async (accountId) => {
  const account = await db.account.findUnique({
    where: { id: accountId },
    select: { trialEndsAt: true }
  })
  return account?.trialEndsAt && account.trialEndsAt > new Date()
}

// Add enterprise contact handler
export const requestEnterprisePlan = async ({ name, email, company, message }) => {
  try {
    // Here you would typically:
    // 1. Save the request to your database
    // 2. Send notification email to sales team
    // 3. Send confirmation email to prospect
    
    // For now, just log it
    logger.info('Enterprise plan request received', {
      name,
      email,
      company,
      message
    })
    
    return true
  } catch (error) {
    logger.error('Error handling enterprise request:', error)
    throw error
  }
} 