import { fetch } from '@whatwg-node/fetch'

const mapTimeFrame = (timeframe) => {
  const mapping = {
    ALL: 'all',
    LAST_1_DAY: '1d',
    LAST_7_DAYS: '7d',
    LAST_30_DAYS: '30d',
    LAST_90_DAYS: '90d',
    CUSTOM: 'custom'
  }
  return mapping[timeframe] || 'all'
}

export const fetchQuestionAnalytics = async (surveyId, accountId, input = {}) => {
  try {
    console.log('Fetching analytics with params:', {
      surveyId,
      accountId,
      input
    })

    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/analytics/questions/`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
        body: JSON.stringify({
          survey_id: surveyId,
          account_id: accountId,
          timeframe: input.timeframe ? mapTimeFrame(input.timeframe) : 'all',
          question_ids: input.questionIds,
          question_types: input.questionTypes,
          exclude_incomplete: input.excludeIncomplete,
          exclude_low_quality: input.excludeLowQuality,
        }),
      }
    )
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error:', errorText)
      throw new Error(`API call failed with status ${response.status}: ${errorText}`)
    }
    
    const data = await response.json()
    console.log('API Response:', data)
    return data
  } catch (error) {
    console.error('Error fetching question analytics:', error)
    throw new Error('Failed to fetch question analytics')
  }
}

export const questionAnalytics = async ({ surveyId, input }) => {
  console.log('questionAnalytics called with:', { surveyId, input })
  
  if (!context.currentUser?.accountId) {
    console.error('No accountId found in context')
    throw new Error('User not authenticated')
  }

  try {
    const result = await fetchQuestionAnalytics(surveyId, context.currentUser.accountId, input)
    console.log('questionAnalytics result:', result)
    return result
  } catch (error) {
    console.error('questionAnalytics error:', error)
    throw error
  }
} 