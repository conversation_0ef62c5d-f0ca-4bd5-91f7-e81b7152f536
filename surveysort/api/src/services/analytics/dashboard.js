import { context } from '@redwoodjs/graphql-server'

import { db } from 'src/lib/db'

export const dashboardMetrics = async () => {
  const { currentUser } = context

  // Get surveys for the current user's account
  const userSurveys = await db.survey.findMany({
    where: {
      account: { id: currentUser.accountId },
    },
    select: { id: true },
  })

  const surveyIds = userSurveys.map((survey) => survey.id)

  const totalResponses = await db.surveyResponse.count({
    where: { surveyId: { in: surveyIds } },
  })

  const fraudulentResponses = await db.surveyResponseEnrichedData.count({
    where: {
      OR: [{ vpnDetected: true }, { proxyDetected: true }],
      surveyResponse: { surveyId: { in: surveyIds } },
    },
  })

  const activeSurveys = await db.survey.count({
    where: {
      id: { in: surveyIds },
      status: 'PUBLISHED',
      isDeleted: false,
      isSuspended: false,
    },
  })

  const completedResponses = await db.surveyResponse.count({
    where: {
      surveyId: { in: surveyIds },
      isComplete: true,
    },
  })

  return {
    totalCleanedResponses: totalResponses - fraudulentResponses,
    totalActiveSurveys: activeSurveys,
    responseRate:
      totalResponses > 0 ? (completedResponses / totalResponses) * 100 : 0,
    fraudDetectionRate:
      totalResponses > 0 ? (fraudulentResponses / totalResponses) * 100 : 0,
  }
}
