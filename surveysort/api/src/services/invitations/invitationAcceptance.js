import { db } from 'src/lib/db'
import { hashPassword } from '@redwoodjs/auth-dbauth-api'
import { logger } from 'src/lib/logger'

export const acceptInvitation = async ({ input }) => {
  const { token, password } = input

  const invitation = await db.invitation.findFirst({
    where: {
      token,
      acceptedAt: null,
      isDeleted: false,
      expiresAt: {
        gt: new Date(),
      },
    },
    include: {
      account: true,
    },
  })

  if (!invitation) {
    throw new Error('Invalid or expired invitation')
  }

  const email = invitation.recipientEmail

  // Check if user already exists
  const existingUser = await db.user.findFirst({
    where: { 
      email,
      isDeleted: false 
    },
  })

  if (existingUser) {
    throw new Error('An account with this email already exists. Please sign in to accept the invitation.')
  }

  // Start transaction
  return await db.$transaction(async (db) => {
    // Create password hash
    const [hashedPassword, salt] = hashPassword(password)

    // Create new user
    const user = await db.user.create({
      data: {
        email,
        hashedPassword,
        salt,
        isVerified: true, // Auto-verify since they're accepting an invitation
        props: {},
      },
    })

    // Get USER role
    const userRole = await db.role.findUnique({
      where: { name: 'USER' },
    })

    // Create user role for the account
    await db.userRole.create({
      data: {
        userId: user.id,
        roleId: userRole.id,
        accountId: invitation.accountId,
      },
    })

    // Mark invitation as accepted
    await db.invitation.update({
      where: { id: invitation.id },
      data: {
        acceptedAt: new Date(),
      },
    })

    logger.info('Invitation accepted successfully', {
      invitationId: invitation.id,
      userId: user.id,
      accountId: invitation.accountId,
    })

    return true
  })
}