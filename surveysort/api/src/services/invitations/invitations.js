import { db } from 'src/lib/db'
import { generateToken } from 'src/lib/auth'
import { transactionalQueue } from 'src/queue/index'
import { isEmailAllowed } from 'src/services/accounts/accounts'
import { logger } from 'src/lib/logger'

// Move to top and export for reuse in other services if needed
export const generateInviteUrl = (token) => {
  return `${process.env.FE_URL}/accept-invitation?token=${token}`
}

// Add resolver for single invitation query
export const invitation = async ({ token }) => {
  const invitation = await db.invitation.findFirst({
    where: {
      token,
      isDeleted: false,
      expiresAt: {
        gt: new Date(),
      },
    },
    include: {
      account: true,
      sender: true,
    },
  })

  if (!invitation) {
    throw new Error('Invalid or expired invitation')
  }

  return {
    ...invitation,
    inviteUrl: generateInviteUrl(invitation.token)
  }
}

export const teamMembers = async () => {
  const { accountId } = context.currentUser

  const activeMembers = await db.userRole.findMany({
    where: {
      accountId,
      isDeleted: false,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          createdAt: true,
        },
      },
      role: {
        select: {
          name: true,
        },
      },
    },
  })

  const pendingInvites = await db.invitation.findMany({
    where: {
      accountId,
      acceptedAt: null,
      isDeleted: false,
      expiresAt: {
        gt: new Date(),
      },
    },
  })

  const data = {
    activeMembers: activeMembers.map(({ user, role }) => ({
      id: user.id,
      email: user.email,
      roles: [role.name],
      createdAt: user.createdAt,
    })),
    pendingInvites: pendingInvites.map(invite => ({
      ...invite,
      inviteUrl: generateInviteUrl(invite.token)
    }))
  }

  return data
}

export const getInvitationWithDetails = async (invitationId) => {
  const invitation = await db.invitation.findUnique({
    where: { id: invitationId },
    include: {
      account: true,
      sender: true,
    },
  })

  if (!invitation) {
    throw new Error('Invitation not found')
  }

  return {
    ...invitation,
    inviteUrl: generateInviteUrl(invitation.token)
  }
}

export const inviteMember = async ({ email }) => {
  const { accountId, id: senderId } = context.currentUser

  if (!isEmailAllowed(email)) {
    throw new Error('Only business email addresses are allowed')
  }

  // First check if user exists
  const existingUser = await db.user.findFirst({
    where: { 
      email,
      isDeleted: false 
    },
    include: {
      userRoles: {
        where: {
          isDeleted: false
        },
        include: {
          account: true
        }
      }
    }
  })

  if (existingUser) {
    // Check if user is already a member of this account
    const isMember = existingUser.userRoles.some(role => role.accountId === accountId)
    if (isMember) {
      throw new Error('This user is already a member of your team')
    }
    
    // If they're a member of other accounts, let admin know
    const otherAccounts = existingUser.userRoles.map(role => role.account.name).join(', ')
    if (otherAccounts) {
      throw new Error(`This user already has an account. Please ask them to log in with ${email} to join your team.`)
    }
  }

  // Check for pending invites
  const pendingInvite = await db.invitation.findFirst({
    where: {
      recipientEmail: email,
      accountId,
      acceptedAt: null,
      isDeleted: false,
      expiresAt: {
        gt: new Date(),
      },
    },
  })

  if (pendingInvite) {
    throw new Error('An invitation has already been sent to this email')
  }

  const token = generateToken()
  const expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 48) // 48 hours

  const invitation = await db.invitation.create({
    data: {
      recipientEmail: email,
      accountId,
      senderId,
      token,
      expiresAt,
    },
    include: {
      account: true,
      sender: true,
    },
  })

  await transactionalQueue.add('adminSendsInvitationToUser', {
    invitationId: invitation.id,
    recipientEmail: email
  })

  return {
    ...invitation,
    inviteUrl: generateInviteUrl(invitation.token)
  }
}

export const resendInvite = async ({ id }) => {
  const { accountId } = context.currentUser

  const invitation = await db.invitation.findFirst({
    where: {
      id,
      accountId,
      acceptedAt: null,
      isDeleted: false,
    },
  })

  if (!invitation) {
    throw new Error('Invitation not found')
  }

  const token = generateToken()
  const expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 48)

  const updatedInvitation = await db.invitation.update({
    where: { id },
    data: {
      token,
      expiresAt,
    },
    include: {
      account: true,
      sender: true,
    },
  })

  await transactionalQueue.add('adminSendsInvitationToUser', {
    invitationId: updatedInvitation.id,
    recipientEmail: updatedInvitation.recipientEmail
  })

  return {
    ...updatedInvitation,
    inviteUrl: generateInviteUrl(updatedInvitation.token)
  }
}

export const revokeInvite = async ({ id }) => {
  const { accountId } = context.currentUser

  return await db.invitation.updateMany({
    where: {
      id,
      accountId,
      acceptedAt: null,
      isDeleted: false,
    },
    data: {
      isDeleted: true,
    },
  })
}

export const removeMember = async ({ id }) => {
  const { accountId } = context.currentUser

  // Don't allow removing admins
  const userRole = await db.userRole.findFirst({
    where: {
      userId: id,
      accountId,
      isDeleted: false,
    },
    include: {
      role: true,
    },
  })

  if (!userRole) {
    throw new Error('Member not found')
  }

  if (userRole.role.name === 'ADMIN') {
    throw new Error('Cannot remove admin users')
  }

  return await db.userRole.updateMany({
    where: {
      userId: id,
      accountId,
      isDeleted: false,
    },
    data: {
      isDeleted: true,
    },
  })
}

// Use it in tasks.js
export const adminSendsInvitationToUser = async ({ invitationId, recipientEmail }) => {
  try {
    logger.info('Starting adminSendsInvitationToUser', { invitationId, recipientEmail })
    
    const invitation = await getInvitationWithDetails(invitationId)
    const inviteUrl = generateInviteUrl(invitation.token)

    await mailer.send(
      InvitationEmail({
        inviteUrl,
        senderEmail: invitation.sender.email,
        accountName: invitation.account.name,
      }),
      {
        to: recipientEmail,
        subject: `Join ${invitation.account.name} on SurveySort`,
        from: process.env.NO_REPLY_FROM_ADDRESS,
      }
    )
    
    logger.info('Completed adminSendsInvitationToUser', { invitationId })
  } catch (error) {
    logger.error('Error in adminSendsInvitationToUser task', { 
      error, 
      invitationId, 
      recipientEmail 
    })
    throw error
  }
} 