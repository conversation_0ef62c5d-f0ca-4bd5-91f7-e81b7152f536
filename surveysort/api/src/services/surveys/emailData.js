import { fetchBasicSurveyAnalytics } from 'src/lib/aiService'

// Get response stats for email templates
export const getSurveyResponseStats = async (surveyId, timeWindow = 7 * 24 * 60 * 60 * 1000) => {
  // Convert timeWindow to days
  const days = timeWindow / (24 * 60 * 60 * 1000)
  
  let timeframe = 'LAST_7_DAYS'
  if (days === 1) timeframe = 'LAST_1_DAY'
  else if (days === 30) timeframe = 'LAST_30_DAYS'
  
  try {
    const { overview_metrics, time_series_data } = await fetchBasicSurveyAnalytics(surveyId, timeframe)
    
    // Just map the metrics directly from SurveyOverviewMetrics
    return {
      responseCount: overview_metrics.total_responses,
      completedCount: overview_metrics.completed_responses,
      completionRate: Math.round(overview_metrics.completion_rate),
      averageCompletionTime: overview_metrics.average_response_time,
      dailyResponseCount: time_series_data?.[0]?.responses || 0,
      weeklyResponseCount: time_series_data?.reduce((sum, point) => sum + point.responses, 0) || 0,
      highQualityRate: overview_metrics.high_quality_rate
    }
  } catch (error) {
    console.error('Error getting survey response stats:', error)
    throw error
  }
} 