import SurveyNotFoundError from 'src/commons/errors/SurveyNotFoundError'
import { db } from 'src/lib/db'
import errorCodes from 'src/lib/errorCodes'
import { verifyToken } from 'src/services/participantLinks/participantLinks'

export const endUserSurvey = async ({ id, token }) => {
  let participantEmail = null

  // If token is provided, verify it and get participant email
  if (token) {
    try {
      const participant = await verifyToken(token)
      participantEmail = participant.email

      // Verify that the participant belongs to this survey
      if (participant.surveyId !== id) {
        throw new Error('Token does not match the requested survey')
      }

      // Check if participant has already completed the survey
      if (participant.status === 'COMPLETED') {
        // Return a special response indicating survey is already completed
        return {
          id,
          title: 'Survey Already Completed',
          description:
            'Thank you for your participation! You have already completed this survey.',
          createdBy: 'system',
          createdAt: new Date(),
          status: 'PUBLISHED',
          source: 'CUSTOM',
          participantEmail,
          alreadyCompleted: true,
          questions: [],
          settings: {
            showProgressBar: false,
            removeBranding: false,
            showNavigation: false,
          },
        }
      }
    } catch (error) {
      throw new Error(`Token verification failed: ${error.message}`)
    }
  }
  const survey = await db.survey.findUnique({
    where: { id, isDeleted: false, isSuspended: false },
    include: {
      questions: {
        where: { isDeleted: false },
        orderBy: [{ pageNumber: 'asc' }, { order: 'asc' }],
      },
      settings: true,
    },
  })

  if (!survey) {
    throw new SurveyNotFoundError('Not Found', {
      code: 404,
      errorMsg: errorCodes['10001'],
    })
  }

  console.log('survey.settings', survey.settings)
  // Parse randomizationGroups from JSON if it exists
  if (!survey.settings) {
    survey.settings = {}
  }

  if (survey.settings?.randomizationGroups) {
    try {
      survey.settings.randomizationGroups = JSON.parse(
        survey.settings.randomizationGroups
      )
    } catch (e) {
      survey.settings.randomizationGroups = []
    }
  } else {
    if (survey.settings) survey.settings.randomizationGroups = []
  }

  if (survey.settings?.showProgressBar) {
    try {
      survey.settings.showProgressBar = JSON.parse(
        survey.settings.showProgressBar
      )
    } catch (e) {
      survey.settings.showProgressBar = false
    }
  } else {
    survey.settings.showProgressBar = false
  }

  // Parse themeConfig
  if (survey.settings?.themeConfig) {
    try {
      survey.settings.themeConfig = JSON.parse(survey.settings.themeConfig)
    } catch (e) {
      survey.settings.themeConfig = null
    }
  }

  // parse the preview image
  if (
    survey.settings?.previewImage &&
    survey.settings?.previewImage?.length > 0
  ) {
    const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME
    survey.settings.previewImage = `${process.env.GCP_STORAGE_URL}/${bucketName}/${survey.settings?.previewImage}`
  }

  // parse the preview image
  if (
    survey.settings?.companyLogo &&
    survey.settings?.companyLogo?.length > 0
  ) {
    const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME
    survey.settings.companyLogo = `${process.env.GCP_STORAGE_URL}/${bucketName}/${survey.settings?.companyLogo}`
  }
  // Use preview fields if they exist, otherwise fall back to survey fields
  const settings = {
    ...survey.settings,
    previewTitle: survey.settings?.previewTitle || survey.title,
    previewDescription:
      survey.settings?.previewDescription || survey.description,
    showProgressBar: survey.settings?.showProgressBar || false,
    removeBranding: survey.settings?.removeBranding || false,
    showNavigation: survey.settings?.showNavigation || false,
  }

  return {
    id: survey.id,
    title: survey.title,
    description: survey.description || '',
    createdBy: survey.createdBy,
    createdAt: survey.createdAt,
    questions: survey.questions,
    status: survey.status,
    source: survey.source,
    settings,
    participantEmail,
  }
}
