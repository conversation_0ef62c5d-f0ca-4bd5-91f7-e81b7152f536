import { readFileSync } from 'fs'
import path from 'path'

export const getQuestionTypesFromSDL = () => {

  
    // Extract the GraphQL schema string from the file content
    const schemaString = `input QuestionConfigInput {
    choices: [String!]
    minLength: Int
    maxLength: Int
    rules: [RuleInput!]
    condition: String
    actions: [ActionInput!]
    rating: Int
    scale: Int
    allowMultiple: Boolean
    allowWriteIn: Boolean
    rows: [String!]
    columns: [String!]
    cellType: String
    includeTime: Boolean
    requireName: Boolean
    requireEmail: Boolean
    requirePhone: Boolean
    requireCompany: Boolean
    requireAddress: Boolean
    requireWebsite: Boolean
    randomize: Boolean
    randomizeChoices: [Int!]
    randomizeRows: [Int!]
    welcomeMessage: String
    welcomeDisclaimer: String
    requireDisclaimer: Boolean
    thankYouMessage: String
  }

  type QuestionConfig {
    choices: [String!]
    minLength: Int
    maxLength: Int
    rules: [Rule!]
    condition: String
    actions: [Action!]
    rating: Int
    scale: Int
    allowMultiple: Boolean
    allowWriteIn: Boolean
    rows: [String!]
    columns: [String!]
    cellType: String
    includeTime: Boolean
    requireName: Boolean
    requireEmail: Boolean
    requirePhone: Boolean
    requireCompany: Boolean
    requireAddress: Boolean
    requireWebsite: Boolean
    randomize: Boolean
    randomizeChoices: [Int!]
    randomizeRows: [Int!]
    welcomeMessage: String
    welcomeDisclaimer: String
    requireDisclaimer: Boolean
    thankYouMessage: String
  }


  type Question {
    id: String!
    title: String!
    explainer: String
    type: QuestionType!
    required: Boolean!
    surveyId: String!
    pageNumber: Int!
    order: Int!
    questionConfig: QuestionConfig
    survey: Survey!
  }

  enum QuestionType {
    MULTIPLE_CHOICE
    ESSAY
    RATING
    SCALE
    SHORT
    SCORING
    RANGE
    DATE_TIME
    IMAGE
    MATRIX
    INSTRUCTION
    EMOTICON
    ACCEPT_DENY
    LIKE_DISLIKE
    CONTACT_DETAILS
    WELCOME_MESSAGE
    THANK_YOU_MESSAGE
  }`
  
    // Extract QuestionType enum values
    const questionTypeMatch = schemaString.match(/enum QuestionType {([^}]+)}/)
    const questionTypes = questionTypeMatch
      ? questionTypeMatch[1]
        .trim()
        .split('\n')
        .map((type) => type.trim())
      : []
  
    // Extract QuestionConfigInput fields
    const questionConfigInputMatch = schemaString.match(
      /input QuestionConfigInput {([^}]+)}/
    )
    const questionConfigInput = questionConfigInputMatch
      ? questionConfigInputMatch[1]
        .trim()
        .split('\n')
        .reduce((acc, field) => {
          const [name, type] = field.split(':').map((s) => s.trim())
          acc[name] = type.replace(/!$/, '') // Remove the non-null operator
          return acc
        }, {})
      : {}
  
    // Extract CreateQuestionInput fields
    const createQuestionInputMatch = schemaString.match(
      /input CreateQuestionInput {([^}]+)}/
    )
    const createQuestionInput = createQuestionInputMatch
      ? createQuestionInputMatch[1]
        .trim()
        .split('\n')
        .reduce((acc, field) => {
          const [name, type] = field.split(':').map((s) => s.trim())
          acc[name] = type.replace(/!$/, '') // Remove the non-null operator
          return acc
        }, {})
      : {}
  
    return {
      questionTypes,
      questionConfigInput,
      createQuestionInput,
    }
  }