import { db } from 'src/lib/db'
import { generateQuestions, generateTemplateBasedQuestions } from 'src/lib/aiService'
import { getQuestionTypesFromSDL } from 'src/services/surveys/questionTypesSDL'
import { createDefaultSettings } from 'src/services/surveys/settings'


export const createSurvey = async ({ input }) => {
    const { currentUser } = context
    const { isGeneratedByAi, ...surveyData } = input
  
    if (isGeneratedByAi) {
      try {
        // Get question types from SDL
        const questionTypes = getQuestionTypesFromSDL()
  
  
        // Get surveyId from AI service - pass accountId and questionTypes
        const surveyId = await generateQuestions({
          title: surveyData.title,
          audience: surveyData.audience,
          surveyObjective: surveyData.surveyObjective,
          additionalContext: surveyData.additionalContext,
          productName: surveyData.productName,
          accountId: currentUser.accountId,
          questionTypes,
        })
  
        // Create default settings
        await createDefaultSettings({ surveyId })

        // Return the survey with questions
        return db.survey.findUnique({
          where: { id: surveyId },
          include: { 
            questions: true,
          },
        })
      } catch (error) {
        console.log('Error generating AI survey:', error)
        throw new Error('Failed to generate AI survey')
      }
    }
  
    // Non-AI flow - create empty survey
    const survey = await db.survey.create({
      data: {
        ...surveyData,
        accountId: currentUser.accountId,
        source: 'CREATED',
        updatedAt: new Date(),
      },
      include: { 
        questions: true,
      },
    })

    // Create default settings
    await createDefaultSettings({ surveyId: survey.id })

    return survey
}


export const createSurveyFromTemplate = async ({ input }) => {
    const { currentUser } = context
    const { templateId, ...surveyData } = input
  
    // Get template questions
    const template = await db.surveyQLibraryCategory.findUnique({
      where: { id: templateId },
      include: {
        questions: {
          orderBy: {
            order: 'asc'
          }
        }
      }
    })
  
    if (!template) {
      throw new Error('Template not found')
    }
  
    // Create new survey with template questions
    const survey = await db.survey.create({
      data: {
        ...surveyData,
        accountId: currentUser.accountId,
        source: 'CREATED',
        questions: {
          create: template.questions.map((q) => ({
            title: q.title,
            type: q.type,
            required: false,
            order: q.order,
            pageNumber: 0,
            questionConfig: q.config || {
              choices: q.type === 'MULTIPLE_CHOICE' ? q.choices : [],
              minLength: 0,
              maxLength: 1000,
              rules: [],
              condition: 'all',
              actions: [],
              rating: q.type === 'RATING' ? Number(q?.choices[q.choices.length - 1]) : null, // Set rating if type is RATING
              scale: ['SCALE', 'SCORING'].includes(q.type) ? Number(q?.choices[q.choices.length - 1]) : null, // Set scale if type is SCALE
              rows: [],
              columns: [],
              cellType: '',
              allowMultiple: false,
              allowWriteIn: false,
            },
          }))
        }
      },
      include: { 
        questions: true,
      },
    })

    // Create default settings
    await createDefaultSettings({ surveyId: survey.id })

    return survey
}
  
export const createSurveyWithTemplate = async ({ input }) => {
    const { currentUser } = context
    const { templateId, ...surveyData } = input
  
    try {
      // Get question types from SDL
      const questionTypes = getQuestionTypesFromSDL()
  
      console.log('Creating template-based survey with:', {
        templateId,
        surveyData,
        questionTypes,
      })
  
      // Get surveyId from AI service
      const surveyId = await generateTemplateBasedQuestions({
        templateId,
        title: surveyData.title,
        audience: surveyData.audience,
        surveyObjective: surveyData.surveyObjective,
        additionalContext: surveyData.additionalContext,
        productName: surveyData.productName,
        accountId: currentUser.accountId,
        questionTypes, // Pass the question types
      })
  
      // Create default settings
      const settings = await createDefaultSettings({ surveyId })

      // Return the survey with questions
      return db.survey.findUnique({
        where: { id: surveyId },
        include: { 
          questions: true,
        },
      })
    } catch (error) {
      console.error('Error creating survey from template:', error)
      throw new Error(`Failed to create survey from template: ${error.message}`)
    }
}