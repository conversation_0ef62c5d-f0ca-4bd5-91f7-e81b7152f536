
import { db } from 'src/lib/db'
import { createId } from '@paralleldrive/cuid2';
import { readCSVFromGCS } from 'src/services/uploads/gcpUtils';
import { gradingQueue } from 'src/queue/index'

const _ = require('lodash')

async function insertQuestions(db, surveyId, headers) {
    // Extract question columns dynamically
    const questionColumns = headers.filter(
      (header) =>
        !['survey_id', 'participant_id'].includes(header) &&
        !header.endsWith('-response')
    )
  
    // Insert each question into the database
    const questionRecords = questionColumns.map((column, index) => {
      return {
        title: column,
        type: 'ESSAY',
        surveyId,
        order: index,
      }
    });
    const TempIds = [];
    // open bug : https://github.com/prisma/prisma/issues/8131 .createMany not return records created.
    const values = questionRecords.map(item => {
      const id = createId();
      TempIds.push(`'${id.replace(/'/g, "''")}'`);
      return `('${id.replace(/'/g, "''")}', '${item.title.replace(/'/g, "''")}', '${item.type.replace(/'/g, "''")}', '${item.surveyId.replace(/'/g, "''")}', ${item.order})`;
    }).join(', ');
    const query = `INSERT INTO public."Question" ("id","title", "type", "surveyId", "order") VALUES ${values};`;
    const executedResults = await db.$executeRawUnsafe(query);
    if (executedResults <= 0)
      throw new Error("Questions are not inserted properly. please check", query);
    const idValues = TempIds.join(', ');
    const promiseResults = await db.$queryRawUnsafe(`SELECT * FROM public."Question" WHERE id in (${idValues})`);
    const questionInsertionResults = new Map(promiseResults.map((res) => {
      return [res.title, res.id];
    }));
    return questionInsertionResults;
  }
  
  async function insertSurveyResponses(db, surveyId, data, questionTitleIdMap) {
    const finalSurveyResponseBulkObjects = [];
    const ipAddress =
      context?.event?.headers['x-forwarded-for'] ||
      context?.event?.requestContext?.identity?.sourceIp ||
      context?.event?.headers['x-real-ip'] ||
      context?.event?.headers['x-appengine-user-ip'] ||
      'IP address not found';
    for (const row of data) {
      if (_.isEmpty(row))
        continue;
      const questionResponses = [];
      const surveyResponseFields = {
        surveyId,
        participantEmail: '',
        participantId: row.participant_id,
        ipAddress,
        fingerprint: '',
        screenResolution: '',
        timezone: '',
        userAgent: '',
        submissionTime: new Date(),
        lastActiveTime: new Date(),
      }
      let questionNumber = 0
      for (const [header, value] of Object.entries(row)) {
        if (!header.endsWith('-response')) continue
        const title = _.first(header.split('-'))
        const questionId = questionTitleIdMap.get(title)
        questionResponses.push({
          questionId,
          questionNumber: ++questionNumber,
          response: value,
          timeToSubmit: 0,
          keystrokeTimingData: {},
          isResponseCopyPasted: false,
          submissionTime: new Date(),
        })
      }
      const finalSurveyResponseObject = {
        ...surveyResponseFields,
        questionResponses,
      }
      finalSurveyResponseBulkObjects.push(finalSurveyResponseObject)
    }
    const TempIds = [];
    const values = finalSurveyResponseBulkObjects.map(item => {
      const submissionTime = item.submissionTime.toISOString().slice(0, 19).replace('T', ' ');
      const lastActiveTime = item.lastActiveTime.toISOString().slice(0, 19).replace('T', ' ');
      const id = createId();
      TempIds.push(`'${id.replace(/'/g, "''")}'`);
      return `('${id.replace(/'/g, "''")}',
        '${item.surveyId.replace(/'/g, "''")}',
        '${item.participantEmail?.replace(/'/g, "''")}' ,
        '${item.participantId.replace(/'/g, "''")}',
        '${item.ipAddress?.replace(/'/g, "''")}',
        '${item.fingerprint?.replace(/'/g, "''")}',
        '${item.screenResolution?.replace(/'/g, "''")}',
        '${item.timezone?.replace(/'/g, "''")}',
        '${item.userAgent?.replace(/'/g, "''")}',
        '${JSON.stringify(item.questionResponses)?.replace(/'/g, "''")}'::jsonb,
        '${submissionTime}',
        '${lastActiveTime}'
      )`
    })
      .join(', ')
  
    const query = `INSERT INTO public."SurveyResponse" (
      "id",
      "surveyId",
      "participantEmail",
      "participantId",
      "ipAddress",
      "fingerprint",
      "screenResolution",
      "timezone",
      "userAgent",
      "questionResponses",
      "submissionTime",
      "lastActiveTime"
    ) VALUES ${values};`;
    const executedResults = await db.$executeRawUnsafe(query);
    if (executedResults <= 0)
      throw new Error("Survey Responses are not inserted properly. please check", query);
    const idValues = TempIds.join(', ');
    const promiseResults = await db.$queryRawUnsafe(`SELECT * FROM public."SurveyResponse" WHERE id in (${idValues})`);
    return promiseResults;
    /* Commenting below for now as update operation for these many is time heavy and mostly not needed.
   only catch is surveyResponseId wont be avail for question response but we can get that from surveyResponse table
   anyway
  
   promiseResults.map(async (promiseResult) => {
      if (promiseResult.questionResponses?.length > 0) {
        const questionResponses = promiseResult.questionResponses
        questionResponses[0].surveyResponseId = promiseResult.id
        await db.surveyResponse.update({
          where: { id: promiseResult.id },
          data: {
            questionResponses,
          },
        })
      }
    }) */
  }


export const createExternalUploadSurvey = async ({ input }) => {
    const { filePath, mappings } = input
    const { currentUser } = context
  
    try {
      const results = await readCSVFromGCS(filePath)
      if (results?.data.length > 0) {
        let createSurveyInput = {
          title: 'Imported Survey',
          additionalContext: 'Uploaded survey',
          source: 'IMPORTED',
          audience: 'IMPORTED',
          surveyObjective: 'IMPORTED',
          surveyExternalUploadFilePath: filePath,
          accountId: currentUser.accountId,
          status: 'COMPLETED',
        }
  
        // Start transaction
        const id = await db.$transaction(async (db) => {
          // Create Survey
          const survey = await db.survey.create({
            data: createSurveyInput,
          })
  
          const columnHeadersArray = Object.keys(results.data[0])
  
          // Insert Questions
          const questionRecords = Object.entries(mappings).map(
            ({ 0: columnName, 1: mapping }, index) => ({
              id: createId(),
              title: columnHeadersArray[index], // For qualtrix we shoudl pickf from row1 value instead row 0
              type: mapping.questionType,
              surveyId: survey.id,
              order: index,
            })
          )
  
          await db.question.createMany({
            data: questionRecords,
          })
  
          // Create a mapping from column name to question ID
          const questionTitleIdMap = new Map()
          questionRecords.forEach((question) => {
            questionTitleIdMap.set(question.title, question.id)
          })
  
          // Insert Survey Responses
          const surveyResponses = results.data.map((row) => {
            const questionResponses = []
            Object.entries(mappings).forEach(([columnName, mapping]) => {
              const questionId = questionTitleIdMap.get(columnName)
              questionResponses.push({
                questionId,
                response: row[columnName], // qualtrix we ll change this to index +1
              })
            })
            return {
              id: createId(),
              surveyId: survey.id,
              participantEmail: row['participant_email'] || null,
              participantId: row['participant_id'] || null,
              ipAddress: context?.event?.headers['x-forwarded-for'] || 'IP not found',
              submissionTime: new Date(),
              questionResponses,
              fingerprint: '',
              screenResolution: '',
              timezone: '',
              userAgent: '',
              lastActiveTime: new Date(),
            }
          })
  
          // Insert into SurveyResponse table
          // Adjust as needed based on your data model
          await db.surveyResponse.createMany({
            data: surveyResponses,
          });
          await addToEnRichSurveyResponseQueue(surveyResponses);
          return survey.id;
        });
  
        return { id };
      } else {
        /* retry mechanism or throw error to manually check what went wrong ? */
        throw new Error('No data found in CSV file.')
      }
    } catch (error) {
      console.error('Error processing CSV file:', error)
      throw error
    }
  }


async function addToEnRichSurveyResponseQueue(savedSurveyResponses) {
    savedSurveyResponses.map(async (surveyResponse) => {
      await gradingQueue.add('gradeSurveyResponse', {
        surveyResponseId: surveyResponse.id,
        surveyId: surveyResponse.surveyId,
      })
    });
}
  