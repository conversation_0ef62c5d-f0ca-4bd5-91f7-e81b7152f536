import {
  surveys,
  survey,
  create<PERSON><PERSON><PERSON>,
  update<PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON><PERSON>,
  createExternalUpload<PERSON><PERSON><PERSON>,
} from './surveys'

// Generated boilerplate tests do not account for all circumstances
// and can fail without adjustments, e.g. Float.
//           Please refer to the RedwoodJS Testing Docs:
//       https://redwoodjs.com/docs/testing#testing-services
// https://redwoodjs.com/docs/testing#jest-expect-type-considerations

describe('surveys', () => {
  /* scenario('returns all surveys', async (scenario) => {
    const result = await surveys()

    expect(result.length).toEqual(Object.keys(scenario.survey).length)
  })

  scenario('returns a single survey', async (scenario) => {
    const result = await survey({ id: scenario.survey.one.id })

    expect(result).toEqual(scenario.survey.one)
  }) */

  scenario('creates a survey', async (scenario) => {
    mockCurrentUser({ accountId: 'cj1x3i8sm0000e3n7xqk9j8a9', name: '<PERSON>' })
    createExternalUploadSurvey({ input: 'clzzpqt5w0002104aabijpmb7/clzzpqt4v0001104aqdee8fs6/uploads/1724991607696_t.csv' });
  })

  /*scenario('updates a survey', async (scenario) => {
    const original = await survey({ id: scenario.survey.one.id })
    const result = await updateSurvey({
      id: original.id,
      input: { title: 'String2' },
    })

    expect(result.title).toEqual('String2')
  })

  scenario('deletes a survey', async (scenario) => {
    const original = await deleteSurvey({
      id: scenario.survey.one.id,
    })
    const result = await survey({ id: original.id })

    expect(result).toEqual(null)
  }) */
})
