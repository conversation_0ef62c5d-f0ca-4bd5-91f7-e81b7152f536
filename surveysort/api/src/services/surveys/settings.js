import { db } from 'src/lib/db'
import { ForbiddenError } from '@redwoodjs/graphql-server'
import { removePrefix } from 'src/services/uploads/gcpUtils'


const checkSurveyAccess = async (surveyId, currentUser) => {
    const survey = await db.survey.findUnique({
      where: { id: surveyId, isDeleted: false },
      select: {
        accountId: true,
        account: { select: { name: true } },
      },
    })
  
    if (
      !survey ||
      (survey.accountId !== currentUser.accountId &&
        survey.account.name !== 'Demo Account')
    ) {
      throw new ForbiddenError("You don't have permission to access this survey.")
    }
  }
export const updateSurveySettings = async ({ surveyId, input }) => {
    const { currentUser } = context
    await checkSurveyAccess(surveyId, currentUser)
  
    const { randomizationGroups, themeConfig, ...otherSettings } = input
  
    if (otherSettings.previewImage?.length > 0) {
      otherSettings.previewImage = removePrefix(otherSettings.previewImage);
    }
  
    const result = await db.surveySettings.upsert({
      where: { surveyId },
      create: {
        ...otherSettings,
        surveyId,
        randomizationGroups: randomizationGroups ? JSON.stringify(randomizationGroups) : null,
        themeConfig: themeConfig ? JSON.stringify(themeConfig) : null
      },
      update: {
        ...otherSettings,
        randomizationGroups: randomizationGroups ? JSON.stringify(randomizationGroups) : null,
        themeConfig: themeConfig ? JSON.stringify(themeConfig) : null
      },
    })
  
    // Parse back into proper type for response
    if (result.randomizationGroups) {
      try {
        result.randomizationGroups = JSON.parse(result.randomizationGroups)
      } catch (e) {
        result.randomizationGroups = []
      }
    } else {
      result.randomizationGroups = []
    }
  
    // Parse themeConfig
    if (result.themeConfig) {
      try {
        result.themeConfig = JSON.parse(result.themeConfig)
      } catch (e) {
        result.themeConfig = null
      }
    }
    // parse the preview image
    if (result?.previewImage && result?.previewImage?.length > 0) {
      const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
      result.previewImage = `${process.env.GCP_STORAGE_URL}/${bucketName}/${result?.previewImage}`
    }
  
    if (result?.companyLogo && result?.companyLogo?.length > 0) {
      const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
      result.companyLogo = `${process.env.GCP_STORAGE_URL}/${bucketName}/${result?.companyLogo}`
    }
  
    return result
  }


export const surveySettings = async ({ surveyId }) => {
    const { currentUser } = context
    await checkSurveyAccess(surveyId, currentUser)
  
    // Get both survey and settings
    const survey = await db.survey.findUnique({
      where: { id: surveyId, isDeleted: false }
    })
  
    const settings = await db.surveySettings.findUnique({
      where: { surveyId },
    })
  
    if (!settings) {
      return null
    }
  
    // Parse JSON into proper type
    if (settings?.randomizationGroups) {
      try {
        settings.randomizationGroups = JSON.parse(settings.randomizationGroups)
      } catch (e) {
        settings.randomizationGroups = []
      }
    } else {
      settings.randomizationGroups = []
    }
  
    // Parse themeConfig
    if (settings?.themeConfig) {
      try {
        settings.themeConfig = JSON.parse(settings.themeConfig)
      } catch (e) {
        settings.themeConfig = null
      }
    }
    // parse the preview image
    if (settings?.previewImage && settings?.previewImage?.length > 0) {
      const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
      settings.previewImage = `${process.env.GCP_STORAGE_URL}/${bucketName}/${settings?.previewImage}`
    }
  
    // parse the preview image
    if (settings?.companyLogo && settings?.companyLogo?.length > 0) {
      const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
      settings.companyLogo = `${process.env.GCP_STORAGE_URL}/${bucketName}/${settings?.companyLogo}`
    }
  
    // Hydrate preview fields with survey data if not set
    return {
      ...settings,
      previewTitle: settings.previewTitle || survey.title,
      previewDescription: settings.previewDescription || survey.description
    }
  }

export const createDefaultSettings = async ({ surveyId }) => {
  const { currentUser } = context
  
  // Create default settings
  const surveySettings = await db.surveySettings.create({
    data: {
      surveyId,
      disclosure: null,
      customTheme: null,
      requireEmail: false,
      showProgressBar: true,
      showNavigation: true,
      companyLogo: null,
      removeBranding: false,
      randomizationGroups: null,
      themeConfig: JSON.stringify({ backgroundColor: '#fffbeb',
        questionTextColor: '#92400e',
        answerTextColor: '#d97706',
        buttonColor: '#b45309',
        progressBarColor: '#fbbf24'}),
      previewImage: null,
      previewTitle: null,
      previewDescription: null
    }
  })


  return surveySettings
}