import { ForbiddenError } from '@redwoodjs/graphql-server'
import { generateQuestions } from 'src/lib/aiService'
import { getQuestionTypesFromSDL } from 'src/services/surveys/questionTypesSDL'
import { db } from 'src/lib/db'

const _ = require('lodash')

const checkSurveyAccess = async (surveyId, currentUser) => {
  const survey = await db.survey.findUnique({
    where: { id: surveyId, isDeleted: false },
    select: {
      accountId: true,
      account: { select: { name: true } },
    },
  })

  if (
    !survey ||
    (survey.accountId !== currentUser.accountId &&
      survey.account.name !== 'Demo Account')
  ) {
    throw new ForbiddenError("You don't have permission to access this survey.")
  }
}

export const surveyDetail = async ({ id }) => {
  const { currentUser } = context

  let survey

  await checkSurveyAccess(id, currentUser)
  survey = await db.survey.findUnique({
    where: { id, accountId: currentUser.accountId, isDeleted: false },
    include: { account: { select: { name: true } } },
  })


  if (!survey) {
    throw new Error('Survey not found')
  }

  survey.createdBy = currentUser.email
  return survey
}




export const updateSurvey = async ({ id, input }) => {
  const { currentUser } = context
  await checkSurveyAccess(id, currentUser)

  const existingSurvey = await db.survey.findUnique({ where: { id, isDeleted: false } })

  if (input.status && input.status !== existingSurvey.status) {
    // Only update the status if it's changing
    return db.survey.update({
      where: { id },
      data: { status: input.status },
    })
  } else if (
    (input.audience && input.audience !== existingSurvey.audience) ||
    (input.surveyObjective && input.surveyObjective !== existingSurvey.surveyObjective)
  ) {
    // If audience or objective is changing, regenerate questions
    const questionTypesInfo = getQuestionTypesFromSDL()
    const generatedQuestions = await generateQuestions(
      {
        ...existingSurvey,
        ...input,
      },
      questionTypesInfo
    )

    return db.survey.update({
      where: { id },
      data: {
        ...input,
        questions: {
          deleteMany: {},
          create: generatedQuestions,
        },
      },
      include: { questions: true },
    })
  } else {
    // If no major changes, just update the survey details
    return db.survey.update({
      where: { id },
      data: input,
    })
  }
}

export const deleteSurvey = async ({ id }) => {
  const { currentUser } = context
  await checkSurveyAccess(id, currentUser)

  return db.survey.delete({
    where: { id },
  })
}

const SURVEYS_PER_PAGE = 5

export const surveyPage = async ({ page = 1, perPage = SURVEYS_PER_PAGE }) => {
  const { currentUser } = context
  const offset = (page - 1) * perPage

  const surveys = await db.survey.findMany({
    where: { accountId: currentUser.accountId, isDeleted: false },
    take: perPage,
    skip: offset,
    orderBy: { createdAt: 'desc' },
  })

  const count = await db.survey.count({
    where: { accountId: currentUser.accountId },
  })

  return {
    surveys,
    count,
  }
}

export const Survey = {
  account: (_obj, { root }) => {
    return db.survey.findUnique({ where: { id: root?.id, isDeleted: false } }).account()
  },
  questions: (_obj, { root }) => {
    return db.survey.findUnique({ where: { id: root?.id, isDeleted: false } }).questions()
  },
  settings: (_obj, { root }) => {
    return db.survey.findUnique({ where: { id: root?.id, isDeleted: false } }).settings()
  },
}

export const surveys = async ({ search }) => {
  const { currentUser } = context

  const surveysData = await db.survey.findMany({
    where: {
      AND: [
        { accountId: currentUser.accountId, isDeleted: false },
        search ? {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { surveyObjective: { contains: search, mode: 'insensitive' } },
          ],
        } : {},
      ]
    },
    include: {
      account: {
        select: { name: true }
      }
    },
    orderBy: { createdAt: 'desc' },
  })

  // Get responses for each survey
  const surveysWithStats = await Promise.all(
    surveysData.map(async (survey) => {
      // Get responses count and completion status
      const [responsesCount, responses] = await Promise.all([
        db.surveyResponse.count({
          where: { surveyId: survey.id }
        }),
        db.surveyResponse.findMany({
          where: { surveyId: survey.id },
          select: {
            isComplete: true
          }
        })
      ])

      const completedResponses = responses.filter(r => r.isComplete).length
      const completionRate = responsesCount > 0
        ? `${Math.round((completedResponses / responsesCount) * 100)}%`
        : '0%'

      // Add createdBy based on account type
      const createdBy = survey.account.name === 'Demo Account'
        ? 'Demo User'
        : currentUser.email

      // Remove account data before returning
      const { account, ...surveyWithoutAccount } = survey

      return {
        ...surveyWithoutAccount,
        totalResponses: responsesCount,
        completionRate,
        createdBy
      }
    })
  )

  return surveysWithStats
}

export const surveyTemplates = () => {
  return db.surveyQLibraryCategory.findMany({
    include: {
      questions: {
        orderBy: {
          order: 'asc'
        }
      }
    }
  })
}
