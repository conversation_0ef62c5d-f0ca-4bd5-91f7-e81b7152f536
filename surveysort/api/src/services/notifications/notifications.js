import { logger } from 'src/lib/logger'
import { notificationQueue } from 'src/queue/index'
import { db } from 'src/lib/db'
import { mailer } from 'src/lib/mailer'
import { ForbiddenError } from '@redwoodjs/graphql-server'
import { getSurveyResponseStats } from 'src/services/surveys/emailData'
import { EmailTemplateData, notificationTemplates, SlackTemplateData } from 'src/lib/notificationTemplates'
import { sendSlackNotification, validateWebhook } from 'src/lib/slackNotifications'

// Queue notification
export const queueNotification = async (rule, data) => {
  const templateConfig = notificationTemplates[rule.event]?.[rule.channel]
  
  if (!templateConfig) {
    throw new Error(`No template found for event ${rule.event} and channel ${rule.channel}`)
  }

  let message
  if (rule.channel === 'EMAIL') {
    message = rule.event === 'SURVEY_RESPONSE_RECEIVED' ?
      EmailTemplateData.formatForImmediate(data.surveyTitle, data.surveyId) :
      EmailTemplateData.formatForDigest(data.surveyTitle, data.responseStats, data.responses)
  } else {
    message = rule.event === 'SURVEY_RESPONSE_RECEIVED' ?
      SlackTemplateData.formatForImmediate(data.surveyTitle, data.responses) :
      SlackTemplateData.formatForDigest(data.surveyTitle, data.responseStats, data.responses)
  }

  await notificationQueue.add('sendNotification', {
    ruleId: rule.id,
    accountId: rule.accountId,
    userId: rule.userId,
    event: rule.event,
    channel: rule.channel,
    webhookUrl: rule.webhookUrl,
    emailAddress: rule.channel === 'EMAIL' ? rule.user?.email : null,
    message,
    templateComponent: rule.channel === 'EMAIL' ? templateConfig.Component : null
  })
}

const checkSurveyAccess = async (surveyId, currentUser) => {
  const survey = await db.survey.findUnique({
    where: { id: surveyId },
    select: {
      accountId: true,
      account: { select: { name: true } },
    },
  })

  if (
    !survey ||
    (survey.accountId !== currentUser.accountId &&
      survey.account.name !== 'Demo Account')
  ) {
    throw new ForbiddenError("You don't have permission to access this survey.")
  }

  return survey
}

// CRUD Operations
export const notificationRulesBySurveyId = async ({ surveyId }) => {
  const { currentUser } = context

  // Check survey access first
  await checkSurveyAccess(surveyId, currentUser)
  
  return db.notificationRule.findMany({
    where: { 
      surveyId,
      accountId: currentUser.accountId,
    },
    include: {
      user: true,
      account: true
    }
  })
}

export const createNotificationRule = async ({ input }) => {
  const { currentUser } = context
  
  // Check survey access before creating rule
  await checkSurveyAccess(input.surveyId, currentUser)

  return db.notificationRule.create({
    data: {
      ...input,
      accountId: currentUser.accountId,
      userId: currentUser.id,
      isActive: input.isActive ?? true,
    }
  })
}

export const updateNotificationRule = async ({ id, input }) => {
  const { currentUser } = context

  // Verify rule belongs to account
  const existingRule = await db.notificationRule.findFirst({
    where: { 
      id,
      accountId: currentUser.accountId
    },
    include: {
      survey: true
    }
  })

  if (!existingRule) {
    throw new Error('Notification rule not found')
  }

  // Check survey access
  await checkSurveyAccess(existingRule.survey.id, currentUser)

  return db.notificationRule.update({
    where: { id },
    data: {
      ...input,
      updatedAt: new Date()
    }
  })
}

export const deleteNotificationRule = async ({ id }) => {
  const { currentUser } = context

  // Verify rule belongs to account
  const existingRule = await db.notificationRule.findFirst({
    where: { 
      id,
      accountId: currentUser.accountId
    }
  })

  if (!existingRule) {
    throw new Error('Notification rule not found')
  }

  return db.notificationRule.delete({
    where: { id }
  })
}

export const testNotificationRule = async ({ id }) => {
  const rule = await db.notificationRule.findUnique({
    where: { id },
    include: { survey: true }
  })

  if (!rule) {
    throw new Error('Notification rule not found')
  }

//   await queueNotification(rule, {
//     surveyTitle: rule.survey.title,
//     surveyId: rule.survey.id
//   })
  await sendDailyDigest(rule.survey.id, rule.survey.title)
  return true
}

// Core notification sending logic
export const sendNotificationMessage = async ({ 
  ruleId, 
  accountId,
  userId,
  event, 
  surveyTitle, 
  message, 
  channel, 
  webhookUrl, 
  emailAddress 
}) => {
  try {
    if (channel === 'WEBHOOK') {
      await sendSlackNotification({ event, surveyTitle, message, webhookUrl })
    } else {
      await sendEmailNotification({ event, surveyTitle, message, emailAddress })
    }

    // Create notification record with account context
    await db.notification.create({
      data: {
        ruleId,
        accountId,
        userId,
        status: 'SENT',
        sentAt: new Date()
      }
    })

    return true
  } catch (error) {
    logger.error('Failed to send notification', { ruleId, channel, error })
    
    // Create failed notification record
    await db.notification.create({
      data: {
        ruleId,
        accountId,
        userId,
        status: 'FAILED',
        error: error.message,
        retryCount: 1
      }
    })

    throw error
  }
}

// Email specific sending with proper data formatting
const sendEmailNotification = async ({ event, emailAddress, message, templateComponent: Template }) => {
  if (!Template) {
    throw new Error('No email template component provided')
  }

  await mailer.send(Template(message), {
    to: emailAddress,
    subject: notificationTemplates[event].EMAIL.subject,
    from: '<EMAIL>'
  })
}

// Core business logic
export const processNotificationJob = async (job) => {
  const { ruleId, event, surveyTitle, message, channel, webhookUrl, emailAddress } = job.data

  try {
    // Process notification based on channel
    if (channel === 'EMAIL') {
      await sendEmailNotification({
        event,
        surveyTitle,
        message,
        emailAddress
      })
    }

    // Record success
    await db.notification.create({
      data: {
        ruleId,
        status: 'SENT',
        sentAt: new Date()
      }
    })

    return true
  } catch (error) {
    logger.error('Failed to process notification', { 
      ruleId, 
      error: error.message 
    })

    // Record failure
    await db.notification.create({
      data: {
        ruleId,
        status: 'FAILED',
        error: error.message
      }
    })

    throw error
  }
}

// Add new function
export const processImmediateNotifications = async (surveyResponse) => {
  // Find active immediate notification rules
  const notificationRules = await db.notificationRule.findMany({
    where: {
      surveyId: surveyResponse.surveyId,
      isActive: true,
      frequency: 'IMMEDIATE'
    },
    include: {
      survey: true,
      user: true
    }
  })

  // Queue notifications for each rule
  for (const rule of notificationRules) {
    await queueNotification(rule, {
      surveyTitle: rule.survey.title,
      responses: surveyResponse
    })
  }
}

// Core notification sending logic
export const sendImmediateNotification = async (rule) => {
  if (rule.channel === 'EMAIL') {
    const emailData = EmailTemplateData.formatForImmediate(
      rule.survey.title,
      rule.survey.id
    )
    
    const Template = notificationTemplates[rule.event].EMAIL.Component
    
    await mailer.send(Template(emailData), {
      to: rule.user.email,
      subject: notificationTemplates[rule.event].EMAIL.subject,
      from: '<EMAIL>'
    })
  } else {
    await sendSlackNotification({
      event: rule.event,
      surveyTitle: rule.survey.title,
      message: `New response received for ${rule.survey.title}`,
      webhookUrl: rule.webhookUrl
    })
  }
}

export const sendDailyDigest = async (surveyId, surveyTitle) => {
  const rules = await db.notificationRule.findMany({
    where: {
      surveyId,
      isActive: true,
      frequency: 'DAILY'
    },
    include: { user: true, survey: true }
  })

  const responses = await db.surveyResponse.findMany({
    where: {
      surveyId,
      createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }
  })

  const responseStats = await getSurveyResponseStats(surveyId, 24 * 60 * 60 * 1000)

  for (const rule of rules) {
    await sendDigestNotification(rule, responses, responseStats, 'DAILY')
  }
}

export const sendWeeklyDigest = async (surveyId, surveyTitle) => {
  const rules = await db.notificationRule.findMany({
    where: {
      surveyId,
      isActive: true,
      frequency: 'WEEKLY'
    },
    include: { user: true, survey: true }
  })

  const responses = await db.surveyResponse.findMany({
    where: {
      surveyId,
      createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    }
  })

  const responseStats = await getSurveyResponseStats(surveyId, 7 * 24 * 60 * 60 * 1000)

  for (const rule of rules) {
    await sendDigestNotification(rule, responses, responseStats, 'WEEKLY')
  }
}

// Helper function for sending digest notifications
const sendDigestNotification = async (rule, responses, responseStats, frequency) => {
  const eventType = `SURVEY_RESPONSE_${frequency}_DIGEST`
  
  if (rule.channel === 'EMAIL') {
    const emailData = EmailTemplateData.formatForDigest(
      rule.survey.title, 
      responseStats, 
      responses
    )
    const Template = notificationTemplates[eventType].EMAIL.Component
    
    await mailer.send(Template(emailData), {
      to: rule.user.email,
      subject: notificationTemplates[eventType].EMAIL.subject,
      from: '<EMAIL>'
    })
  } else {
    await sendSlackNotification(rule.webhookUrl, {
      event: eventType,
      surveyTitle: rule.survey.title,
      responses,
      responseStats
    })
  }
}

// Add this function
export const getNotificationRule = async (ruleId) => {
  const rule = await db.notificationRule.findUnique({
    where: { id: ruleId },
    include: { 
      user: true, 
      survey: true,
      account: true 
    }
  })

  if (!rule) {
    throw new Error(`Notification rule ${ruleId} not found`)
  }

  return rule
} 