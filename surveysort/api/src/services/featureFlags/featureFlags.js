import { getAllFeatureFlags } from 'src/lib/posthog'
import { isWhitelisted } from 'src/lib/whitelist'
import { logger } from 'src/lib/logger'

export const getFlags = async ({ flagKeys }) => {
  logger.info('Checking feature flags', { flagKeys })
  
  const { currentUser } = context
  
  if (!currentUser?.id) {
    logger.debug('No user in context, flags disabled', { flagKeys })
    return flagKeys.map(key => ({
      key,
      enabled: false
    }))
  }

  // Whitelisted users get all features enabled
  if (isWhitelisted(currentUser.email)) {
    logger.info('User is whitelisted, enabling all flags', { 
      userId: currentUser.id,
      email: currentUser.email 
    })
    return flagKeys.map(key => ({
      key,
      enabled: true
    }))
  }

  try {
    const properties = {
      email: currentUser.email,
      accountId: currentUser.accountId,
      isVerified: currentUser.isVerified,
      roles: currentUser.roles,
      stripePaidStatus: currentUser.stripePaidStatus,
    }

    const allFlags = await getAllFeatureFlags(currentUser.id, properties)
    
    const results = flagKeys.map(key => ({
      key,
      enabled: allFlags[key] ?? false
    }))
    
    logger.info('Feature flags check results', { 
      flagKeys,
      userId: currentUser.id,
      results 
    })

    return results
  } catch (error) {
    logger.error('Error checking feature flags', {
      flagKeys,
      userId: currentUser?.id,
      error: error.message,
      stack: error.stack
    })
    
    return flagKeys.map(key => ({
      key,
      enabled: false
    }))
  }
} 