import { context } from '@redwoodjs/graphql-server'

import { db } from 'src/lib/db'
import { createLink } from 'src/services/participantLinks/participantLinks'

import { readCSVFromGCS, upload } from '../uploads/gcpUtils'

/**
 * Upserts a list of participants from CSV or JSON
 */
export const upsertParticipantList = async ({ input }) => {
  const { surveyId, data, gcpFilePath, skipDuplicates = true } = input

  const survey = await db.survey.findFirst({
    where: { id: surveyId, accountId: context.currentUser.accountId },
  })

  if (!survey) throw new Error('Survey not found')

  let rows

  if (gcpFilePath) {
    // Read from GCP file
    const result = await readCSVFromGCS(gcpFilePath)
    rows = result.data
  } else if (data) {
    // Parse CSV data directly (legacy support)
    rows = parseCSVData(data)
  } else {
    throw new Error('Either data or gcpFilePath must be provided')
  }

  // Get suppression list

  const suppressedEmails = new Set(
    (
      await db.suppressionEntry.findMany({
        where: { accountId: context.currentUser.accountId },

        select: { email: true },
      })
    ).map((entry) => entry.email.toLowerCase())
  )

  const report = {
    totalRows: rows.length,
    created: 0,
    updated: 0,
    skipped: 0,
    suppressedCount: 0,
    errors: [],
  }

  for (const [index, row] of rows.entries()) {
    try {
      const email = row.email?.trim().toLowerCase()

      if (!email || !isValidEmail(email)) {
        report.errors.push(`Row ${index + 1}: Invalid email`)

        continue
      }

      if (suppressedEmails.has(email)) {
        report.suppressedCount++

        report.skipped++

        continue
      }

      const existingParticipant = await db.surveyParticipant.findUnique({
        where: { surveyId_email: { surveyId, email } },
      })

      if (existingParticipant) {
        if (!skipDuplicates) {
          await db.surveyParticipant.update({
            where: { surveyId_email: { surveyId, email } },
            data: {
              name: row.name?.trim(),
              props: extractCustomProps(row),
            },
          })
          report.updated++
        } else {
          report.skipped++
        }
      } else {
        await db.surveyParticipant.create({
          data: {
            surveyId,
            email,
            name: row.name?.trim(),
            props: extractCustomProps(row),
          },
        })
        report.created++
      }
    } catch (error) {
      report.errors.push(`Row ${index + 1}: ${error.message}`)
    }
  }

  return {
    created: report.created,
    updated: report.updated,
    skipped: report.skipped,
  }
}

/**
 * Generates links for participants
 */
export const generateLinks = async ({ surveyId, segment = {} }) => {
  // Find participants that match the segment
  const participants = await db.surveyParticipant.findMany({
    where: {
      surveyId,
      ...(segment.status ? { status: segment.status } : {}),
    },
    select: { id: true },
  })

  let count = 0

  // Create links for each participant
  for (const participant of participants) {
    await createLink({ participantId: participant.id })
    count++
  }

  return count
}

/**
 * Lists participants with pagination and filtering
 */
export const listParticipants = async ({
  surveyId,
  page = 1,
  perPage = 20,
  filter = {},
}) => {
  try {
    // Build where clause
    const where = {
      surveyId,
      ...(filter.status ? { status: filter.status } : {}),
      ...(filter.search
        ? {
            OR: [
              { email: { contains: filter.search, mode: 'insensitive' } },
              { name: { contains: filter.search, mode: 'insensitive' } },
            ],
          }
        : {}),
    }

    // Get total count
    const count = (await db.surveyParticipant.count({ where })) || 0

    // Get participants
    const participants =
      (await db.surveyParticipant.findMany({
        where,
        include: {
          links: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: { email: 'asc' },
        skip: (page - 1) * perPage,
        take: perPage,
      })) || []

    return {
      participants,
      count,
      hasMore: page * perPage < count,
    }
  } catch (error) {
    console.error('Error in listParticipants:', error)
    // Return empty results instead of throwing to prevent breaking the UI
    return {
      participants: [],
      count: 0,
      hasMore: false,
    }
  }
}

/**
 * Queues email sending for participants
 */
export const sendSurveyEmails = async ({ input }) => {
  const { surveyId, participantIds } = input

  // TODO: Implement actual email sending logic using a queue
  // This is a placeholder implementation

  // For participants who haven't been sent an email yet, we need to set firstSentAt
  const now = new Date()

  // Mark participants as SENT
  await db.surveyParticipant.updateMany({
    where: {
      id: { in: participantIds },
      surveyId,
    },
    data: {
      status: 'SENT',
      lastSentAt: now,
      firstSentAt: {
        set: now,
      },
    },
  })

  return true
}

/**
 * Gets participation stats for a survey
 */
export const participantStats = async ({ surveyId }) => {
  const stats = await db.$queryRaw`
    SELECT
     CAST(COUNT(*) AS integer) AS total,
    CAST(COALESCE(SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END), 0) AS integer) as pending,
    CAST(COALESCE(SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END), 0) AS integer) as sent,
    CAST(COALESCE(SUM(CASE WHEN status = 'CLICKED' THEN 1 ELSE 0 END), 0) AS integer) as clicked,
    CAST(COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END), 0) AS integer) as completed,
    CAST(COALESCE(SUM(CASE WHEN status = 'BOUNCED' THEN 1 ELSE 0 END), 0) AS integer) as bounced,
    CAST(COALESCE(SUM(CASE WHEN status = 'UNSUBSCRIBED' THEN 1 ELSE 0 END), 0) AS integer) as unsubscribed

    FROM "SurveyParticipant"
    WHERE "surveyId" = ${surveyId}
  `
  return stats[0]
}

/**
 * Deletes a participant
 */
export const deleteParticipant = async ({ id }) => {
  // Delete the participant's links first
  await db.participantLink.deleteMany({
    where: { participantId: id },
  })

  // Delete the participant
  return db.surveyParticipant.delete({
    where: { id },
  })
}

export const generateLinksForSurvey = async ({ surveyId }) => {
  const participants = await db.surveyParticipant.findMany({
    where: {
      surveyId,

      status: 'PENDING',

      links: { none: { expiresAt: { gt: new Date() } } },
    },
  })

  let generated = 0

  for (const participant of participants) {
    await createLink({ participantId: participant.id })

    generated++
  }

  return { generated, total: participants.length }
}

export const exportParticipantLinks = async ({ surveyId }) => {
  const participants = await db.surveyParticipant.findMany({
    where: { surveyId },

    include: {
      links: {
        where: { expiresAt: { gt: new Date() } },

        orderBy: { createdAt: 'desc' },

        take: 1,
      },
    },
  })

  const exportData = participants.map((p) => ({
    name: p.name || '',

    email: p.email,

    survey_link: p.links[0]
      ? `${process.env.WEB_URL}/s/${p.links[0].token}`
      : 'NO_LINK',

    status: p.status,
  }))

  const gcpUrl = await upload(exportData, 'participant-exports', 'csv')

  return { downloadUrl: gcpUrl, count: exportData.length }
}

const isValidEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)

const extractCustomProps = (row) =>
  Object.keys(row)
    .filter((key) => !['email', 'name'].includes(key))
    .reduce((acc, key) => ({ ...acc, [key]: row[key] }), {})

const parseCSVData = (csvData) => {
  const lines = csvData.trim().split('\n')
  if (lines.length < 2) return []

  const headers = lines[0].split(',').map((h) => h.trim())
  const rows = []

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map((v) => v.trim())
    const row = {}
    headers.forEach((header, index) => {
      row[header] = values[index] || ''
    })
    rows.push(row)
  }

  return rows
}
