import { db } from 'src/lib/db'
import { requireAuth } from 'src/lib/auth'
import { logger } from 'src/lib/logger'
import stripe from 'src/lib/stripe'
import { mailer } from 'src/lib/mailer'
import { WelcomeEmail } from 'src/mail/Welcome/Welcome'
import { VerificationEmail } from 'src/mail/Verification/Verification'
import { transactionalQueue } from 'src/queue/index'
import { PropKey, updateUserPropsByUserId } from 'src/services/userProps'
import { PasswordResetEmail } from 'src/mail/PasswordReset/PasswordReset'
import { generateToken } from 'src/lib/auth'
import client from 'src/lib/posthog'
import { isWhitelisted } from 'src/lib/whitelist'
import { billing } from 'src/services/billing/billing'

export const currentAccount = async () => {
  logger.info('Fetching current account')
  const { currentUser } = context
  
  if (!currentUser?.accountId) {
    logger.error('No account associated with user', { userId: currentUser?.id })
    throw new Error('No account associated with user')
  }

  try {
    const account = await db.account.findUnique({
      where: { id: currentUser.accountId },
      select: {
        id: true,
        checkoutInProgress: true,
        stripeCustomerId: true,
      },
    })
    logger.info('Successfully fetched account', { accountId: account.id })
    return account
  } catch (error) {
    logger.error('Error fetching account', { error, userId: currentUser?.id })
    throw error
  }
}

export const getPaidAccountsWithUsage = async () => {
  // Get accounts with active subscriptions
  const activeSubscriptions = await stripe.subscriptions.list({
    status: 'active',
    expand: ['data.customer']
  })

  // Filter to only paid (not canceled) subscriptions
  const paidCustomerIds = activeSubscriptions.data
    .filter(sub => !sub.cancel_at_period_end)
    .map(sub => sub.customer.id)

  if (paidCustomerIds.length === 0) return []

  // Get corresponding accounts
  return await db.account.findMany({
    where: {
      stripeCustomerId: { in: paidCustomerIds },
      isDeleted: false
    },
    include: {
      surveys: true // Include what's needed for usage calculation
    }
  })
}

export const deprovisionUserAccount = async ({ id }) => {
  return await db.$transaction(async (db) => {
    // First, find the user ID associated with the provided id
    const user = await db.user.findUnique({
      where: { id: id, isDeleted: false },
    })

    if (!user) {
      throw new Error('User not found.')
    }

    // Delete related Invitations where the user is the sender by setting isDeleted to true
    await db.invitation.updateMany({
      where: { senderId: user.id },
      data: { isDeleted: true },
    })

    const relatedUserRoles = await db.userRole.findMany({
      where: { userId: user.id, isDeleted: false },
      select: { accountId: true, roleId: true },
    })

    const adminRole = await db.role.findUnique({
      where: { name: 'ADMIN' },
    })

    const stripeCustomerIds = new Set()

    if (relatedUserRoles.some((ur) => ur.roleId === adminRole.id)) {
      // If the user is an admin, delete all users and roles in the related accounts
      for (const { accountId } of relatedUserRoles) {
        // Set isDeleted to true for all user roles in the account
        await db.userRole.updateMany({
          where: { accountId: accountId },
          data: { isDeleted: true },
        })

        // Set isDeleted to true for all users in the account
        await db.user.updateMany({
          where: { userRoles: { some: { accountId: accountId } } },
          data: { isDeleted: true },
        })

        const account = await db.account.findUnique({
          where: { id: accountId, isDeleted: false },
          select: { stripeCustomerId: true },
        })
        if (account.stripeCustomerId) {
          stripeCustomerIds.add(account.stripeCustomerId)
        }

        // Set isDeleted to true for the account itself
        await db.account.update({
          where: { id: accountId },
          data: { isDeleted: true },
        })
      }
    } else {
      // If the user is not an admin, set isDeleted to true for their user role
      await db.userRole.updateMany({
        where: { userId: user.id },
        data: { isDeleted: true },
      })
    }

    for (const customerId of stripeCustomerIds) {
      try {
        await stripe.customers.update(customerId, {
          metadata: { deleted_in_app: 'true' },
        })
      } catch (error) {
        console.error(error)
      }
    }

    // Finally, set isDeleted to true for the User itself
    await db.user.update({
      where: { id: user.id },
      data: { isDeleted: true },
    })
  })
}

export const createUserWithAccount = async ({ email, hashedPassword, salt }) => {
  logger.info('Starting user and account creation', { email })
  
  // First, run the database transaction
  const newUser = await db.$transaction(async (db) => {
    try {
      // Create a new account
      logger.debug('Creating new account')
      const newAccount = await db.account.create({
        data: {
          name: `${email}'s Account`,
        },
      })
      logger.info('Created new account', { accountId: newAccount.id })

      // Get the ADMIN role first
      logger.debug('Fetching ADMIN role')
      const adminRole = await db.role.findUnique({
        where: { name: 'ADMIN' },
      })

      if (!adminRole) {
        logger.error('ADMIN role not found')
        throw new Error('ADMIN role not found')
      }

      // Create the user with all required fields AND initialize props
      logger.debug('Creating new user')
      const user = await db.user.create({
        data: {
          email,
          hashedPassword,
          salt,
          props: {}, // Initialize empty props object
          userRoles: {
            create: {
              roleId: adminRole.id,
              accountId: newAccount.id,
              isDeleted: false
            }
          }
        },
        include: {
          userRoles: true
        }
      })
      logger.info('Created new user', { userId: user.id })

      // Identify user in PostHog before creating Stripe customer
      try {
        await client.identify({
          distinctId: user.id,
          properties: {
            email: user.email,
            accountId: newAccount.id,
            isVerified: false,
            roles: ['ADMIN'],
            stripePaidStatus: false,
            onboardingComplete: false,
            isAdmin: true,
            $initial_referrer: '$direct',
            $initial_referring_domain: '$direct'
          }
        })
        logger.info('Identified user in PostHog', { userId: user.id })
      } catch (posthogError) {
        logger.error('Error identifying user in PostHog', { 
          error: posthogError,
          userId: user.id 
        })
        // Continue despite PostHog error
      }

      // Create Stripe customer
      logger.debug('Creating Stripe customer')
      try {
        const stripeCustomer = await stripe.customers.create({
          email: user.email,
          metadata: {
            accountId: newAccount.id,
            converted_to_paid: 'false',
          },
        })
        logger.info('Created Stripe customer', { 
          stripeCustomerId: stripeCustomer.id,
          accountId: newAccount.id 
        })

        // Update account with Stripe customer ID
        await db.account.update({
          where: { id: newAccount.id },
          data: { stripeCustomerId: stripeCustomer.id },
        })
      } catch (stripeError) {
        logger.error('Error creating Stripe customer', { 
          error: stripeError,
          accountId: newAccount.id,
          userId: user.id 
        })
        // Continue without Stripe - we can handle this later
      }
      logger.info(`accnt_creation_success, ${user.id}`)
      return user
    } catch (error) {
      logger.error('Error in createUserWithAccount transaction', {
        error,
        email
      })
      throw error
    }
  })

  // After transaction completes successfully, handle non-transactional operations
  try {
    // Set up user properties
    logger.debug('Setting up user properties')
    await updateUserPropsByUserId(newUser.id, {
      [PropKey.preferredName]: '',
      [PropKey.title]: '',
      [PropKey.organization]: '',
      [PropKey.surveysLastMonth]: 0,
      [PropKey.hoursCleaningResponses]: 0,
      [PropKey.onboardingDone]: false,
    })

    // Queue welcome email
    logger.debug('Queueing welcome email')
    await transactionalQueue.add('companySendsWelcomeEmail', {
      userId: newUser.id,
      emailAddress: newUser.email,
    })

    logger.info('Successfully completed post-transaction operations', {
      userId: newUser.id
    })
  } catch (error) {
    logger.error('Error in post-transaction operations', {
      error,
      userId: newUser.id
    })
    // Don't throw here - user is already created
  }
  logger.info('Successfully completed post-transaction operations', {
    userId: newUser.id
  })
  return newUser
}

export const normalizeEmail = (email) => {
  const [localPart, domain] = email.toLowerCase().split('@')
  // Remove everything after + in local part
  const normalizedLocalPart = localPart.split('+')[0]
  return `${normalizedLocalPart}@${domain}`
}

export const isPublicEmail = (email) => {
  const normalizedEmail = normalizeEmail(email)
  const blockedDomains = process.env.BLOCKED_DOMAINS?.trim().split(',') || []
  const domain = normalizedEmail.split('@')[1]
  return blockedDomains.includes(domain)
}

export const isEmailAllowed = (email) => {
  logger.debug('Checking if email is allowed', { email })
  
  const normalizedEmail = normalizeEmail(email)
  
  // First check whitelist
  if (isWhitelisted(normalizedEmail)) {
    logger.info('Email is whitelisted', { email, normalizedEmail })
    return true
  }

  // Only check for public email if not whitelisted
  const isPublic = isPublicEmail(email)
  logger.info('Email domain check result', { email, normalizedEmail, isAllowed: !isPublic })
  return !isPublic
}

export const sendVerificationEmail = async ({ emailAddress, verificationUrl }) => {
  return await mailer.send(
    VerificationEmail({
      verificationUrl,
    }),
    {
      to: emailAddress,
      subject: 'Verify your SurveySort email address',
      from: process.env.NO_REPLY_FROM_ADDRESS || '<EMAIL>',
    }
  )
}

export const sendWelcomeEmail = async ({ emailAddress }) => {
  return await mailer.send(
    WelcomeEmail(),
    {
      to: emailAddress,
      subject: 'Welcome to SurveySort!',
      from: process.env.NO_REPLY_FROM_ADDRESS || '<EMAIL>',
    }
  )
}

export const getPlans = async () => {
  const stripeProducts = await stripe.products.list({
    active: true,
  })

  let currentSubscription = null
  if (context.currentUser && context.currentUser.accountId) {
    const account = await db.account.findUnique({
      where: { id: context.currentUser.accountId },
      select: {
        stripeCustomerId: true,
      },
    })

    if (account.stripeCustomerId) {
      const subscriptions = await stripe.subscriptions.list({
        customer: account.stripeCustomerId,
        status: 'active',
        expand: ['data.items.data.price'],
      })
      if (subscriptions.data.length > 0) {
        currentSubscription = subscriptions.data[0]
      }
    }
  }

  const planOrder = ['Individual Researcher', 'Research Teams', 'Enterprise']

  const plans = await Promise.all(
    stripeProducts.data
      .sort((a, b) => planOrder.indexOf(a.name) - planOrder.indexOf(b.name))
      .map(async (product) => {
        const prices = await stripe.prices.list({
          product: product.id,
          active: true,
        })

        if (prices.data.length === 0) {
          return [
            {
              priceId: null,
              productId: product.id,
              name: product.name,
              description: product.description || '',
              price: null,
              interval: 'custom',
              currency: null,
              features:
                product.metadata?.features
                  ?.split(',')
                  .map((feature) => feature.trim()) || [],
              isCurrent: false,
              discount: 0,
            },
          ]
        }

        return prices.data.map((price) => {
          const isYearly = price.recurring?.interval === 'year'
          const monthlyPrice = isYearly
            ? price.unit_amount / 100 / 12
            : price.unit_amount / 100

          return {
            priceId: price.id,
            productId: product.id,
            name: product.name,
            description: product.description || '',
            price: monthlyPrice,
            interval: price.recurring?.interval || 'custom',
            currency: price.currency,
            features:
              product.metadata?.features
                ?.split(',')
                .map((feature) => feature.trim()) || [],
            isCurrent:
              currentSubscription?.items.data.some(
                (item) => item.price.id === price.id
              ) || false,
            discount: isYearly ? 30 : 0,
          }
        })
      })
  )

  return plans.flat().filter((plan) => plan !== null)
}

export const createCheckoutSession = async ({ priceId }) => {
  const { currentUser } = context
  const account = await db.account.findUnique({
    where: { id: currentUser.accountId },
  })

  // Get the base price and its metered components
  const price = await stripe.prices.retrieve(priceId, {
    expand: ['product']
  })

  const meteredPrices = await stripe.prices.list({
    product: price.product.id,
    type: 'recurring',
    recurring: { usage_type: 'metered' }
  })

  // Add all prices to line items
  const lineItems = [
    { price: priceId, quantity: 1 },
    ...meteredPrices.data.map(price => ({
      price: price.id,
      quantity: 1
    }))
  ]

  const session = await stripe.checkout.sessions.create({
    mode: 'subscription',
    line_items: lineItems,
    success_url: `${process.env.FE_URL}/subscription-success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.FE_URL}/subscription-canceled`,
    customer: account.stripeCustomerId,
    client_reference_id: account.id,
    subscription_data: {
      trial_from_plan: true // Use trial period defined in the price
    }
  })

  return { sessionId: session.id, url: session.url }
}

export const verifyUserEmail = async ({ token }) => {
  const user = await db.user.findFirst({
    where: {
      verificationToken: token,
      verificationTokenExpiresAt: {
        gt: new Date(),
      },
      isVerified: false,
    },
  })

  if (!user) {
    logger.error('Invalid or expired verification token', { token })
    throw new Error('Invalid or expired verification token')
  }

  try {
    // Update user verification status
    await db.user.update({
      where: { id: user.id },
      data: {
        isVerified: true,
        verificationToken: null,
        verificationTokenExpiresAt: null,
      },
    })

    // Set up user properties
    logger.debug('Setting up user properties')
    await updateUserPropsByUserId(user.id, {
      [PropKey.preferredName]: '',
      [PropKey.title]: '',
      [PropKey.organization]: '',
      [PropKey.surveysLastMonth]: 0,
      [PropKey.hoursCleaningResponses]: 0,
      [PropKey.onboardingDone]: false,
    })

    // Queue welcome email
    await transactionalQueue.add('companySendsWelcomeEmail', {
      userId: user.id,
      emailAddress: user.email,
    })

    logger.info('Email verified successfully', { userId: user.id })
    return { message: 'Email verified successfully' }
  } catch (error) {
    logger.error('Error verifying email', { error, userId: user.id })
    throw error
  }
}

export const createUnverifiedUser = async ({ email, hashedPassword, salt, verificationToken, verificationTokenExpiresAt }) => {
  logger.info('Creating unverified user account', { email })
  
  return await db.$transaction(async (db) => {
    try {
      // Create account and user as before
      const newAccount = await db.account.create({
        data: {
          name: `${email}'s Account`,
        },
      })

      const adminRole = await db.role.findUnique({
        where: { name: 'ADMIN' },
      })

      const user = await db.user.create({
        data: {
          email,
          hashedPassword,
          salt,
          verificationToken,
          verificationTokenExpiresAt,
          isVerified: false,
          props: {},
          userRoles: {
            create: {
              roleId: adminRole.id,
              accountId: newAccount.id,
              isDeleted: false
            }
          }
        }
      })

      // Try to create trial subscription if billing service is available
      try {
        if (billing?.createTrialSubscription) {
          await billing.createTrialSubscription(newAccount.id, email)
        } else {
          logger.warn('Billing service not available - skipping trial subscription creation', {
            accountId: newAccount.id,
            email
          })
        }
      } catch (billingError) {
        logger.error('Error creating trial subscription', {
          error: billingError,
          accountId: newAccount.id,
          email
        })
        // Continue without trial subscription - can be handled later
      }
      
      return user
    } catch (error) {
      logger.error('Error in createUnverifiedUser', { error, email })
      throw error
    }
  })
}

// Add GraphQL mutation resolver
export const verifyEmail = async ({ token }) => {
  try {
    const result = await verifyUserEmail({ token })
    return { message: result.message }
  } catch (error) {
    return { error: error.message }
  }
}

export const sendPasswordResetEmail = async ({ emailAddress, resetUrl }) => {
  return await mailer.send(
    PasswordResetEmail({
      resetUrl,
    }),
    {
      to: emailAddress,
      subject: 'Reset your SurveySort password',
      from: process.env.NO_REPLY_FROM_ADDRESS || '<EMAIL>',
    }
  )
}

export const validateResetToken = async ({ token }) => {
  const user = await db.user.findFirst({
    where: {
      resetToken: token,
      resetTokenExpiresAt: {
        gt: new Date(),
      },
    },
  })

  if (!user) {
    logger.error('Invalid or expired reset token', { token })
    throw new Error('Invalid or expired reset token')
  }

  return user
}

export const updateUserResetToken = async ({ userId, resetToken, expiresIn = 24 }) => {
  const resetTokenExpiresAt = new Date(Date.now() + 1000 * 60 * 60 * expiresIn) // Default 24 hours

  return await db.user.update({
    where: { id: userId },
    data: {
      resetToken,
      resetTokenExpiresAt,
    },
  })
}

export const isAccountPaid = async (accountId) => {
  const account = await db.account.findUnique({
    where: { id: accountId }
  })

  if (!account?.stripeCustomerId) return false

  const subscriptions = await stripe.subscriptions.list({
    customer: account.stripeCustomerId,
    status: 'active'
  })

  return subscriptions.data.length > 0 && 
         !subscriptions.data[0].cancel_at_period_end
}

export const canUseFeature = async (accountId, feature) => {
  const isPaid = await isAccountPaid(accountId)
  if (!isPaid) return false

  // Add additional feature-specific checks here
  return true
}

