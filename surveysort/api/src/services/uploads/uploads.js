import { getPresignedUrlFromGCP, getPresignedUrlForPreviewImageUpload, getPresignedUrlForSurveyCompanyImageUpload } from 'src/services/uploads/gcpUtils'
export const getPresignedUrl = async ({ input }) => {
  return getPresignedUrlFromGCP({ ...input }); // we can add more wrappers here in future. as of now enabled GCP
}

export const getSurveyPreviewImageUploadPresignedUrl = async ({ input }) => {
  return getPresignedUrlForPreviewImageUpload({ ...input });
}

export const getSurveyCompanyLogoUploadPresignedUrl = async ({ input }) => {
  return getPresignedUrlForSurveyCompanyImageUpload({ ...input });
}

