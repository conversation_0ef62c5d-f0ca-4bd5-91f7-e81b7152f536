const { Storage } = require('@google-cloud/storage');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const csvParser = require('csv-parser');
const fs = require('fs');
const path = require('path');

const storage = new Storage({
  credentials: process.env.GOOGLE_SERVICE_ACCOUNT_KEY,
});

const getTimestamp = () => {
  const now = new Date();
  return now.toISOString().replace(/[:.]/g, '-'); // Replace colons and periods for valid filename characters
}
// Function to generate CSV file
async function generateCsvFile(data) {
  const timestamp = getTimestamp();
  const userId = context?.currentUser?.id || 'test-user';
  const filename = `${userId}-${timestamp}.csv`;

  const filePath = path.join(__dirname, '../../../../', process.env.SURVEY_SORT_UPLOAD_TEMP_FOLDER, filename);
  console.log(`File path: ${filePath}`);
  if (data.length === 0) {
    console.log('No data to write to CSV.');
    return;
  }

  // Create CSV writer
  const csvWriter = createCsvWriter({
    path: filePath,
    header: Object.keys(data[0]).map(key => ({ id: key, title: key })),
  });
  await csvWriter.writeRecords(data);
  console.log(`CSV file ${filePath} generated.`);
  return filePath;
}


// Function to upload CSV file to GCP
async function uploadToGCS(bucketSubFolderName, filePath) {
  try {
    const userId = context?.currentUser?.id || 'test';
    const bucketName = process.env.SURVEY_SORT_USER_UPLOADS_BUCKET_NAME;
    const bucket = storage.bucket(bucketName)
    const fileName = Date.now() + '_' + path.basename(filePath);
    const accountId = context?.currentUser?.accountId || 'test-account';
    const destination = `${accountId}/${userId}/${bucketSubFolderName}/${fileName}`;

    const results = await bucket.upload(filePath, {
      destination: destination,
      gzip: false, // Optional: gzip compress the file while uploading
    });
    console.log(`File ${filePath} uploaded to ${destination}.`);
    return results[1].selfLink || results[1].mediaLink;

  } catch (error) {
    console.error('Error uploading file:', error);
  }
}


export async function upload(queryResults, bucketSubFolderName, fileType = 'csv') { //TODO: move to enum later
  let filePath;
  if (fileType === 'csv') {
    filePath = await generateCsvFile(queryResults);
  }
  if (filePath) {
    const gcpFilePath = await uploadToGCS(bucketSubFolderName, filePath);
    // Clean up: delete the local file after upload
    fs.unlinkSync(filePath);
    console.log(`Local file ${filePath} deleted.`);
    return gcpFilePath;
  }
  throw new Error('File path is undefined. Cannot upload to GCS.');
}

export const generatePresignedUrl = async (bucketName, filePath, fileType, predefinedAcl = null, action = 'read', expiresInMs = 3 * 60 * 1000,) => {
  try {
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);
    const options = {
      action,
      version: 'v4',
      contentType: fileType,
      expires: Date.now() + expiresInMs, // URL expires in ms
    };

    if (predefinedAcl) {
      options.predefinedAcl = predefinedAcl;
    }

    const signedUrl = await file.getSignedUrl(options);

    return { signedUrl: signedUrl[0], filePath: filePath };
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw error; // Re-throw the error for the caller to handle
  }
}

export async function readCSVFromGCS(fileName) {
  // Reference to the bucket and file
  const bucket = storage.bucket(process.env.SURVEY_SORT_USER_UPLOADS_BUCKET_NAME);
  const file = bucket.file(fileName);
  return new Promise((resolve, reject) => {
    // Create a stream to read the file
    const fileStream = file.createReadStream();
    // Array to hold the rows of the CSV
    const results = [];
    const headers = new Set();
    // Pipe the stream into csv-parser and process the data
    fileStream
      .pipe(csvParser())
      .on('data', (row) => {
        Object.keys(row).forEach(header => headers.add(header));
        results.push(row); // Push each row to results array
      })
      .on('end', () => {
        resolve({ headers: Array.from(headers), data: results });
      })
      .on('error', (error) => {
        reject(error); // Reject the promise if an error occurs
      });
  });
}
async function makeFilePublic(fileName) {
  const bucketName = process.env.SURVEY_SORT_USER_UPLOADS_BUCKET_NAME;
  const bucket = storage.bucket(bucketName);
  await bucket.file(fileName).makePublic();
}

export async function getPresignedUrlForPreviewImageUpload({ fileName, fileType, surveyId }) {
  const bucketSubFolderName = 'survey';
  const accountId = context?.currentUser?.accountId || 'test-account';
  const userId = context?.currentUser?.id || 'test';
  fileName = `${surveyId}_preview.${fileName.split('.').pop()}`;
  const filePath = `${accountId}/${userId}/${bucketSubFolderName}/${surveyId}/${fileName}`;
  const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
  const result = await generatePresignedUrl(bucketName, filePath, fileType, 'publicRead', 'write');
  return result;
}

export async function getPresignedUrlFromGCP({ fileName, fileType }) {

  const bucketSubFolderName = process.env.SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_UPLOAD;
  const accountId = context?.currentUser?.accountId || 'test-account';
  const userId = context?.currentUser?.id || 'test';
  fileName = Date.now() + '_' + path.basename(fileName);
  const filePath = `${accountId}/${userId}/${bucketSubFolderName}/${fileName}`;
  const bucketName = process.env.SURVEY_SORT_USER_UPLOADS_BUCKET_NAME;
  return generatePresignedUrl(bucketName, filePath, fileType, null, 'write');
}

export async function getPresignedUrlForSurveyCompanyImageUpload({ fileName, fileType, surveyId }) {

  const bucketSubFolderName = 'survey';
  const accountId = context?.currentUser?.accountId || 'test-account';
  const userId = context?.currentUser?.id || 'test';
  fileName = `${surveyId}_company.${fileName.split('.').pop()}`;
  const filePath = `${accountId}/${userId}/${bucketSubFolderName}/${surveyId}/${fileName}`;
  const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
  const result = await generatePresignedUrl(bucketName, filePath, fileType, 'publicRead');
  return result;
}

export const removePrefix = (url) => {
  const bucketName = process.env.SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME;
  const prefix = `${process.env.GCP_STORAGE_URL}/${bucketName}/`;
  if (url.startsWith(prefix)) {
    return url.slice(prefix.length);
  }
  return url;
}

export const getPresignedUrlForFileDownloads = async (bucketName, filePath, fileType, predefinedAcl = null, accessType, expiresInMs = 3 * 60 * 1000) => {
  return await generatePresignedUrl(bucketName, filePath, fileType, predefinedAcl, 'read', expiresInMs);
}
