import { context } from '@redwoodjs/graphql-server'
import { db } from 'src/lib/db'
import { reportExportRequestsQueue } from 'src/queue/index'
import { ForbiddenError } from '@redwoodjs/graphql-server'

// Helper function to check if user has access to a survey
const checkSurveyAccess = async (surveyId, currentUser) => {
  const survey = await db.survey.findUnique({
    where: { id: surveyId, isDeleted: false },
    select: {
      accountId: true,
      account: { select: { name: true } },
    },
  })

  if (
    !survey ||
    (survey.accountId !== currentUser.accountId &&
      survey.account.name !== 'Demo Account')
  ) {
    throw new ForbiddenError("You don't have permission to access this survey.")
  }

  return survey
}

export const surveyReports = async ({ surveyId }) => {
  const { currentUser } = context
  await checkSurveyAccess(surveyId, currentUser)


  // Fetch actual reports from the database
  const reports = await db.reportExportRequest.findMany({
    where: {
      surveyId,
      accountId: currentUser.accountId,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  return reports
}

export const surveyReport = async ({ id }) => {
  const { currentUser } = context
  
  const report = await db.reportExportRequest.findUnique({
    where: { id },
    include: {
      survey: true,
    },
  })

  if (!report) {
    throw new Error(`Report with ID ${id} not found`)
  }

  // Check if user has access to the survey this report belongs to
  await checkSurveyAccess(report.surveyId, currentUser)

  return report
}

export const createReport = async ({ input }) => {
  const { currentUser } = context
  const survey = await checkSurveyAccess(input.surveyId, currentUser)

  // Create a new report export request and save it to the database
  const newReportRequest = await db.reportExportRequest.create({
    data: {
      surveyId: input.surveyId,
      accountId: currentUser.accountId,
      name: input.name,
      subtitle: input.subtitle || null,
      type: input.type,
      status: 'SUBMITTED', // Default status is SUBMITTED
      downloadUrl: null, // Initially null
      info: input.config || null,
      requestedPage: input.requestedPage || 'REPORTS_PAGE', // Default to REPORTS_PAGE
      createdBy: currentUser?.email || '',
    }
  })

  // Add the request to the queue for processing
  await reportExportRequestsQueue.add('reportExportRequest', {
    uuid: newReportRequest.id,
    surveyId: newReportRequest.surveyId,
  })

  return newReportRequest
}

export const deleteReport = async ({ id }) => {
  const { currentUser } = context
  
  const report = await db.reportExportRequest.findUnique({
    where: { id }
  })

  if (!report) {
    throw new Error(`Report with ID ${id} not found`)
  }

  // Check if user has access to the survey this report belongs to
  await checkSurveyAccess(report.surveyId, currentUser)

  // Delete the report
  return db.reportExportRequest.delete({
    where: { id },
  })
}
