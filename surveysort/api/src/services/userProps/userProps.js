import { context } from '@redwoodjs/graphql-server'

import { db } from 'src/lib/db'

export const PropKey = {
  signupEmailQueued: 'signupEmailQueued',
  preferredName: 'preferredName',
  title: 'title',
  organization: 'organization',
  industry: 'industry',
  onboardingDone: 'onboardingDone',
  verificationEmailQueued: 'verificationEmailQueued',
  welcomeEmailQueued: 'welcomeEmailQueued',
}

// Should only be accessed by admin / tasks / not from UI
export const upsertUserProp = async (userId, key, value) => {
  // Validate key
  if (!Object.values(PropKey).includes(key)) {
    throw new Error(
      'Invalid key provided. It must be one of the defined PropKey values.'
    )
  }

  const user = await db.user.findUnique({
    where: { id: userId },
    select: { props: true }, // Select only the props field
  })

  if (!user) {
    throw new Error('User not found')
  }

  const updatedProps = { ...user.props }

  // Update or insert the key with the new value
  updatedProps[key] = value

  // Update the user record with the new props
  await db.user.update({
    where: { id: userId },
    data: { props: updatedProps },
  })
}

// Should only be accessed by admin / tasks / not from UI
export const getUserProps = async (userId) => {
  console.log('getUserProps', userId)
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { props: true },
  })
  console.log('user', user)
  return user.props || {}
}

// Should only be accessed by admin / tasks / not from UI
export const updateUserPropsByUserId = async (userId, input) => {
  const validatedInput = Object.entries(input).reduce((acc, [key, value]) => {
    if (Object.values(PropKey).includes(key)) {
      acc[key] = value
    }
    return acc
  }, {})

  const updatedUser = await db.user.update({
    where: { id: userId },
    data: { props: { ...(await getUserProps(userId)), ...validatedInput } },
  })
  return updatedUser.props
}

export const getUserOnboardingStatus = async () => {
  const userProps = await getUserProps()
  return !!userProps.onboardingDone
}

export const completeUserOnboarding = async () => {
  await updateUserPropsByUserId(context.currentUser.id, {
    onboardingDone: true,
  })
  return true
}

// Should only be accessed by admin / tasks / not from UI
export const getUserProp = async (userId, key) => {
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { props: true },
  })

  if (!user || !user.props) {
    return null // Return null if no user or no props found
  }

  return user.props[key] || null // Return the prop value or null if the key doesn't exist
}

export const updateUserProps = async ({ input }) => {
  const { currentUser } = context

  if (!currentUser) {
    throw new Error('You must be logged in to update user properties')
  }

  // Check for required fields
  if (!input.preferredName || !input.title || !input.organization || !input.industry) {
    throw new Error(
      'Preferred Name, Title, Organization, and Industry are required fields'
    )
  }

  const validatedInput = Object.entries(input).reduce((acc, [key, value]) => {
    const propKey = Object.entries(PropKey).find(
      ([_, v]) => v.toLowerCase() === key.toLowerCase()
    )?.[1]
    if (propKey) {
      acc[propKey] = value
    }
    return acc
  }, {})

  const currentProps = await getUserProps(currentUser.id)

  const updatedProps = {
    [PropKey.preferredName]:
      validatedInput[PropKey.preferredName] ||
      currentProps[PropKey.preferredName] ||
      '',
    [PropKey.title]:
      validatedInput[PropKey.title] || currentProps[PropKey.title] || '',
    [PropKey.organization]:
      validatedInput[PropKey.organization] ||
      currentProps[PropKey.organization] ||
      '',
    [PropKey.industry]:
      validatedInput[PropKey.industry] ||
      currentProps[PropKey.industry] ||
      '',
    [PropKey.onboardingDone]:
      validatedInput[PropKey.onboardingDone] ??
      currentProps[PropKey.onboardingDone] ??
      false,
    [PropKey.signupEmailQueued]:
      currentProps[PropKey.signupEmailQueued] ?? false,
  }

  await db.user.update({
    where: { id: currentUser.id },
    data: { props: updatedProps },
  })

  return {
    preferredName: updatedProps[PropKey.preferredName],
    title: updatedProps[PropKey.title],
    organization: updatedProps[PropKey.organization],
    industry: updatedProps[PropKey.industry],
    onboardingDone: updatedProps[PropKey.onboardingDone],
    signupEmailQueued: updatedProps[PropKey.signupEmailQueue],
  }
}
