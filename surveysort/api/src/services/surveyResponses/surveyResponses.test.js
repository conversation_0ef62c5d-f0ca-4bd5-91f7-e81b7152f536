import {
  surveyResponses,
  surveyResponse,
  createSurveyResponse,
  updateSurveyResponse,
  deleteSurveyResponse,
} from './surveyResponses'

// Generated boilerplate tests do not account for all circumstances
// and can fail without adjustments, e.g. Float.
//           Please refer to the RedwoodJS Testing Docs:
//       https://redwoodjs.com/docs/testing#testing-services
// https://redwoodjs.com/docs/testing#jest-expect-type-considerations

describe('surveyResponses', () => {
  scenario('returns all surveyResponses', async (scenario) => {
    const result = await surveyResponses()

    expect(result.length).toEqual(Object.keys(scenario.surveyResponse).length)
  })

  scenario('returns a single surveyResponse', async (scenario) => {
    const result = await surveyResponse({
      id: scenario.surveyResponse.one.id,
    })

    expect(result).toEqual(scenario.surveyResponse.one)
  })

  scenario('creates a surveyResponse', async (scenario) => {
    const result = await createSurveyResponse({
      input: {
        surveyId: 'String',
        accountId: scenario.surveyResponse.two.accountId,
        ipAddress: 'String',
        fingerprint: 'String',
        screenResolution: 'String',
        timezone: 'String',
        userAgent: 'String',
        submissionTime: '2024-07-26T13:30:06.895Z',
        lastActiveTime: '2024-07-26T13:30:06.895Z',
      },
    })

    expect(result.surveyId).toEqual('String')
    expect(result.accountId).toEqual(scenario.surveyResponse.two.accountId)
    expect(result.ipAddress).toEqual('String')
    expect(result.fingerprint).toEqual('String')
    expect(result.screenResolution).toEqual('String')
    expect(result.timezone).toEqual('String')
    expect(result.userAgent).toEqual('String')
    expect(result.submissionTime).toEqual(new Date('2024-07-26T13:30:06.895Z'))
    expect(result.lastActiveTime).toEqual(new Date('2024-07-26T13:30:06.895Z'))
  })

  scenario('updates a surveyResponse', async (scenario) => {
    const original = await surveyResponse({
      id: scenario.surveyResponse.one.id,
    })
    const result = await updateSurveyResponse({
      id: original.id,
      input: { surveyId: 'String2' },
    })

    expect(result.surveyId).toEqual('String2')
  })


})
