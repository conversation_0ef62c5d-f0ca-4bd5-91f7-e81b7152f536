export const standard = defineScenario({
  surveyResponse: {
    one: {
      data: {
        surveyId: 'String',
        ipAddress: 'String',
        fingerprint: 'String',
        screenResolution: 'String',
        timezone: 'String',
        userAgent: 'String',
        submissionTime: '2024-07-26T13:30:06.936Z',
        lastActiveTime: '2024-07-26T13:30:06.936Z',
        account: { create: {} },
      },
    },
    two: {
      data: {
        surveyId: 'String',
        ipAddress: 'String',
        fingerprint: 'String',
        screenResolution: 'String',
        timezone: 'String',
        userAgent: 'String',
        submissionTime: '2024-07-26T13:30:06.936Z',
        lastActiveTime: '2024-07-26T13:30:06.936Z',
        account: { create: {} },
      },
    },
  },
})
