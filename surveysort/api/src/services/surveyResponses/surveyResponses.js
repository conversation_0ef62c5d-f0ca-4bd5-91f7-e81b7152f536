import { db } from 'src/lib/db'
import { gradingQueue } from 'src/queue/index'
import { upload } from '../uploads/gcpUtils'
import { logger } from 'src/lib/logger'
import { processImmediateNotifications } from 'src/services/notifications'

export const surveyResponses = () => {
  return db.surveyResponse.findMany()
}

export const surveyResponse = ({ id }) => {
  return db.surveyResponse.findUnique({
    where: { id },
    include: {
      questionResponses: true,
    },
  })
}

export const createSurveyResponse = async ({ input }, { context }) => {
  logger.info('Creating new survey response', { surveyId: input.surveyId })
  
  const ipAddress =
    context?.event?.headers['x-forwarded-for'] ||
    context?.event?.requestContext?.identity?.sourceIp ||
    context?.event?.headers['x-real-ip'] ||
    context?.event?.headers['x-appengine-user-ip'] ||
    'IP address not found'
  logger.debug('Detected IP address', { ipAddress })
  // Add the IP address to the input data
  input.ipAddress = ipAddress

  if (input.questionResponse.questionId) {
    input.questionResponses = [input.questionResponse]
    delete input.questionResponse
  }

  const createAndUpdateSurveyResponse = await db.$transaction(async (db) => {
    console.log('Starting DB transaction for survey response creation')
    logger.debug('Starting DB transaction for survey response creation')
    const result = await db.surveyResponse.create({
      data: input,
    })
    logger.info('Created survey response', { responseId: result.id })
    
    if (result.questionResponses?.length > 0) {
      console.log('Updating question responses', { 
        responseId: result.id,
        questionCount: result.questionResponses.length 
      })
      logger.debug('Updating question responses', { 
        responseId: result.id,
        questionCount: result.questionResponses.length 
      })
      const questionResponses = result.questionResponses
      questionResponses[0].surveyResponseId = result.id
      await db.surveyResponse.update({
        where: { id: result.id },
        data: {
          questionResponses,
        },
      })
    }

    await processImmediateNotifications(result)

    return result
  })

  logger.info('Adding grading job to queue', { 
    responseId: createAndUpdateSurveyResponse.id,
    surveyId: createAndUpdateSurveyResponse.surveyId 
  })
  
  console.log('Adding grading job to queue', { 
    responseId: createAndUpdateSurveyResponse.id,
    surveyId: createAndUpdateSurveyResponse.surveyId 
  })
  await gradingQueue.add('gradeSurveyResponse', {
    surveyResponseId: createAndUpdateSurveyResponse.id,
    surveyId: createAndUpdateSurveyResponse.surveyId,
  })
  console.log({createAndUpdateSurveyResponse})
  return createAndUpdateSurveyResponse
}

export const updateSurveyResponse = ({ id, input }) => {
  return db.surveyResponse.update({
    data: input,
    where: { id },
  })
}


export const surveyResponseWithFilters = async ({ input }) => {
  const res = await db.surveyResponse.findMany();
  await upload(res, 'downloads');
}

export const SurveyResponse = {
  account: (_obj, { root }) => {
    return db.surveyResponse.findUnique({ where: { id: root?.id } }).account()
  },
  enrichedData: (_obj, { root }) => {
    return db.surveyResponse
      .findUnique({ where: { id: root?.id } })
      .enrichedData()
  },
  questionResponses: (_obj, { root }) => {
    return db.surveyResponse
      .findUnique({ where: { id: root?.id } })
      .questionResponses()
  },
}
