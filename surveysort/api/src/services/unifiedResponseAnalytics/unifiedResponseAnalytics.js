import { fetch } from '@whatwg-node/fetch'
import { upload } from 'src/services/uploads/gcpUtils'
import { transactionalQueue } from 'src/queue/index'
import { context } from '@redwoodjs/graphql-server'
import { getPresignedUrlForFileDownloads } from 'src/services/uploads/gcpUtils'

// Private helper function
const fetchUnifiedSurveyResponses = async ({
  surveyId,
  filters = {},
  page = 1,
  pageSize = 20,
  sortField = "submissionTime",
  sortDirection = "desc"
}) => {
  try {
    console.log({ account_id: context.currentUser?.accountId, });
    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/analytics/responses/`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
        body: JSON.stringify({
          survey_id: surveyId,
          account_id: context.currentUser?.accountId,
          filters: filters,
          page,
          page_size: pageSize,
          sort_field: sortField,
          sort_direction: sortDirection,
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        errorData.detail ||
        `Analytics service error: ${response.status}`
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching unified survey responses:', error)
    throw new Error(`Failed to fetch responses: ${error.message}`)
  }
}

// GraphQL resolver
export const unifiedSurveyResponses = ({
  surveyId,
  filters = {},
  page = 1,
  pageSize = 20,
  sortField = "submissionTime",
  sortDirection = "desc"
}) => {
  // Validate sort field
  const validSortFields = [
    "submissionTime",
    "authenticityScore",
    "responseQualityScore",
    "effortScore",
    "overallQualityScore"
  ]

  if (!validSortFields.includes(sortField)) {
    throw new Error(`Invalid sort field. Must be one of: ${validSortFields.join(', ')}`)
  }

  // Validate sort direction
  if (!["asc", "desc"].includes(sortDirection)) {
    throw new Error('Sort direction must be either "asc" or "desc"')
  }

  // Validate page and pageSize
  if (page < 1) throw new Error('Page must be greater than 0')
  if (pageSize < 1 || pageSize > 100) throw new Error('Page size must be between 1 and 100')

  // Call the fetch function with validated parameters
  return fetchUnifiedSurveyResponses({
    surveyId,
    filters,
    page,
    pageSize,
    sortField,
    sortDirection
  })
}

export const exportUnifiedResponses = async ({ input }) => {
  // Fetch all responses without pagination
  const { surveyId, filters, sortField, sortDirection } = input
  const allResponses = await fetchUnifiedSurveyResponses({
    surveyId,
    filters,
    page: 1,
    pageSize: 999999, // Large number to get all responses
    sortField: sortField || 'submissionTime',
    sortDirection: sortDirection || 'desc'
  })

  // Format responses for CSV
  const formattedResponses = allResponses.responses.map(response => ({
    id: response.id,
    submissionTime: response.submissionTime,
    participantEmail: response.participantEmail,
    authenticityScore: response.authenticityScore,
    responseQualityScore: response.responseQualityScore,
    effortScore: response.effortScore,
    overallQualityScore: response.overallQualityScore,
    timeSpentOnSurvey: response.timeSpentOnSurvey,
    isDuplicate: response.isDuplicate,
    copyPastedDetected: response.copyPastedDetected,
    vpnDetected: response.vpnDetected,
    proxyDetected: response.proxyDetected,
    submittedCountry: response.submittedCountry,
    submittedCity: response.submittedCity,
    ...response.questionResponses.reduce(
      (acc, qr) => ({
        ...acc,
        [`question_${qr.questionId}`]: qr.response,
      }),
      {}
    ),
  }))

  const bucketName = process.env.SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_DOWNLOAD;
  // Upload to GCS
  const gcpUrlPath = await upload(
    formattedResponses,
    bucketName,
    'csv'
  )

  // Generate presigned URL for the file
  const fileType = 'text/csv';
  const { filePath, signedUrl } = await getPresignedUrlForFileDownloads(bucketName, gcpUrlPath, fileType, 'publicRead', 30 * 60 * 1000); // 30 minutes

  const baseUrl = process.env.FE_URL + '/redirectGcs'; // Get the API URL from .env, and get the route
  const encodedUrl = encodeURIComponent(signedUrl);
  const encodedContentType = encodeURIComponent(fileType);

  const emailUrl = `${baseUrl}?url=${encodedUrl}&contentType=${encodedContentType}`;

  // Queue email sending
  await transactionalQueue.add('sendDownloadSurveyResponsesEmailLink', {
    emailAddress: context.currentUser.email,
    downloadLink: emailUrl,
  })

  return {
    count: formattedResponses.length,
    email: context.currentUser.email,
  }
}

export const unifiedSurveyFilters = async ({ surveyId }) => {
  const response = await fetch(
    `${process.env.AI_SERVICE_URL}/analytics/responses/filters/${surveyId}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )

  if (!response.ok) {
    throw new Error(`API call failed with status ${response.status}`)
  }

  return response.json()
}
