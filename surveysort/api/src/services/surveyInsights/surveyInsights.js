import { db } from 'src/lib/db'
import { ForbiddenError } from '@redwoodjs/graphql-server'
import { getSurveyInsightChatResponse, getAssistantSuggestions } from 'src/lib/aiService'

const checkSurveyAccess = async (surveyId, currentUser) => {
  const survey = await db.survey.findUnique({
    where: { id: surveyId, isDeleted: false },
    select: {
      accountId: true,
      account: { select: { name: true } },
    },
  })

  if (
    !survey ||
    (survey.accountId !== currentUser.accountId &&
      survey.account.name !== 'Demo Account')
  ) {
    throw new ForbiddenError("You don't have permission to access this survey.")
  }

  return survey
}

export const surveyInsightConversation = async ({ surveyId }) => {
  const { currentUser } = context
  await checkSurveyAccess(surveyId, currentUser)

  const conversation = await db.surveyInsightConversation.findUnique({
    where: { surveyId },
    include: { survey: true },
  })

  if (conversation) {
    // Ensure all messages have responseType
    conversation.messages = conversation.messages.map(message => ({
      ...message,
      responseType: message.responseType || (message.sender === 'user' ? 'text' : 'analytics')
    }))
  }

  return conversation
}

export const newMessages = async ({ surveyId, since }) => {
  const { currentUser } = context
  await checkSurveyAccess(surveyId, currentUser)

  const conversation = await db.surveyInsightConversation.findUnique({
    where: { surveyId },
    select: { messages: true },
  })

  if (!conversation) {
    throw new ForbiddenError("You don't have access to this conversation.")
  }

  // Filter messages and ensure responseType exists
  const newMessages = conversation.messages
    .filter((message) => new Date(message.timestamp) > new Date(since))
    .map(message => ({
      ...message,
      responseType: message.responseType || (message.sender === 'user' ? 'text' : 'analytics')
    }))

  return newMessages
}

export const sendMessage = async ({ surveyId, message }) => {
  const { currentUser } = context
  const survey = await checkSurveyAccess(surveyId, currentUser)

  // Retrieve or create the conversation
  let conversation = await db.surveyInsightConversation.findUnique({
    where: { surveyId },
    include: { survey: true },
  })

  if (!conversation) {
    conversation = await db.surveyInsightConversation.create({
      data: {
        survey: { connect: { id: surveyId } },
        account: { connect: { id: survey.accountId } },
        messages: [],
      },
      include: { survey: true },
    })
  }

  // Store only text data for user message
  const userMessage = {
    sender: 'user',
    text: message,
    timestamp: new Date(),
    responseType: 'text'
  }

  let updatedMessages = [...conversation.messages, userMessage]

  // Update DB with user message
  conversation = await db.surveyInsightConversation.update({
    where: { id: conversation.id },
    data: { messages: updatedMessages },
    include: { survey: true },
  })

  // Get AI response
  const assistantResponse = await getSurveyInsightChatResponse(
    surveyId,
    survey.accountId,
    message
  )

  // Store complete assistant message directly from API
  const assistantMessageForDB = {
    sender: 'assistant',
    timestamp: new Date(),
    text: assistantResponse.response,
    ...assistantResponse
  }

  updatedMessages = [...updatedMessages, assistantMessageForDB]

  // Update DB with complete message
  conversation = await db.surveyInsightConversation.update({
    where: { id: conversation.id },
    data: { messages: updatedMessages },
    include: { survey: true },
  })

  return conversation
}

export const assistantSuggestions = async ({ surveyId }) => {
  const { currentUser } = context
  const survey = await checkSurveyAccess(surveyId, currentUser)
  
  try {
    return await getAssistantSuggestions(
      surveyId, 
      currentUser.accountId
    )
  } catch (error) {
    console.error('Error fetching suggestions:', error)
    throw error
  }
}

export const SurveyInsightConversation = {
  survey: (_obj, { root }) => {
    return db.surveyInsightConversation
      .findUnique({ where: { id: root?.id } })
      .survey()
  },
  account: (_obj, { root }) => {
    return db.surveyInsightConversation
      .findUnique({ where: { id: root?.id } })
      .account()
  },
}
