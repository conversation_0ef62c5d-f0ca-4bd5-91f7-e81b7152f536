import { createParticipantLink, verifyToken, participantLinksByParticipant } from './participantLinks'
import { db } from 'src/lib/db'

// Mock the database
jest.mock('src/lib/db', () => ({
  db: {
    participantLink: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
    surveyParticipant: {
      update: jest.fn(),
    },
  },
}))

// Mock crypto
jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({
    toString: jest.fn(() => 'mocktoken123'),
  })),
  createHash: jest.fn(() => ({
    update: jest.fn(() => ({
      digest: jest.fn(() => 'mockhash123'),
    })),
  })),
  randomUUID: jest.fn(() => 'mock-uuid'),
}))

describe('participantLinks service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createParticipantLink', () => {
    it('creates a participant link with default TTL', async () => {
      const mockLink = {
        id: 'link123',
        expiresAt: new Date(),
        batchId: 'mock-uuid',
      }
      
      db.participantLink.create.mockResolvedValue(mockLink)
      
      const result = await createParticipantLink({
        input: { participantId: 'participant123' }
      })
      
      expect(db.participantLink.create).toHaveBeenCalledWith({
        data: {
          participantId: 'participant123',
          token: 'mocktoken123',
          tokenHash: 'mockhash123',
          batchId: 'mock-uuid',
          expiresAt: expect.any(Date),
        },
      })
      
      expect(result).toEqual({
        id: 'link123',
        token: 'mocktoken123',
        expiresAt: mockLink.expiresAt,
        batchId: 'mock-uuid',
      })
    })
  })

  describe('participantLinksByParticipant', () => {
    it('finds links for a participant', async () => {
      const mockLinks = [{ id: 'link1' }, { id: 'link2' }]
      db.participantLink.findMany.mockResolvedValue(mockLinks)
      
      const result = await participantLinksByParticipant({ participantId: 'participant123' })
      
      expect(db.participantLink.findMany).toHaveBeenCalledWith({
        where: { participantId: 'participant123' },
        orderBy: { createdAt: 'desc' },
      })
      
      expect(result).toEqual(mockLinks)
    })
  })

  describe('verifyToken', () => {
    it('verifies a valid token', async () => {
      const mockLink = {
        id: 'link123',
        clickedAt: null,
        participant: {
          id: 'participant123',
          status: 'PENDING',
        },
      }
      
      db.participantLink.findUnique.mockResolvedValue(mockLink)
      db.participantLink.update.mockResolvedValue({})
      db.surveyParticipant.update.mockResolvedValue({})
      
      const result = await verifyToken('valid-token')
      
      expect(db.participantLink.findUnique).toHaveBeenCalledWith({
        where: { token: 'valid-token' },
        include: { participant: true },
      })
      
      expect(db.participantLink.update).toHaveBeenCalledWith({
        where: { id: 'link123' },
        data: { clickedAt: expect.any(Date) },
      })
      
      expect(db.surveyParticipant.update).toHaveBeenCalledWith({
        where: { id: 'participant123' },
        data: { status: 'CLICKED' },
      })
      
      expect(result).toEqual(mockLink.participant)
    })

    it('throws error for invalid token', async () => {
      db.participantLink.findUnique.mockResolvedValue(null)
      
      await expect(verifyToken('invalid-token')).rejects.toThrow('Invalid survey link')
    })
  })
}) 