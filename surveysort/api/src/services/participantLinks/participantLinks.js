import { db } from 'src/lib/db'
import crypto from 'crypto'

/**
 * Creates a new participant link
 * @param {Object} input - Link creation parameters
 * @param {string} input.participantId - The participant ID to link
 * @param {number} input.ttlDays - Time to live in days (default: 365)
 * @returns {Promise<Object>} The created link
 */
export const createParticipantLink = async ({ input }) => {
  const { participantId, ttlDays = 365 } = input
  
  // Generate a secure random token (9 bytes = 72 bits)
  const randomBytes = crypto.randomBytes(9)
  const token = randomBytes.toString('base64url')
  
  // Generate a hash of the token for secure lookups
  const tokenHash = crypto.createHash('sha256').update(token).digest('hex')
  
  // Calculate expiration date
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + ttlDays)
  
  // Create a batch ID for this generation
  const batchId = crypto.randomUUID()

  // Create the link in the database
  const link = await db.participantLink.create({
    data: {
      participantId,
      token,
      tokenHash,
      batchId,
      expiresAt,
    },
  })

  return { 
    id: link.id,
    token,
    expiresAt: link.expiresAt,
    batchId: link.batchId
  }
}

/**
 * Get links for a participant
 * @param {string} participantId - The participant ID
 * @returns {Promise<Array>} All links for this participant
 */
export const participantLinksByParticipant = async ({ participantId }) => {
  return db.participantLink.findMany({
    where: { participantId },
    orderBy: { createdAt: 'desc' },
  })
}

/**
 * Verifies a participant token
 * @param {string} token - The token to verify
 * @returns {Promise<Object>} The participant associated with the token
 * @throws Will throw an error if token is invalid or expired
 */
export const verifyToken = async (token) => {
  // Find the link by token
  const link = await db.participantLink.findUnique({
    where: { token },
    include: { participant: true },
  })

  // Validate the link
  if (!link) {
    throw new Error('Invalid survey link')
  }

  // Check if the link has expired
  if (link.expiresAt && new Date() > link.expiresAt) {
    throw new Error('Survey link has expired')
  }

  // Update the link's clickedAt if not already set
  if (!link.clickedAt) {
    await db.participantLink.update({
      where: { id: link.id },
      data: {
        clickedAt: new Date(),
      },
    })

    // Update participant status to CLICKED if not already COMPLETED
    if (link.participant.status !== 'COMPLETED') {
      await db.surveyParticipant.update({
        where: { id: link.participant.id },
        data: { status: 'CLICKED' },
      })
    }
  }

  return link.participant
} 

/**
 * Helper for participants service to create links
 * @param {Object} params - Link creation parameters 
 * @param {string} params.participantId - The participant ID to link
 * @param {number} params.ttlDays - Time to live in days (default: 365)
 * @returns {Promise<Object>} The created link
 */
export const createLink = async ({ participantId, ttlDays = 365 }) => {
  return createParticipantLink({ input: { participantId, ttlDays } })
} 