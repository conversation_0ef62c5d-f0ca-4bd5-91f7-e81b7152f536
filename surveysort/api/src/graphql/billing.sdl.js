export const schema = gql`
  type MeteredComponent {
    name: String!
    includedQuantity: Int!
    unitAmount: Int!
  }

  type Plan {
    priceId: String!
    productId: String!
    name: String!
    description: String!
    monthlyPrice: Float
    yearlyPrice: Float
    yearlyPriceTotal: Float
    interval: String!
    currency: String!
    features: [String!]!
    isCurrent: Boolean!
    meteredComponents: [MeteredComponent!]!
    discount: Float
    isEnterprise: Boolean!
    hideFromPricing: Boolean
    contactSales: Boolean
  }

  type CheckoutSession {
    sessionId: String!
    url: String!
  }

  type Query {
    getPlans: [Plan!]! @skipAuth
    getCurrentUsage: JSON! @requireAuth
  }

  type Mutation {
    createCheckoutSession(priceId: String!): CheckoutSession! @requireAuth
    requestEnterprisePlan(name: String!, email: String!, company: String!, message: String): Boolean! @skipAuth
  }
`
