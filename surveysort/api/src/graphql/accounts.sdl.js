export const schema = gql`
  type Account {
    id: String!
    name: String!
    checkoutInProgress: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    isDeleted: Boolean!
    users: [User!]!
  }

  type User {
    id: String!
    email: String!
    isVerified: Boolean!
    isDeleted: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    props: JSON
    userRoles: [UserRole!]!
  }

  type UserRole {
    id: String!
    userId: String!
    roleId: String!
    accountId: String!
    isDeleted: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    user: User!
    role: Role!
    account: Account!
  }

  type Role {
    id: String!
    name: String!
    description: String
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type VerificationResult {
    message: String
    error: String
  }

  type Query {
    currentAccount: Account @requireAuth
  }

  type Mutation {
    verifyEmail(token: String!): VerificationResult! @skipAuth
  }
`
