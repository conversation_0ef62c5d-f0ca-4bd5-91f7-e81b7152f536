export const schema = gql`
  enum FilterType {
    METRIC
    BOOLEAN
    SELECT
    RANGE
    DATE_RANGE
    TEXT
  }

  enum QuestionFilterType {
    CHOICE
    RANGE
    DATE_RANGE
    TEXT
    MATRIX
    CONTACT
  }

  type FilterOption {
    value: String!
    label: String!
  }

  type FilterValidation {
    min: Float
    max: Float
    required: Boolean
    pattern: String
  }

  type FilterConfiguration {
    key: String!
    type: FilterType!
    label: String!
    description: String
    defaultValue: JSON
    options: [FilterOption]
    validation: FilterValidation
    stat: Int
    previousStat: String
  }

  type DeviceInfo {
    browser: String
    os: String
    device: String
    screenResolution: String
  }

  type QuestionResponse {
    questionId: String!
    response: JSON!
    title: String
    type: String
  }

  type UnifiedResponse {
    id: String!
    surveyId: String!
    submissionTime: DateTime!
    participantEmail: String
    questionResponses: [QuestionResponse!]!
    authenticityScore: Float
    overallResponseQualityScore: Float
    effortScorePerOpenEndedQuestion: JSON
    relevanceScorePerOpenEndedQuestion: JSON
    responseQualityExplanationPerOpenEndedQuestion: JSON
    keystrokePatternScorePerOpenEndedQuestion: JSON
    timeSpentOnSurvey: Int
    keystrokePatternScore: Float
    straightLiningDetected: Boolean
    isDuplicate: Boolean
    copyPastedDetected: Boolean
    vpnDetected: Boolean
    proxyDetected: Boolean
    submittedCountry: String
    submittedCity: String
    submittedLatitude: Float
    submittedLongitude: Float
    deviceInfo: DeviceInfo
    submittedTooFast: Boolean
    isHighQuality: Boolean
    isLowEffort: Boolean
    isIrrelevant: Boolean
    hasContactDetails: Boolean
    questionStatuses: JSON
    isGradingComplete: Boolean
    submissionContactDetails: JSON
  }

  type ResponseMetrics {
    totalResponsesCount: Int!
    completedResponsesCount: Int!
    avgTimeSpentInMinutes: Float!
    highQualityResponsesCount: Int!
    overallResponseQualityStatus: String!
  }

  type PageInfo {
    hasNextPage: Boolean!
    endCursor: String
    totalPages: Int!
    currentPage: Int!
    totalCount: Int!
  }

  type QuestionFilterConfig {
    id: String!
    title: String!
    type: String!
    filterType: QuestionFilterType!
    choices: [String]
    min: Float
    max: Float
    allowMultiple: Boolean
    allowWriteIn: Boolean
    rows: [String]
    columns: [String]
    cellType: String
    fields: [String]
  }

  type UnifiedResponsesContext {
    availableFilters: [FilterConfiguration!]!
    availableQuestionFilters: [QuestionFilterConfig!]!
  }

  type UnifiedResponseAnalytics {
    responses: [UnifiedResponse!]!
    pageInfo: PageInfo!
    context: UnifiedResponsesContext!
  }

  type UnifiedResponseExport {
    count: Int!
    email: String!
  }

  input ScoreFilter {
    min: Float
    max: Float
  }

  input DateRangeFilter {
    start: String
    end: String
  }

  input DeviceInfoFilter {
    browser: String
    os: String
    device: String
  }

  input QuestionResponseFilter {
    questionId: String!
    type: String!
    value: String
    minValue: Float
    maxValue: Float
    row: String
    column: String
  }

  input UnifiedResponseFilters {
    isDuplicate: Boolean
    copyPastedDetected: Boolean
    vpnDetected: Boolean
    proxyDetected: Boolean
    submissionTime: DateRangeFilter
    submittedCountry: String
    submittedCity: String
    questionFilters: [QuestionResponseFilter]
    isHighQuality: Boolean
    isLowEffort: Boolean
    isIrrelevant: Boolean
    sentiment: String
    hasContactDetails: Boolean
    isComplete: Boolean
    straightLiningDetected: Boolean
  }

  input ExportUnifiedResponsesInput {
      surveyId: String!
      filters: UnifiedResponseFilters,
      sortField: String,
      sortDirection: String,
  }

  type Query {
    unifiedSurveyResponses(
      surveyId: String!
      filters: UnifiedResponseFilters
      page: Int = 1
      pageSize: Int = 20
      sortField: String = "submissionTime"
      sortDirection: String = "desc"
    ): UnifiedResponseAnalytics! @requireAuth

    unifiedSurveyFilters(
      surveyId: String!
    ): UnifiedResponsesContext! @requireAuth
  }

  type Mutation {
    exportUnifiedResponses(input: ExportUnifiedResponsesInput! ): UnifiedResponseExport!  @requireAuth
  }
`
