export const schema = gql`
  type Account {
    id: String!
    name: String
  }

  enum SurveyStatus {
    DRAFT
    PUBLISHED
    COMPLETED
  }

  enum SurveySource {
    CREATED
    IMPORTED
    INTEGRATED
  }

  type Survey {
    id: String!
    title: String!
    description: String
    accountId: String!
    account: Account!
    status: SurveyStatus!
    source: SurveySource!
    isDeleted: Boolean!
    isSuspended: Boolean!
    createdAt: DateTime
    updatedAt: DateTime
    createdBy: String
    surveyDetails: JSON
    audience: String!
    surveyObjective: String!
    additionalContext: String
    settings: SurveySettings
    totalResponses: Int
    completionRate: String
  }

  type SurveyPage {
    surveys: [Survey!]!
    count: Int!
  }

  type EndUserSurvey {
    id: String!
    title: String!
    description: String!
    createdBy: String!
    createdAt: DateTime
    questions: [Question]!
    status: SurveyStatus!
    source: SurveySource!
    settings: SurveySettings
  }

  type SurveySettings {
    id: String!
    surveyId: String!
    disclosure: String
    customTheme: JSON
    requireEmail: Boolean!
    showProgressBar: Boolean!
    showNavigation: Boolean!
    companyLogo: String
    removeBranding: Boolean!
    randomizationGroups: [RandomizationGroup!]
    themeConfig: ThemeConfig
    previewImage: String
    previewTitle: String
    previewDescription: String
  }

  type Query {
    surveys(search: String): [Survey!] @requireAuth
    surveyDetail(id: String!): Survey @requireAuth
    endUserSurvey(id: String!): EndUserSurvey @skipAuth
    surveySettings(surveyId: String!): SurveySettings @requireAuth
    surveyTemplates: [SurveyTemplate!]! @requireAuth
    surveyTemplate(id: Int!): SurveyTemplate @requireAuth
  }

  input CreateSurveyInput {
    title: String!
    audience: String!
    surveyObjective: String!
    additionalContext: String
    productName: String!
    isGeneratedByAi: Boolean
  }


  input UpdateSurveyInput {
    title: String
    description: String
    audience: String
    surveyObjective: String
    additionalContext: String
    status: SurveyStatus
    isDeleted: Boolean
    isSuspended: Boolean
    disclosure: String
  }


input ColumnMappingInput {
  questionTitle: String!
  questionType: QuestionType!
}

input CreateExternalUploadSurveyInput {
  filePath: String!
  mappings: JSON! # Or define a specific input type for mappings
}


  type ResearchLink {
    title: String!
    url: String!
  }



  type Mutation {
    createSurvey(input: CreateSurveyInput!): Survey! @requireAuth
    createExternalUploadSurvey(input: CreateExternalUploadSurveyInput!): Survey! @requireAuth
    updateSurvey(id: String!, input: UpdateSurveyInput!): Survey! @requireAuth
    deleteSurvey(id: String!): Survey! @requireAuth
    updateSurveySettings(surveyId: String!, input: UpdateSurveySettingsInput!): SurveySettings! @requireAuth
    createSurveyWithTemplate(input: CreateSurveyWithTemplateInput!): Survey! @requireAuth
  }

  input UpdateSurveySettingsInput {
    disclosure: String
    customTheme: JSON
    requireEmail: Boolean
    showProgressBar: Boolean
    showNavigation: Boolean
    companyLogo: String
    removeBranding: Boolean
    randomizationGroups: [RandomizationGroupInput!]
    themeConfig: ThemeConfigInput
    previewImage: String
    previewTitle: String
    previewDescription: String
  }

  type RandomizationGroup {
    name: String!
    questionIds: [String!]!
  }

  input RandomizationGroupInput {
    name: String!
    questionIds: [String!]!
  }

  type ThemeConfig {
    questionTextColor: String
    answerTextColor: String
    buttonColor: String
    backgroundColor: String
    progressBarColor: String
  }

  input ThemeConfigInput {
    questionTextColor: String
    answerTextColor: String
    buttonColor: String
    backgroundColor: String
    progressBarColor: String
  }

  type SurveyTemplate {
    id: Int!
    name: String!
    description: String
    imageUrl: String
    questions: [TemplateQuestion!]!
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type TemplateQuestion {
    id: Int!
    title: String!
    type: QuestionType!
    description: String
    choices: JSON
    config: JSON
    order: Int!
  }

  input CreateSurveyFromTemplateInput {
    templateId: String!
    title: String!
    audience: String!
    surveyObjective: String!
    additionalContext: String
  }

  input CustomizeTemplateInput {
    templateId: String!
    surveyObjective: String!
    audience: String!
    productName: String!
  }

  type CustomizedTemplateResponse {
    questions: [CustomizedQuestion!]!
    considerations: String
  }

  type CustomizedQuestion {
    title: String!
    type: QuestionType!
    description: String
    choices: JSON
  }

  input CreateSurveyWithTemplateInput {
    templateId: Int!
    title: String
    productName: String!
    audience: String!
    additionalContext: String
  }
`
