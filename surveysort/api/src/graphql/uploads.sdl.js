export const schema = gql`
  input PresignedUrlInput {
    fileName: String!
    fileType: String!
  }

  type PresignedUrlResponse {
    signedUrl: String!
    filePath: String!
  }

  input PresignedSurveyPreviewImageUrlInput{
    fileName: String!
    fileType: String!
    surveyId: String!
  }

  type Mutation {
    getPresignedUrl(input: PresignedUrlInput!): PresignedUrlResponse!
      @requireAuth
      getSurveyPreviewImageUploadPresignedUrl(input: PresignedSurveyPreviewImageUrlInput!): PresignedUrlResponse!
      @requireAuth
      getSurveyCompanyLogoUploadPresignedUrl(input: PresignedSurveyPreviewImageUrlInput!): PresignedUrlResponse!
      @requireAuth

  }
`
