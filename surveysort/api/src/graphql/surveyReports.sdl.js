export const schema = gql`
  type SurveyReport {
    id: String!
    surveyId: String!
    accountId: String!
    name: String!
    subtitle: String
    type: String!
    status: String!
    createdAt: DateTime!
    createdBy: String
    downloadUrl: String
    info: JSON
    requestedPage: String
  }

  input CreateReportInput {
    surveyId: String!
    name: String!
    subtitle: String
    type: String!
    config: JSON
    requestedPage: String
  }

  type Query {
    surveyReports(surveyId: String!): [SurveyReport!]! @requireAuth
    surveyReport(id: String!): SurveyReport @requireAuth
  }

  type Mutation {
    createReport(input: CreateReportInput!): SurveyReport! @requireAuth
    deleteReport(id: String!): SurveyReport! @requireAuth
  }
` 