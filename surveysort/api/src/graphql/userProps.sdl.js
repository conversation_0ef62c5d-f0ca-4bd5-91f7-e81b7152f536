export const schema = gql`
  type UserProps {
    preferredName: String!
    title: String!
    organization: String!
    industry: String!
    onboardingDone: Boolean!
  }

  input UpdateUserPropsInput {
    preferredName: String
    title: String
    organization: String
    industry: String
    onboardingDone: Boolean
  }

  type Query {
    getUserProps: UserProps! @requireAuth
  }

  type Mutation {
    updateUserProps(input: UpdateUserPropsInput!): UserProps! @requireAuth
  }
`
