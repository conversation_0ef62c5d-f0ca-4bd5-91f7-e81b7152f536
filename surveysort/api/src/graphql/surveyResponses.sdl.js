export const schema = gql`
  type SurveyResponse {
    id: String!
    surveyId: String!
    participantEmail: String
    account: Account!
    participantId: String
    ipAddress: String!
    fingerprint: String!
    screenResolution: String!
    timezone: String!
    userAgent: String!
    submissionTime: DateTime!
    isComplete: Boolean!
    lastActiveTime: DateTime!
    questionResponses: [QuestionResponse]!
    enrichedData: SurveyResponseEnrichedData
  }

  input SurveyResponseFilters {
    isComplete: Boolean
    surveyId: String
  }

  type Query {
    surveyResponses: [SurveyResponse!]! @skipAuth
    surveyResponse(id: String!): SurveyResponse @skipAuth
    surveyResponseWithFilters(filters: SurveyResponseFilters!): [SurveyResponse] @skipAuth
  }

  input CreateSurveyResponseInput {
    surveyId: String!
    participantEmail: String
    participantId: String
    fingerprint: String!
    screenResolution: String!
    timezone: String!
    userAgent: String!
    submissionTime: DateTime!
    lastActiveTime: DateTime!
    questionResponse: JSON!
  }

  input UpdateSurveyResponseInput {
    surveyId: String
    participantEmail: String
    participantId: String
    ipAddress: String
    fingerprint: String
    screenResolution: String
    timezone: String
    userAgent: String
    submissionTime: DateTime
    isComplete: Boolean
    lastActiveTime: DateTime
    questionResponse: CreateQuestionResponseInput!
  }

  type Mutation {
    createSurveyResponse(input: CreateSurveyResponseInput!): SurveyResponse!
      @skipAuth
    updateSurveyResponse(
      id: String!
      input: UpdateSurveyResponseInput!
    ): SurveyResponse! @skipAuth
  }
`
