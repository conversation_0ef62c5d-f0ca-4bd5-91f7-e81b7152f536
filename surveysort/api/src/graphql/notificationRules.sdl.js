export const schema = gql`
  type NotificationRule {
    id: String!
    surveyId: String!
    event: String!
    channel: NotificationChannel!
    frequency: NotificationFrequency!
    isActive: Boolean!
    template: String
    webhookUrl: String
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  enum NotificationChannel {
    EMAIL
    WEBHOOK
  }

  enum NotificationFrequency {
    IMMEDIATE
    DAILY
    WEEKLY
  }

  type Query {
    notificationRules: [NotificationRule!]! @requireAuth
    notificationRule(id: String!): NotificationRule @requireAuth
    notificationRulesBySurveyId(surveyId: String!): [NotificationRule!]! @requireAuth
  }

  input CreateNotificationRuleInput {
    surveyId: String!
    event: String!
    channel: NotificationChannel!
    frequency: NotificationFrequency!
    template: String
    webhookUrl: String
    isActive: Boolean!
  }

  input UpdateNotificationRuleInput {
    event: String!
    channel: NotificationChannel
    frequency: NotificationFrequency
    isActive: Boolean
    template: String
    webhookUrl: String
  }

  type Mutation {
    createNotificationRule(input: CreateNotificationRuleInput!): NotificationRule! @requireAuth
    updateNotificationRule(id: String!, input: UpdateNotificationRuleInput!): NotificationRule! @requireAuth
    deleteNotificationRule(id: String!): NotificationRule! @requireAuth
    testNotificationRule(id: String!): Boolean! @requireAuth
  }
` 