export const schema = gql`
  type QuestionResponse {
    id: String!
    surveyId: String
    surveyResponseId: String!
    surveyResponse: SurveyResponse!
    questionId: String!
    question: Question!
    questionNumber: Int!
    pageNumber: Int!
    response: JSON!
    timeToSubmit: Float!
    keystrokeTimingData: JSON
    isResponseCopyPasted: Boolean!
    submissionTime: DateTime!
  }

  type Query {
    questionResponses: [QuestionResponse!]! @requireAuth
    questionResponse(id: String!): QuestionResponse @requireAuth
  }

  input CreateQuestionResponseInput {
    surveyId: String
    surveyResponseId: String
    questionId: String!
    questionNumber: Int!
    pageNumber:Int!
    response: JSON!
    timeToSubmit: Float!
    keystrokeTimingData: JSON
    isResponseCopyPasted: Boolean!
    submissionTime: DateTime!
  }

  input UpdateQuestionResponseInput {
    surveyId: String
    surveyResponseId: String!
    questionId: String!
    questionNumber: Int
    pageNumber:Int
    response: JSON
    timeToSubmit: Float
    keystrokeTimingData: JSON
    isResponseCopyPasted: Boolean
    submissionTime: DateTime
  }

  type Mutation {
    createQuestionResponse(
      input: CreateQuestionResponseInput!
    ): QuestionResponse! @skipAuth
  }
`
