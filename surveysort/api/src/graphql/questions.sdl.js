// DONT REMOVE:if you change this please also change the questionTypesSDL.js file
export const schema = gql`
  input QuestionConfigInput {
    choices: [String!]
    minLength: Int
    maxLength: Int
    rules: [RuleInput!]
    condition: String
    actions: [ActionInput!]
    rating: Int
    scale: Int
    allowMultiple: Boolean
    allowWriteIn: Boolean
    rows: [String!]
    columns: [String!]
    cellType: String
    includeTime: Boolean
    requireName: Boolean
    requireEmail: Boolean
    requirePhone: Boolean
    requireCompany: Boolean
    requireAddress: Boolean
    requireWebsite: Boolean
    randomize: Boolean
    randomizeChoices: [Int!]
    randomizeRows: [Int!]
    welcomeMessage: String
    welcomeDisclaimer: String
    requireDisclaimer: Boolean
    thankYouMessage: String
  }

  type QuestionConfig {
    choices: [String!]
    minLength: Int
    maxLength: Int
    rules: [Rule!]
    condition: String
    actions: [Action!]
    rating: Int
    scale: Int
    allowMultiple: Boolean
    allowWriteIn: Boolean
    rows: [String!]
    columns: [String!]
    cellType: String
    includeTime: Boolean
    requireName: Boolean
    requireEmail: Boolean
    requirePhone: Boolean
    requireCompany: Boolean
    requireAddress: Boolean
    requireWebsite: Boolean
    randomize: Boolean
    randomizeChoices: [Int!]
    randomizeRows: [Int!]
    welcomeMessage: String
    welcomeDisclaimer: String
    requireDisclaimer: Boolean
    thankYouMessage: String
  }


  input RuleInput {
    operation: String!
    value: String
  }

  type Rule {
    operation: String!
    value: String
  }

  input ActionInput {
    type: String!
    questionNumber: Int
  }

  type Action {
    type: String!
    questionNumber: Int
  }

  type Question {
    id: String!
    title: String!
    explainer: String
    type: QuestionType!
    required: Boolean!
    surveyId: String!
    pageNumber: Int!
    order: Int!
    questionConfig: QuestionConfig
    survey: Survey!
  }

  enum QuestionType {
    MULTIPLE_CHOICE
    ESSAY
    RATING
    SCALE
    SHORT
    SCORING
    RANGE
    DATE_TIME
    IMAGE
    MATRIX
    INSTRUCTION
    EMOTICON
    ACCEPT_DENY
    LIKE_DISLIKE
    CONTACT_DETAILS
    WELCOME_MESSAGE
    THANK_YOU_MESSAGE
  }

  type Query {
    surveyQuestions(surveyId: String!): [Question!]! @requireAuth
  }

  input CreateQuestionInput {
    title: String!
    explainer: String
    type: QuestionType!
    required: Boolean!
    surveyId: String!
    pageNumber: Int
    order: Int
    questionConfig: QuestionConfigInput
  }

  input UpdateQuestionInput {
    title: String
    explainer: String
    type: QuestionType
    required: Boolean
    questionConfig: QuestionConfigInput!
    pageNumber: Int
  }

  input UpdateQuestionsInput {
    questions: [UpdateQuestionInput!]!
  }

  input ReorderQuestionInput {
    id: String!
    order: Int!
    pageNumber: Int!
  }

  input ReorderQuestionsInput {
    surveyId: String!
    questions: [ReorderQuestionInput!]!
  }

  type Mutation {
    createQuestion(input: CreateQuestionInput!): Question! @requireAuth
    updateQuestion(id: String!, input: UpdateQuestionInput!): Question! @requireAuth
    updateQuestions(input: UpdateQuestionsInput!): [Question!]! @requireAuth
    deleteQuestion(id: String!): Question! @requireAuth
    reorderQuestions(input: ReorderQuestionsInput!): [Question!]! @requireAuth
  }
`
