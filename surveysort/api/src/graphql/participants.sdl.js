export const schema = gql`
  type SurveyParticipant {
    id: String!
    surveyId: String!
    survey: Survey!
    email: String!
    name: String
    props: JSON
    status: ParticipantStatus!
    links: [ParticipantLink!]!
    firstSentAt: DateTime
    lastSentAt: DateTime
    respondedAt: DateTime
  }

  type ParticipantLink {
    id: String!
    participantId: String!
    participant: SurveyParticipant!
    token: String!
    tokenHash: String
    batchId: String
    expiresAt: DateTime
    clickedAt: DateTime
    createdAt: DateTime!
  }

  enum ParticipantStatus {
    PENDING
    SENT
    CLICKED
    COMPLETED
    BOUNCED
    UNSUBSCRIBED
  }

  type ImportReport {
    created: Int!
    updated: Int!
    skipped: Int!
  }

  input UploadCsv {
    surveyId: String!
    data: String
    gcpFilePath: String
    importId: String
  }

  input SegmentInput {
    status: ParticipantStatus
  }

  input ParticipantFilters {
    status: ParticipantStatus
    search: String
  }

  type ParticipantPage {
    participants: [SurveyParticipant!]!
    count: Int!
    hasMore: Boolean!
  }

  type ParticipantStats {
    total: Int!
    pending: Int!
    sent: Int!
    clicked: Int!
    completed: Int!
    bounced: Int!
    unsubscribed: Int!
  }

  input SendEmailInput {
    surveyId: String!
    templateId: String!
    participantIds: [String!]!
  }

  type Mutation {
    upsertParticipantList(input: UploadCsv!): ImportReport! @requireAuth
    generateLinks(surveyId: String!, segment: SegmentInput): Int! @requireAuth
    sendSurveyEmails(input: SendEmailInput!): Boolean! @requireAuth
    deleteParticipant(id: String!): SurveyParticipant! @requireAuth
  }

  type Query {
    listParticipants(surveyId: String!, page: Int, perPage: Int, filter: ParticipantFilters): ParticipantPage! @requireAuth
    participantStats(surveyId: String!): ParticipantStats! @requireAuth
  }
`
