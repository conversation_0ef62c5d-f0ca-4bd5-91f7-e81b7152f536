export const schema = gql`
  type TeamMembersResult {
    activeMembers: [TeamMember!]!
    pendingInvites: [Invitation!]!
  }

  type TeamMember {
    id: String!
    email: String!
    roles: [String!]!
    createdAt: DateTime!
  }

  type Invitation {
    id: String!
    recipientEmail: String!
    token: String!
    createdAt: DateTime!
    expiresAt: DateTime!
    acceptedAt: DateTime
    isDeleted: Boolean!
    account: Account!
    accountId: String!
    sender: User!
    senderId: String!
    inviteUrl: String!
  }

  input AcceptInvitationInput {
    token: String!
    password: String!
  }

  type Query {
    teamMembers: TeamMembersResult! @requireAuth
    invitation(token: String!): Invitation @skipAuth
  }

  type Mutation {
    inviteMember(email: String!): Invitation! @requireAuth
    resendInvite(id: String!): Invitation! @requireAuth
    revokeInvite(id: String!): Boolean! @requireAuth
    removeMember(id: String!): Boolean! @requireAuth
    acceptInvitation(input: AcceptInvitationInput!): Boolean! @skipAuth
  }
` 