export const schema = gql`
  enum TimeFrame {
    ALL
    LAST_1_DAY
    LAST_7_DAYS
    LAST_30_DAYS
    LAST_90_DAYS
    CUSTOM
  }

  input QuestionAnalyticsInput {
    timeframe: TimeFrame
    questionIds: [String!]
    questionTypes: [String!]
    excludeIncomplete: Boolean
    excludeLowQuality: Boolean
  }

  type ChartDataItem {
    category: String!
    value: Int!
    percentage: Float!
    label: String
  }

  type QuestionInsights {
    totalResponses: Int!
    responseRate: Float
    averageRating: Float
    maxRating: Int
    mostSelected: ChartDataItem
    label: String
  }

  type InsightExample {
    insight: String!
    example_responses: [String!]!
  }

  type ThemeCount {
    theme: String!
    count: Int!
  }

  type OpenEndedAnalysis {
    sentimentDistribution: [ChartDataItem!]!
    keyInsights: [InsightExample!]
    commonThemes: [ThemeCount!]
  }

  type QuestionChartData {
    data: [ChartDataItem!]!
    insights: QuestionInsights!
    openEndedAnalysis: OpenEndedAnalysis
  }

  type QuestionData {
    questionId: String!
    questionType: String!
    title: String!
    responseRate: Float!
    chartType: String!
    chartData: QuestionChartData!
  }

  type SurveyOverviewMetrics {
    totalResponses: Int!
    completedResponses: Int!
    completionRate: Float!
    highQualityResponses: Int!
    highQualityRate: Float!
    averageResponseTime: Int!
  }

  type TimeSeriesDataPoint {
    date: DateTime!
    responses: Int!
    completedResponses: Int!
  }

  type QuestionAnalytics {
    surveyMetrics: SurveyOverviewMetrics!
    timeSeriesData: [TimeSeriesDataPoint!]!
    questions: [QuestionData!]!
  }

  type Query {
    questionAnalytics(
      surveyId: String!
      input: QuestionAnalyticsInput
    ): QuestionAnalytics! @requireAuth
  }
` 