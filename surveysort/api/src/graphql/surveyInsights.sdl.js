export const schema = gql`
  type ResponseList {
    responses: [UnifiedResponse!]!
    total_count: Int!
    page: Int!
    page_size: Int!
  }

  type ThemeCount {
    theme: String!
    count: Int!
  }

  type InsightExample {
    insight: String!
    example_responses: [String!]!
  }

  type Message {
    sender: String!
    text: String
    timestamp: DateTime!
    responseType: String!
    response: String
    responseList: ResponseList
    questionAnalytics: QuestionAnalytics
    surveyAnalytics: SurveyAnalytics
    responseAnalytics: UnifiedResponseAnalytics
    metadata: AgentMetadata
    keyInsights: [InsightExample!]
    commonThemes: [ThemeCount!]
  }

  type AssistantSuggestion {
    suggestion: String!
  }

  type AssistantSuggestionsResponse {
    suggestions: [AssistantSuggestion!]!
  }

  type AgentMetadata {
    surveyId: String!
    intentType: String!
    timestamp: String!
    questionIds: [String!]
    timeframe: String
    analysisType: String
    metrics: [String!]
    appliedFilters: JSON
  }

  type SurveyAnalytics {
    metrics: JSON!
    timeSeries: [JSON]
  }

  type SurveyInsightConversation {
    id: String!
    userId: String!
    surveyId: String!
    accountId: String!
    messages: [Message!]!
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type Query {
    surveyInsightConversation(surveyId: String!): SurveyInsightConversation @requireAuth
    newMessages(surveyId: String!, since: DateTime!): [Message!]! @requireAuth
    assistantSuggestions(surveyId: String!): AssistantSuggestionsResponse! @requireAuth
  }

  type Mutation {
    sendMessage(surveyId: String!, message: String!): SurveyInsightConversation @requireAuth
  }
`
