export const schema = gql`
  type AnalyticsSurvey {
    id: String!
    title: String!
    questions: [AnalyticsQuestion!]!
    completionRate: Float!
    summaryMetrics: AnalyticsSummaryMetrics!
    analyticsAvailable: Boolean!
  }

  type SurveyQualityMetrics {
    analyticsAvailable: Boolean!
    authenticityMetrics: AuthenticityMetrics!
    responseQualityMetrics: ResponseQualityMetrics!
    overallQualityMetrics: OverallQualityMetrics!
  }

  type AnalyticsQuestion {
    id: String!
    title: String!
    type: QuestionType!
    responses: [AnalyticsQuestionResponse!]!
  }

  type AnalyticsQuestionResponse {
    response: String!
    count: Int!
  }

  type AnalyticsSummaryMetrics {
    totalResponses: Int!
    averageCompletionTime: Float
  }

  type AuthenticityMetrics {
    vpnProxyDetectionRate: Float
    avgFraudRiskScore: Float
    duplicatedResponseRate: Float
  }

  type ResponseQualityMetrics {
    avgEffortScore: Float
    copyPastedContentRate: Float
    straightLiningRate: Float
    avgTimeSpentOnSurvey: Float
    suspiciousKeystrokePatternRate: Float
    submittedTooFastRate: Float
  }

  type OverallQualityMetrics {
    avgOverallQualityScore: Float
    highQualityPercentage: Float!
    lowQualityPercentage: Float!
  }

  type EnrichedSurveyResponse {
    id: String!
    participantId: String
    participantEmail: String
    submissionTime: DateTime!
    isComplete: Boolean!
    authenticityScore: Float
    responseQualityScore: Float
    effortScore: Float
    overallQualityScore: Float
    timeSpentOnSurvey: Int
    isDuplicate: Boolean
    copyPastedDetected: Boolean
  }

  type EnrichedResponsesPage {
    responses: [EnrichedSurveyResponse!]!
    count: Int!
  }

  type ExportResponse {
    count: Int!
  }

  input SurveyResponseFilters {
    authenticityScore: String
    responseQualityScore: String
    effortScore: String
    overallQualityScore: String
    submittedTooFast: Boolean
    isDuplicate: Boolean
    copyPastedDetected: Boolean
  }


   type SurveyResponseEnrichedData {
    id: String!
    surveyResponseId: String!
    authenticityScore: Float
    vpnDetected: Boolean
    proxyDetected: Boolean
    ipReputationScore: Float
    fraudRiskScore: Float
    responseQualityScore: Float
    effortScore: Float
    relevanceScore: Float
    responseQualityExplanation: String
    straightLiningDetected: Boolean
    copyPastedDetected: Boolean
    timeSpentOnSurvey: Int
    keystrokePatternScore: Float
    isDuplicate: Boolean
    overallQualityScore: Float
    submittedCountry: String
    submittedCity: String
    submittedLatitude: Float
    submittedLongitude: Float
    iapiResponse: JSON
    ipQualityScoreResponse: JSON
    abuseIpDbResponse: JSON
    lastEnrichmentAttempt: DateTime
  }

  input EnrichIpDataInput {
    vpnDetected: Boolean
    proxyDetected: Boolean
    ipReputationScore: Float
    fraudRiskScore: Float
    submittedCountry: String
    submittedCity: String
    submittedLatitude: Float
    submittedLongitude: Float
    iapiResponse: JSON
    ipQualityScoreResponse: JSON
    abuseIpDbResponse: JSON
  }


  type Query {
    surveyResponseAnalytics(surveyId: String!): AnalyticsSurvey! @requireAuth
    surveyQualityMetrics(surveyId: String!): SurveyQualityMetrics! @requireAuth
    enrichedResponsesPage(
      surveyId: String
      page: Int = 1
      perPage: Int = 10
      filters: SurveyResponseFilters
    ): EnrichedResponsesPage! @requireAuth
    enrichedResponsesExport(
      surveyId: String
      filters: SurveyResponseFilters
    ): ExportResponse! @requireAuth
    enrichIpData(
      id: String!
      data: EnrichIpDataInput!
    ): SurveyResponseEnrichedData! @requireAuth
  }
`
