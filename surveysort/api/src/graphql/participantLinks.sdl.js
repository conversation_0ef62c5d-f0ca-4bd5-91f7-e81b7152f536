export const schema = gql`
  type ParticipantLink {
    id: String!
    participantId: String!
    participant: SurveyParticipant!
    token: String!
    tokenHash: String!
    batchId: String
    expiresAt: DateTime
    clickedAt: DateTime
    createdAt: DateTime!
  }
  
  type CreateLinkResponse {
    id: String!
    token: String!
    expiresAt: DateTime
    batchId: String
  }

  input CreateLinkInput {
    participantId: String!
    ttlDays: Int
  }

  input VerifyTokenInput {
    token: String!
  }

  type Mutation {
    createParticipantLink(input: CreateLinkInput!): CreateLinkResponse! @requireAuth
  }

  type Query {
    participantLinksByParticipant(participantId: String!): [ParticipantLink!]! @requireAuth
  }
` 