import {
  Body,
  Container,
  Head,
  Html,
  Preview,
  Text,
  Button,
} from '@react-email/components'

import { emailStyles } from '../components/EmailStyles'

export function SurveyResponseEmail({ 
  surveyTitle, 
  surveyId
}) {
  const surveyUrl = `${process.env.FE_URL}/survey-detail/${surveyId}`

  return (
    <Html>
      <Head />
      <Preview>New response received for {surveyTitle}</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Text style={emailStyles.heading}>
            New Survey Response
          </Text>

          <Text style={emailStyles.paragraph}>
            You have received a new response to <strong>{surveyTitle}</strong>
          </Text>

          <Button 
            href={surveyUrl}
            style={emailStyles.button}
          >
            View Response
          </Button>

          <Text style={emailStyles.footer}>
            You received this notification because you have immediate notifications enabled.
          </Text>
        </Container>
      </Body>
    </Html>
  )
} 