import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Preview,
  Section,
  Text,
  Button,
} from '@react-email/components'

import { emailStyles } from '../components/EmailStyles'

export function DailyDigestEmail({ surveyTitle, responseCount, responses }) {
  return (
    <Html>
      <Head />
      <Preview>Daily Digest: {surveyTitle} ({responseCount} new responses)</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Text style={emailStyles.heading}>
            Daily Survey Digest
          </Text>

          <Text style={emailStyles.paragraph}>
            Here's your daily summary for <strong>{surveyTitle}</strong>
          </Text>

          <Section style={emailStyles.statsBox}>
            <Text style={emailStyles.stat}>
              {responseCount} new responses today
            </Text>
          </Section>

          <Section style={emailStyles.responseBox}>
            <Text style={emailStyles.label}>Latest Responses:</Text>
            <Text style={emailStyles.response}>
              {responses}
            </Text>
          </Section>

          <Button 
            href={`${process.env.FE_URL}/surveys/${surveyId}/responses`}
            style={emailStyles.button}
          >
            View All Responses
          </Button>

          <Hr style={emailStyles.hr} />

          <Text style={emailStyles.footer}>
            You receive this digest daily at midnight.
          </Text>
        </Container>
      </Body>
    </Html>
  )
} 