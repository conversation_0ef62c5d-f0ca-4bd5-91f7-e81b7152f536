import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Preview,
  Section,
  Text,
  Button,
} from '@react-email/components'

import { emailStyles } from '../components/EmailStyles'

export function WeeklyDigestEmail({ surveyTitle, responseCount, responses }) {
  return (
    <Html>
      <Head />
      <Preview>Weekly Digest: {surveyTitle} ({responseCount} responses this week)</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Text style={emailStyles.heading}>
            Weekly Survey Digest
          </Text>

          <Text style={emailStyles.paragraph}>
            Here's your weekly summary for <strong>{surveyTitle}</strong>
          </Text>

          <Section style={emailStyles.statsBox}>
            <Text style={emailStyles.stat}>
              {responseCount} responses this week
            </Text>
          </Section>

          <Section style={emailStyles.responseBox}>
            <Text style={emailStyles.label}>Response Highlights:</Text>
            <Text style={emailStyles.response}>
              {responses}
            </Text>
          </Section>

          <Button 
            href={`${process.env.FE_URL}/surveys/${surveyId}/responses`}
            style={emailStyles.button}
          >
            View All Responses
          </Button>

          <Hr style={emailStyles.hr} />

          <Text style={emailStyles.footer}>
            You receive this digest weekly on Sunday at midnight.
          </Text>
        </Container>
      </Body>
    </Html>
  )
} 