import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components'

import { emailStyles } from '../components/EmailStyles'

export function NotificationEmail({ event, surveyTitle, message }) {
  return (
    <Html>
      <Head />
      <Preview>Survey Notification: {event}</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Text style={emailStyles.heading}>
            Survey Notification
          </Text>

          <Text style={emailStyles.paragraph}>
            <strong>{surveyTitle}</strong>
          </Text>

          <Text style={emailStyles.paragraph}>
            Event: {event}
          </Text>

          <Section style={emailStyles.messageBox}>
            <Text style={emailStyles.message}>
              {message}
            </Text>
          </Section>

          <Hr style={emailStyles.hr} />

          <Text style={emailStyles.footer}>
            You received this email because you have notifications enabled for this survey.
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Add preview props for testing
NotificationEmail.PreviewProps = {
  event: "New Response",
  surveyTitle: "Customer Feedback Survey",
  message: "You have received a new response to your survey!"
} 