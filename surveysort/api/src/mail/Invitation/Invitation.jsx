import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import * as React from "react"
import { emailStyles } from '../components/EmailStyles'

export function InvitationEmail({ inviteUrl, senderEmail, accountName }) {
  return (
    <Html>
      <Head />
      <Preview>Join {accountName} on SurveySort</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Img
            src={`${process.env.FE_URL}/logo.webp`}
            width="170"
            height="50"
            alt="SurveySort"
            style={{ margin: "0 auto" }}
          />
          <Text style={emailStyles.heading}>You've been invited!</Text>
          <Text style={emailStyles.paragraph}>
            {senderEmail} has invited you to join their team on SurveySort.
            Join {accountName} to start collaborating on surveys and analyzing responses together.
          </Text>
          <Section style={{ textAlign: 'center' }}>
            <Button style={emailStyles.button} href={inviteUrl}>
              Accept Invitation
            </Button>
          </Section>
          <Text style={emailStyles.paragraph}>
            This invitation will expire in 48 hours.
            If you weren't expecting this invitation, you can safely ignore it.
          </Text>
          <Text style={emailStyles.paragraph}>
            Best,
            <br />
            The SurveySort team
          </Text>
          <Hr style={emailStyles.hr} />
          <Text style={emailStyles.footer}>
            SurveySort Technologies Inc.
            <br />
            2093 Philadelphia Pike #3222, Claymont, DE 19703
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Add preview props for testing
InvitationEmail.PreviewProps = {
  inviteUrl: "https://example.com/accept-invitation?token=123",
  senderEmail: "<EMAIL>",
  accountName: "Acme Inc"
} 