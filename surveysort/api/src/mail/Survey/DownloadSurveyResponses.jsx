import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { emailStyles } from '../components/EmailStyles';

export function DownloadSurveyResponses({ downloadLink }) {
  return (
    <Html>
      <Head />
      <Preview>Download your survey responses</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Text style={emailStyles.heading}>🪄 Your download link is ready</Text>
          <Section>
            <Text style={emailStyles.paragraph}>
              <Link href={downloadLink} style={emailStyles.button}>
                Download Survey Responses
              </Link>
            </Text>
            <Text style={emailStyles.paragraph}>
              Please note: This link is valid for the next 30 minutes.
            </Text>
            <Text style={emailStyles.paragraph}>
              If you didn't request this, please ignore this email.
            </Text>
          </Section>
          <Text style={emailStyles.paragraph}>
            Best,
            <br />
            The SurveySort team
          </Text>
          <Hr style={emailStyles.hr} />
          <Text style={emailStyles.footer}>
            SurveySort Technologies Inc.
            <br />
            2093 Philadelphia Pike #3222, Claymont, DE 19703
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

export default DownloadSurveyResponses;
