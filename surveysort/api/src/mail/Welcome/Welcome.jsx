import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";
import { emailStyles } from '../components/EmailStyles'

export function WelcomeEmail() {
  return (
    <Html>
      <Head />
      <Preview>
        The best way to create and analyze surveys
      </Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Img
            src={`${process.env.FE_URL}/logo.webp`}
            width="170"
            height="50"
            alt="SurveySort"
            style={{ margin: "0 auto" }}
          />
          <Text style={emailStyles.heading}>Welcome!</Text>
          <Text style={emailStyles.paragraph}>
            Welcome to SurveySort, the best way to create and analyze surveys.
          </Text>
          <Section style={{ textAlign: "center" }}>
            <Button style={emailStyles.button} href={process.env.FE_URL}>
              Start Now
            </Button>
          </Section>
          <Text style={emailStyles.paragraph}>
            Best,
            <br />
            The SurveySort team
          </Text>
          <Hr style={emailStyles.hr} />
          <Text style={emailStyles.footer}>
            The best way to create and analyze surveys
          </Text>
        </Container>
      </Body>
    </Html>
  );
}



const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
};

const logo = {
  margin: "0 auto",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
};

const btnContainer = {
  textAlign: "center",
};

const button = {
  backgroundColor: "#5F51E8",
  borderRadius: "3px",
  color: "#fff",
  fontSize: "16px",
  textDecoration: "none",
  textAlign: "center",
  display: "block",
  padding: "12px",
};

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
};

const footer = {
  color: "#8898aa",
  fontSize: "12px",
};

