import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import * as React from "react"
import { emailStyles } from '../components/EmailStyles'

export function PasswordResetEmail({ resetUrl }) {
  return (
    <Html>
      <Head />
      <Preview>Reset your SurveySort password</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Img
            src={`${process.env.FE_URL}/logo.webp`}
            width="170"
            height="50"
            alt="SurveySort"
            style={{ margin: "0 auto" }}
          />
          <Text style={emailStyles.heading}>Reset your password</Text>
          <Text style={emailStyles.paragraph}>
            Please click the button below to reset your password:
          </Text>
          <Section style={{ textAlign: 'center' }}>
            <Button style={emailStyles.button} href={resetUrl}>
              Reset Password
            </Button>
          </Section>
          <Text style={emailStyles.paragraph}>
            This reset link will expire in 24 hours.
            If you did not request this password reset, please ignore this email.
          </Text>
          <Text style={emailStyles.paragraph}>
            Best,
            <br />
            The SurveySort team
          </Text>
          <Hr style={emailStyles.hr} />
          <Text style={emailStyles.footer}>
            SurveySort Technologies Inc.
            <br />
            2093 Philadelphia Pike #3222, Claymont, DE 19703
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Add preview props for testing
PasswordResetEmail.PreviewProps = {
  resetUrl: "https://example.com/reset-password?token=123",
}; 