import * as React from "react"

export const emailStyles = {
  main: {
    backgroundColor: "rgba(250, 249, 247, 0.98)",
    fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
  },
  container: {
    margin: "0 auto",
    padding: "20px 25px 48px",
  },
  heading: {
    fontSize: "28px",
    fontWeight: "bold",
    color: "rgba(255, 124, 0, 1)",
    marginTop: "48px",
  },
  paragraph: {
    fontSize: "16px",
    lineHeight: "26px",
    color: "rgba(68, 64, 60, 0.95)",
  },
  button: {
    backgroundColor: "rgba(255, 124, 0, 1)",
    color: "rgba(255, 255, 255, 0.95)",
    borderRadius: "8px",
    fontSize: "16px",
    textDecoration: "none",
    textAlign: "center",
    display: "block",
    padding: "12px 24px",
    margin: "24px auto",
  },
  hr: {
    borderColor: "rgba(231, 229, 228, 0.95)",
    margin: "32px 0",
  },
  footer: {
    color: "rgba(120, 53, 15, 0.95)",
    fontSize: "12px",
    textAlign: "center",
  }
} 