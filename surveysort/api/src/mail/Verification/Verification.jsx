import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import * as React from "react"
import { emailStyles } from '../components/EmailStyles'

export function VerificationEmail({ verificationUrl }) {
  return (
    <Html>
      <Head />
      <Preview>Verify your email address for SurveySort</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <Img
            src={`${process.env.FE_URL}/logo.webp`}
            width="170"
            height="50"
            alt="SurveySort"
            style={{ margin: "0 auto" }}
          />
          <Text style={emailStyles.heading}>Welcome to SurveySort!</Text>
          <Text style={emailStyles.paragraph}>
            Please verify your email address by clicking the button below:
          </Text>
          <Section style={{ textAlign: 'center' }}>
            <Button style={emailStyles.button} href={verificationUrl}>
              Verify Email Address
            </Button>
          </Section>
          <Text style={emailStyles.paragraph}>
            This verification link will expire in 48 hours.
            If you did not create this account, please ignore this email.
          </Text>
          <Text style={emailStyles.paragraph}>
            Best,
            <br />
            The SurveySort team
          </Text>
          <Hr style={emailStyles.hr} />
          <Text style={emailStyles.footer}>
            SurveySort Technologies Inc.
            <br />
            2093 Philadelphia Pike #3222, Claymont, DE 19703
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Add preview props for testing
VerificationEmail.PreviewProps = {
  verificationUrl: "https://example.com/verify/token123",
}; 