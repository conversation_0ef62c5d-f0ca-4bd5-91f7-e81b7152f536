// api/src/functions/redirectGcs.js

import { logger } from 'src/lib/logger'
import { context } from '@redwoodjs/api'

export const handler = async (event, _context) => {
  logger.info(`${event.httpMethod} ${event.path} function redirectGcs`)

  const { url, contentType } = event.queryStringParameters

  if (!url || !contentType) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Missing URL or Content-Type' }),
    }
  }

  return {
    statusCode: 302, // Redirect status code
    headers: {
      'Content-Type': contentType,
      Location: url,
    },
    body: '', // No body needed for a redirect
  }
}
