import { WebhookVerificationError } from '@redwoodjs/graphql-server'

import { db } from 'src/lib/db'
import { logger } from 'src/lib/logger'
import stripe from 'src/lib/stripe'
import { transactionalQueue } from 'src/queue/index'

export const handler = async (event, _context) => {
  logger.info(
    `${event.httpMethod} ${event.path}: listenToStripeEvents function`
  )
  const webhookInfo = { webhook: 'listenToStripeEvents' }
  const webhookLogger = logger.child({ webhookInfo })
  webhookLogger.trace('>> in listenToStripeEvents')

  const sig = event.headers['stripe-signature']

  let stripeEvent

  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SK
    stripeEvent = stripe.webhooks.constructEvent(event.body, sig, webhookSecret)
  } catch (err) {
    throw new WebhookVerificationError(err.message)
  }

  try {
    const { customer } = stripeEvent.data.object
    const subscription = stripeEvent.data.object

    const account = await db.account.findFirst({
      where: { stripeCustomerId: customer },
    })

    if (!account) return { statusCode: 200 }

    switch (stripeEvent.type) {
      case 'customer.subscription.created':
        // New subscription created (including trial)
        await db.account.update({
          where: { id: account.id },
          data: { 
            trialStartedAt: subscription.trial_end ? new Date() : null,
            trialEndsAt: subscription.trial_end ? 
              new Date(subscription.trial_end * 1000) : null,
            currentPlanId: subscription.items.data[0].price.product.id
          }
        })
        break

      case 'customer.subscription.trial_will_end':
        // Send trial ending notification
        await transactionalQueue.add('sendTrialEndingEmail', {
          accountId: account.id,
          daysLeft: 3
        })
        break

      case 'customer.subscription.updated':
        // Handle plan changes and cancellations
        await db.account.update({
          where: { id: account.id },
          data: { 
            currentPlanId: subscription.items.data[0].price.product.id,
            cancelScheduledFor: subscription.cancel_at_period_end ? 
              new Date(subscription.current_period_end * 1000) : null
          }
        })
        break

      case 'customer.subscription.deleted':
        // Handle subscription ended
        await db.account.update({
          where: { id: account.id },
          data: { 
            currentPlanId: null,
            trialEndsAt: null,
            trialStartedAt: null,
            cancelScheduledFor: null
          }
        })
        break

      case 'invoice.payment_failed':
        // Handle failed payments
        await db.account.update({
          where: { id: account.id },
          data: { accessRestricted: true }
        })
        
        await transactionalQueue.add('sendPaymentFailedEmail', {
          accountId: account.id
        })
        break

      case 'invoice.payment_succeeded':
        // Clear any payment restrictions
        await db.account.update({
          where: { id: account.id },
          data: { accessRestricted: false }
        })
        break
    }

    return { statusCode: 200 }
  } catch (error) {
    logger.error(`Error processing webhook event: ${error.message}`)
    return { statusCode: 500, body: error.message }
  }
}
