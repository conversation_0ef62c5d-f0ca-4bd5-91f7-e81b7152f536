import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@redwoodjs/auth-dbauth-api'
import { hashToken } from '@redwoodjs/auth-dbauth-api'
import { db } from 'src/lib/db'
import { cookieName, generateToken } from 'src/lib/auth'
import { isEmailAllowed, createUnverifiedUser } from 'src/services/accounts/accounts'
import { transactionalQueue } from 'src/queue/index'
import { logger } from 'src/lib/logger'


export const handler = async (event, context) => {
  const forgotPasswordOptions = {
    handler: async (user) => {
      logger.info(`Forgot password request for user ${user.email}`)
      
      try {
        // Check if user exists and is verified
        const existingUser = await db.user.findUnique({
          where: { 
            email: user.email,
            isDeleted: false
          }
        })

        if (!existingUser) {
          logger.info(`No user found for password reset: ${user.email}`)
          return { email: user.email } // Still return success for security
        }

        // Generate reset token like verification token
        const resetToken = generateToken()
        const hashedResetToken = hashToken(resetToken)
        const resetTokenExpiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24) // 24 hours

        // Update user with reset token
        await db.user.update({
          where: { id: existingUser.id },
          data: {
            resetToken: hashedResetToken,
            resetTokenExpiresAt,
          },
        })

        // Send raw token to email task
        await transactionalQueue.add('companySendsPasswordResetLink', {
          userId: existingUser.id,
          emailAddress: user.email,
          resetToken: resetToken // Send raw token
        })
        
        return {
          email: user.email
        }
      } catch (error) {
        logger.error('Error in forgotPassword', { error, email: user.email })
        throw error
      }
    },
    expires: 60 * 60 * 24, // 24 hours
    errors: {
      usernameNotFound: 'If an account matches, we will send reset instructions to that email address.',
      usernameRequired: 'Email is required',
    },
  }

  const loginOptions = {
    expires: 60 * 60 * 24 * 365 * 10,
    handler: async (user) => {
      logger.info(`Login attempt for user ${user.email}`)
      
      if (!user.isVerified) {
        logger.warn(`Unverified user attempted login: ${user.email}`)
        throw new Error('Please verify your email address. Check your inbox for the verification link.')
      }
      
      logger.info(`Successful login for user ${user.email}`)
      return user
    },
    errors: {
      usernameOrPasswordMissing: 'Both username and password are required',
      usernameNotFound: 'Username ${username} not found',
      incorrectPassword: 'Incorrect password for ${username}',
    },
  }

  const resetPasswordOptions = {
    handler: async (user) => {
      return user
      // console.log('user', user)
      // logger.info('Reset password attempt', { 
      //   userId: user.id,
      //   hasResetToken: !!user.resetToken,
      //   resetToken: user.resetToken
      // })
      
      // try {
      //   if (!user.resetToken) {
      //     logger.error('No reset token provided', { userId: user.id })
      //     throw new Error('Reset token is required')
      //   }

      //   // Find user with matching hashed token
      //   const hashedToken = hashToken(user.resetToken)
      //   console.log('hashedToken', hashedToken)
      //   const validUser = await db.user.findFirst({
      //     where: {
      //       id: user.id,
      //       resetToken: hashedToken,
      //       resetTokenExpiresAt: {
      //         gt: new Date(),
      //       },
      //     },
      //   })

      //   if (!validUser) {
      //     logger.error('Invalid or expired reset token', { userId: user.id })
      //     throw new Error('This reset link is invalid or has expired')
      //   }

      //   // Clear the reset token after successful reset
      //   await db.user.update({
      //     where: { id: user.id },
      //     data: {
      //       resetToken: null,
      //       resetTokenExpiresAt: null,
      //     },
      //   })

      //   logger.info('Password reset successful', { userId: user.id })
      //   return true
      // } catch (error) {
      //   logger.error('Error in resetPassword', { error, userId: user.id })
      //   throw error
      // }
    },
    allowReusedPassword: false,
    errors: {
      resetTokenExpired: 'This reset link has expired. Please request a new one.',
      resetTokenInvalid: 'This reset link is invalid. Please request a new one.',
      resetTokenRequired: 'Reset token is required',
      reusedPassword: 'Must choose a new password',
    },
  }

  const signupOptions = {
    handler: async ({ username, hashedPassword, salt }) => {
      logger.info(`Starting signup process for ${username}`)

      if (!isEmailAllowed(username)) {
        logger.warn(`Signup blocked - personal email domain used: ${username}`)
        return { error: 'Only business email addresses are allowed. Personal email domains are not accepted.' }
      }
      
      const existingUser = await db.user.findFirst({
        where: { 
          email: username,
          isDeleted: false 
        }
      })

      if (existingUser) {
        logger.info(`Existing user found: ${existingUser.id}`)
        if (!existingUser.isVerified) {
          logger.info(`Resending verification for existing unverified user: ${username}`)
          
          const verificationToken = generateToken()
          const verificationTokenExpiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 2)

          await db.user.update({
            where: { id: existingUser.id },
            data: {
              verificationToken,
              verificationTokenExpiresAt,
            },
          })

          await transactionalQueue.add('companySendsVerificationLink', {
            userId: existingUser.id,
            emailAddress: username,
            verificationToken,
          })

          return { message: 'We sent you another verification email. Please check your inbox.' }
        } else {
          logger.warn(`Signup attempted with existing verified email: ${username}`)
          return { error: 'This email is already registered.' }
        }
      }

      // Create new unverified user
      const verificationToken = generateToken()
      const verificationTokenExpiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 2)

      const newUser = await createUnverifiedUser({
        email: username,
        hashedPassword,
        salt,
        verificationToken,
        verificationTokenExpiresAt
      })

      // Queue verification email
      await transactionalQueue.add('companySendsVerificationLink', {
        userId: newUser.id,
        emailAddress: username,
        verificationToken,
      })

      return {}
    },
    errors: {
      fieldMissing: '${field} is required',
    },
  }

  const authHandler = new DbAuthHandler(event, context, {
    db: db,
    authModelAccessor: 'user',
    authFields: {
      id: 'id',
      username: 'email',
      hashedPassword: 'hashedPassword',
      salt: 'salt',
      resetToken: 'resetToken',
      resetTokenExpiresAt: 'resetTokenExpiresAt',
    },
    cookie: {
      name: cookieName,
      HttpOnly: true,
      Path: '/',
      SameSite: 'Strict',
      Secure: process.env.NODE_ENV !== 'development',
    },
    forgotPassword: forgotPasswordOptions,
    login: loginOptions,
    resetPassword: resetPasswordOptions,
    signup: signupOptions,
  })

  return await authHandler.invoke()
}
