import { createAuthDecoder } from '@redwoodjs/auth-dbauth-api'
import { createGraphQLHandler } from '@redwoodjs/graphql-server'

import directives from 'src/directives/**/*.{js,ts}'
import sdls from 'src/graphql/**/*.sdl.{js,ts}'
import services from 'src/services/**/*.{js,ts}'

import { cookieName, getCurrentUser } from 'src/lib/auth'
import { db } from 'src/lib/db'
import { logger } from 'src/lib/logger'

const authDecoder = createAuthDecoder(cookieName)

export const handler = createGraphQLHandler({
  authDecoder,
  getCurrentUser,
  loggerConfig: { logger, options: {} },
  directives,
  sdls,
  services,
  armorConfig: {
    maxDepth: {
      n: 10,
    },
  },
  onException: () => {
    // Disconnect from your database with an unhandled exception.
    db.$disconnect()
  },
  formatError: (err) => {
    // Return sanitized error for the client
    if (typeof (err) !== 'string') {
      return {
        message: error.extensions?.code === 'INTERNAL_SERVER_ERROR'
          ? 'Something went wrong. Please try again later.'
          : error.message,
        extensions: error.extensions || {},
      };
    }
    return err;
  },
})
