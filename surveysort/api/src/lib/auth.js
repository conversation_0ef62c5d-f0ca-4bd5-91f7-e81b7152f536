import { AuthenticationError, ForbiddenError } from '@redwoodjs/graphql-server'
import { db } from './db'
import { logger } from './logger'
import stripe from 'src/lib/stripe'
import crypto from 'crypto'
import { TRIAL_LIMITS } from 'src/services/billing/billing'

/**
 * The name of the cookie that dbA<PERSON> sets
 *
 * %port% will be replaced with the port the api server is running on.
 * If you have multiple RW apps running on the same host, you'll need to
 * make sure they all use unique cookie names
 */
export const cookieName = 'session_%port%'

/**
 * The session object sent in as the first argument to getCurrentUser() will
 * have a single key `id` containing the unique ID of the logged in user
 * (whatever field you set as `authFields.id` in your auth function config).
 * You'll need to update the call to `db` below if you use a different model
 * name or unique field name, for example:
 *
 *   return await db.profile.findUnique({ where: { email: session.id } })
 *                   ───┬───                       ──┬──
 *      model accessor ─┘      unique id field name ─┘
 *
 * !! BEWARE !! Anything returned from this function will be available to the
 * client--it becomes the content of `currentUser` on the web side (as well as
 * `context.currentUser` on the api side). You should carefully add additional
 * fields to the `select` object below once you've decided they are safe to be
 * seen if someone were to open the Web Inspector in their browser.
 */
export const getCurrentUser = async (session) => {

  if (!session || !session.id) {
    logger.warn('Invalid or missing session')
    return null
  }

  try {
    const user = await db.user.findUnique({
      where: { id: session.id },
      select: { 
        id: true, 
        email: true,
        isVerified: true,
        props: true,
        userRoles: {
          include: {
            role: true,
            account: {
              select: {
                id: true,
                name: true,
                stripeCustomerId: true
              }
            }
          }
        }
      },
    })

    if (!user) {
      logger.warn('User not found for session', { sessionId: session.id })
      return null
    }

    // Get the first user role and account
    const userRole = user.userRoles?.[0]
    if (!userRole) {
      throw new Error('No role found for user')
    }

    const account = userRole.account

    // Get subscription status including trial
    let subscriptionStatus = {
      isActive: false,
      onTrial: false,
      trialDaysLeft: 0,
      trialEndsAt: null,
      isPaid: false
    }

    if (account.stripeCustomerId) {
      try {
        const subscriptions = await stripe.subscriptions.list({
          customer: account.stripeCustomerId,
          status: ['active', 'trialing'],
          expand: ['data.items.data.price.product']
        })

        const subscription = subscriptions.data[0]
        
        if (subscription) {
          const basePrice = subscription.items.data.find(
            item => item.price.metadata.type === 'base'
          )
          
          subscriptionStatus = {
            isActive: subscription.status === 'active',
            onTrial: subscription.status === 'trialing',
            trialDaysLeft: subscription.trial_end ? 
              Math.ceil((subscription.trial_end * 1000 - Date.now()) / (1000 * 60 * 60 * 24)) : 0,
            trialEndsAt: subscription.trial_end ? 
              new Date(subscription.trial_end * 1000) : null,
            isPaid: subscription.status === 'active' && !subscription.cancel_at_period_end,
            currentPlan: subscription.items.data[0]?.price.product.name,
            currentPeriodEnd: new Date(subscription.current_period_end * 1000),
            limits: subscription.status === 'trialing' ? 
              TRIAL_LIMITS : 
              {
                aiQuestions: parseInt(basePrice?.price.product.metadata.included_ai_questions || 0),
                responseGradings: parseInt(basePrice?.price.product.metadata.included_gradings || 0),
                insightConversations: parseInt(basePrice?.price.product.metadata.included_conversations || 0)
              },
            currentUsage: await getCurrentUsage(account.id)
          }
        }
      } catch (stripeError) {
        logger.error('Error fetching Stripe subscription:', stripeError)
      }
    }

    return {
      id: user.id,
      email: user.email,
      accountId: account.id,
      isVerified: user.isVerified,
      roles: user.userRoles.map(ur => ur.role.name),
      subscription: subscriptionStatus,
      hasCompletedOnboarding: user.props?.onboardingDone ?? false,
      props: user.props,
    }
  } catch (error) {
    logger.error('Error in getCurrentUser:', error)
    return null
  }
}

/**
 * The user is authenticated if there is a currentUser in the context
 *
 * @returns {boolean} - If the currentUser is authenticated
 */
export const isAuthenticated = () => {

  return !!context.currentUser
}

/**
 * When checking role membership, roles can be a single value, a list, or none.
 * You can use Prisma enums too (if you're using them for roles), just import your enum type from `@prisma/client`
 */

/**
 * Checks if the currentUser is authenticated (and assigned one of the given roles)
 *
 * @param roles: {@link AllowedRoles} - Checks if the currentUser is assigned one of these roles
 *
 * @returns {boolean} - Returns true if the currentUser is logged in and assigned one of the given roles,
 * or when no roles are provided to check against. Otherwise returns false.
 */
export const hasRole = (roles) => {
  if (!isAuthenticated()) {
    return false
  }

  const currentUserRoles = context.currentUser?.roles

  if (typeof roles === 'string') {
    if (typeof currentUserRoles === 'string') {
      return currentUserRoles === roles
    } else if (Array.isArray(currentUserRoles)) {
      return currentUserRoles?.some((allowedRole) => roles === allowedRole)
    }
  }

  if (Array.isArray(roles)) {
    if (Array.isArray(currentUserRoles)) {
      return currentUserRoles?.some((allowedRole) =>
        roles.includes(allowedRole)
      )
    } else if (typeof currentUserRoles === 'string') {
      return roles.some((allowedRole) => currentUserRoles === allowedRole)
    }
  }

  return false
}

/**
 * Use requireAuth in your services to check that a user is logged in,
 * whether or not they are assigned a role, and optionally raise an
 * error if they're not.
 *
 * @param roles: {@link AllowedRoles} - When checking role membership, these roles grant access.
 *
 * @returns - If the currentUser is authenticated (and assigned one of the given roles)
 *
 * @throws {@link AuthenticationError} - If the currentUser is not authenticated
 * @throws {@link ForbiddenError} If the currentUser is not allowed due to role permissions
 *
 * @see https://github.com/redwoodjs/redwood/tree/main/packages/auth for examples
 */
// api/src/lib/auth.js
export const requireAuth = ({ roles } = {}) => {
  if (!isAuthenticated()) {
    throw new AuthenticationError("You don't have permission to do that.")
  }

  // Add verification check here
  if (!context.currentUser?.isVerified) {
    throw new ForbiddenError("Please verify your email first.")
  }

  if (roles && !hasRole(roles)) {
    throw new ForbiddenError("You don't have access to do that.")
  }
}

export const generateToken = () => {
  return crypto.randomBytes(32).toString('hex')
}
