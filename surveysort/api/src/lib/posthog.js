import { PostHog } from 'posthog-node'

const POSTHOG_KEY = 'phc_2UUcHSdZYX1452aTm188fHgtjOyNxIE3kGiOPVxhZ9p'

const isDev = process.env.NODE_ENV !== 'production'

const client = new PostHog(POSTHOG_KEY, { 
  host: 'https://us.i.posthog.com',
  flushAt: 1,
  flushInterval: 0,
  personalApiKey: POSTHOG_KEY,
  preloadFeatureFlags: true,
  featureFlagsPollingInterval: 10000
})

export const getAllFeatureFlags = async (userId, properties = {}) => {
  try {
    const flags = await client.getAllFlags(userId, {
      personProperties: {
        ...properties,
        isDev,
        isProd: !isDev,
        environment: process.env.NODE_ENV
      },
      sendFeatureFlagEvents: true
    })
    return flags
  } catch (error) {
    console.error('Error getting all feature flags:', error)
    return {} 
  }
}

export const capture = (params) => {
  return client.capture({
    ...params,
    properties: {
      ...params.properties,
      isDev,
      isProd: !isDev,
      environment: process.env.NODE_ENV
    }
  })
}

export default client 