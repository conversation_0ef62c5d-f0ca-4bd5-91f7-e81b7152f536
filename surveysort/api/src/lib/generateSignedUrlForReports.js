import { transactionalQueue } from "src/queue/index";
import { generatePresignedUrl } from "src/services/uploads/gcpUtils";
import { db } from './db'
import { logger } from '$api/src/lib/logger'

async function getPresignedUrlFromGCP({ fileName, fileType = 'application/pdf' }) {
  const bucketName = process.env.SURVEY_SORT_USER_UPLOADS_BUCKET_NAME;
  return generatePresignedUrl(bucketName, fileName, fileType, null, 'read', 60 * 60 * 1000);
}



export const generateSignedUrlForReports = async (data) => {
  const { uuid } = data;

  const reportExportRequest = await db.reportExportRequest.findUnique({
    where: {
      id:uuid,
    },
  });

  if (!reportExportRequest) {
    throw new Error("ReportExportRequest not found in DB");
  }

  const { downloadUrl } = reportExportRequest;

  const fileName = downloadUrl.split('/').pop();
  const fileType = fileName.endsWith('.pdf') ? 'application/pdf' : fileName.endsWith('.xlsx') ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'application/octet-stream';
   
  const {signedUrl} = await getPresignedUrlFromGCP({fileName: downloadUrl, fileType});
    if(!signedUrl || !reportExportRequest.createdBy || reportExportRequest.createdBy.length <=0 ) {
      throw new Error("ReportExportRequest createdBy not found");
    }
    await transactionalQueue.add('sendDownloadSurveyResponsesEmailLink', {
      emailAddress: reportExportRequest.createdBy,
      downloadLink: signedUrl,
    })
}
