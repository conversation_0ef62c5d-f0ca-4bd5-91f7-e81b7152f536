import { Mailer } from '@redwoodjs/mailer-core'
import { Nodemailer<PERSON>ailHandler } from '@redwoodjs/mailer-handler-nodemailer'
import { ReactEmailRenderer } from '@redwoodjs/mailer-renderer-react-email'
import { ResendMailHandler } from '@redwoodjs/mailer-handler-resend'

import { logger } from 'src/lib/logger'

console.log(process.env.NODE_ENV)

export const mailer = new Mailer({
  handling: {
    handlers: {
      nodeMailer: new NodemailerMailHandler({
        transport: {
          host: 'localhost',
          port: 25,
          secure: false,
        },
      }),
      brevoMailer: new NodemailerMailHandler({
        transport: {
          host: process.env.BREVO_SMTP_RELAY_HOST,
          port: 587,
          secure: false, // true for 465, false for other ports
          auth: {
            user: process.env.BREVO_SMTP_USERNAME,
            pass: process.env.BREVO_SMTP_PASSWORD,
          },
        },
      }),
      resendMailer: new ResendMailHandler({
        apiKey: process.env.RESEND_API_KEY,
      }),
    },
    default: 'resendMailer',
  },

  rendering: {
    renderers: {
      reactEmail: new ReactEmailRenderer(),
    },
    default: 'reactEmail',
  },

  development: {
    when:
      process.env.NODE_ENV === 'development' ||
      process.env.NODE_ENV === 'local',
    handler: 'nodeMailer',
  },

  production: {
    when:
      process.env.NODE_ENV == 'production' ||
      process.env.NODE_ENV === 'staging',
    handler: 'resendMailer',
  },

  defaults: {
    replyTo: process.env.NO_REPLY_FROM_ADDRESS,
  },
  logger,
})
