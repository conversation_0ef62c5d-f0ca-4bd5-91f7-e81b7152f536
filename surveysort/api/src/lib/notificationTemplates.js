import { SurveyResponseEmail } from 'src/mail/Notification/SurveyResponseEmail'
import { DailyDigestEmail } from 'src/mail/Notification/DailyDigestEmail'
import { WeeklyDigestEmail } from 'src/mail/Notification/WeeklyDigestEmail'

// Move formatting helpers here
const formatAnswer = (answer) => {
  if (!answer) return 'No answer provided'
  if (Array.isArray(answer)) return answer.join(', ')
  if (typeof answer === 'object') return JSON.stringify(answer, null, 2)
  return answer.toString()
}

const formatDigestResponses = (responses) => {
  if (!Array.isArray(responses)) {
    responses = [responses]
  }
  
  return responses
    .map((response) => {
      const formattedAnswers = Object.entries(response.answers || {})
        .map(([question, answer]) => {
          return `Q: ${question}\nA: ${formatAnswer(answer)}`
        })
        .join('\n')

      return `Response #${response.id}\nSubmitted: ${new Date(response.createdAt).toLocaleString()}\n${formattedAnswers}\n`
    })
    .join('\n---\n\n')
}

const formatSlackResponses = (responses) => {
  if (!Array.isArray(responses)) {
    responses = [responses]
  }

  return responses
    .map((response) => {
      const answers = Object.entries(response.answers || {})
        .map(([q, a]) => `*${q}*\n${formatAnswer(a)}`)
        .join('\n')
      
      return `*Response #${response.id}*\n${answers}`
    })
    .join('\n\n')
}

export const EmailTemplateData = {
  formatForDigest: (surveyTitle, responseStats, responses) => ({
    surveyTitle,
    responseCount: responseStats.responseCount,
    completionRate: responseStats.completionRate,
    highQualityRate: responseStats.highQualityRate,
    averageCompletionTime: responseStats.averageCompletionTime,
    responses: formatDigestResponses(responses)
  }),

  formatForImmediate: (surveyTitle, surveyId) => ({
    surveyTitle,
    surveyId
  })
}

export const SlackTemplateData = {
  formatForDigest: (surveyTitle, responseStats, responses) => ({
    text: `*${surveyTitle}* - Digest Update`,
    responses: formatSlackResponses(responses),
    stats: `Total: ${responseStats.responseCount} | Completion: ${responseStats.completionRate}%`
  }),

  formatForImmediate: (surveyTitle, response) => ({
    text: `*${surveyTitle}* - New Response`,
    responses: formatSlackResponses([response])
  }),

  formatMessage: ({ event, surveyTitle, message }) => ({
    text: message,
    blocks: [
      {
        type: "header",
        text: {
          type: "plain_text",
          text: `Survey Notification: ${event}`,
        }
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: `*Survey:* ${surveyTitle}\n${message}`
        }
      }
    ]
  })
}

export const notificationTemplates = {
  SURVEY_RESPONSE_RECEIVED: {
    EMAIL: {
      subject: 'New Survey Response',
      Component: SurveyResponseEmail
    },
    WEBHOOK: {
      template: '*New Survey Response*\n\nSurvey: {{surveyTitle}}\n\nResponse details:\n{{responses}}'
    }
  },
  SURVEY_RESPONSE_DAILY_DIGEST: {
    EMAIL: {
      subject: 'Daily Survey Digest',
      Component: DailyDigestEmail
    },
    WEBHOOK: {
      template: '*Daily Survey Digest*\n\nSurvey: {{surveyTitle}}\nTotal today: {{responseCount}}\n\nLatest responses:\n{{responses}}'
    }
  },
  SURVEY_RESPONSE_WEEKLY_DIGEST: {
    EMAIL: {
      subject: 'Weekly Survey Digest',
      Component: WeeklyDigestEmail
    },
    WEBHOOK: {
      template: '*Weekly Survey Digest*\n\nSurvey: {{surveyTitle}}\nTotal this week: {{responseCount}}\n\nHighlights:\n{{responses}}'
    }
  }
} 