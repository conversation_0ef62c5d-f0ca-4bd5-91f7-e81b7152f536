import { fetch } from '@whatwg-node/fetch'
import { logger } from 'src/lib/logger'
import stripe from 'src/lib/stripe'
import {
  getPaidAccountsWithUsage,
  sendVerificationEmail,
  sendWelcomeEmail,
  sendPasswordResetEmail,
} from 'src/services/accounts'
import { getUserProp, PropKey, upsertUserProp } from 'src/services/userProps'

import { transactionalQueue } from 'src/queue/index'
import { reportUsageForAccount } from 'src/services/billing'
import { db } from './db'
import { mailer } from './mailer'
import { InvitationEmail } from 'src/mail/Invitation/Invitation'
import {
  getInvitationWithDetails,
  sendInvitationEmail,
} from 'src/services/invitations'
import { NotificationEmail } from 'src/mail/Notification/NotificationEmail'
import { sendNotificationMessage } from 'src/services/notifications'
import { scheduleDigestNotifications } from 'src/services/notifications'
import { processNotificationJob } from 'src/services/notifications'
import { DownloadSurveyResponses } from 'src/mail/Survey/DownloadSurveyResponses'
export const companySendsVerificationLink = async ({ userId, emailAddress, verificationToken }) => {
  logger.info('Starting companySendsVerificationLink', userId, emailAddress)

  // try {
  const verificationUrl = `${process.env.FE_URL}/verify/${verificationToken}`
  logger.info(`Generated verification URL ${verificationUrl}`)

  const emailResponse = await sendVerificationEmail({
    emailAddress,
    verificationUrl
  })

  // Safely check if messageID exists and is not null/undefined

  const messageID = emailResponse?.messageID
  if (messageID) {
    logger.info('Verification email queued successfully', userId, messageID)
    await upsertUserProp(userId, PropKey.verificationEmailQueued, true)
  } else {
    logger.error(`Vailed to queue verification email ${JSON.stringify(emailResponse)}`)
    throw new Error('Verification email failed - no messageID received')
  }
  // } catch (e) {
  //   logger.error('Error in companySendsVerificationLink', userId, emailAddress, e.message, e.stack)
  //   throw new Error('Error in companySendsVerificationLink: ' + e.message)
  // }
}

export const companySendsWelcomeEmail = async ({ userId, emailAddress }) => {
  logger.info('Starting companySendsWelcomeEmail', { userId, emailAddress })

  try {
    const welcomeEmailQueued = await getUserProp(userId, PropKey.welcomeEmailQueued)
    if (welcomeEmailQueued) {
      logger.info('Welcome email already queued, skipping...', { userId })
      return
    }

    const user = await db.user.findUnique({
      where: { id: userId },
      select: { email: true }
    })

    const emailResponse = await sendWelcomeEmail({
      emailAddress: user.email,
    })

    const { handlerInformation } = emailResponse
    const { rejected } = handlerInformation

    if (rejected && rejected.length == 0) {
      logger.info('Welcome email queued successfully', { userId })
      await upsertUserProp(userId, PropKey.welcomeEmailQueued, true)
    } else {
      logger.error('Failed to queue welcome email', { userId, rejected })
    }
  } catch (e) {
    throw new Error('Error in companySendsWelcomeEmail', {
      userId,
      emailAddress,
      error: e.message,
      stack: e.stack
    })
  }
}

export const sendDownloadSurveyResponsesEmailLink = async ({ emailAddress, downloadLink }) => {
  logger.info('Starting sendDownloadSurveyResponsesEmailLink', { emailAddress });

  try {
    await mailer.send(DownloadSurveyResponses({ downloadLink }), {
      to: emailAddress,
      subject: 'Download your filtered survey responses',
      from: process.env.NO_REPLY_FROM_ADDRESS || '<EMAIL>',
    });
    logger.info(`sent sendDownloadSurveyResponsesEmailLink to ${emailAddress}`, { emailAddress });
  } catch (e) {
    logger.error('Error sending download survey responses email', {
      emailAddress,
      error: e.message,
      stack: e.stack
    })
    throw e
  }
}

export const reportBulkUsage = async () => {
  logger.info('Starting reportBulkUsage')

  try {
    const paidAccounts = await getPaidAccountsWithUsage()

    for (const account of paidAccounts) {
      try {
        await reportUsageForAccount(account.id)
      } catch (error) {
        logger.error('Failed to report usage for account', {
          accountId: account.id,
          error: error.message
        })
      }
    }
  } catch (error) {
    logger.error('Error in reportBulkUsage', { error })
    throw error
  }
}

export const companySendsPasswordResetLink = async ({ userId, emailAddress, resetToken }) => {
  logger.info('Starting companySendsPasswordResetLink', { userId, emailAddress })

  try {
    const resetUrl = `${process.env.FE_URL}/reset-password?resetToken=${resetToken}`
    logger.debug('Reset URL generated', { resetUrl })

    const emailResponse = await sendPasswordResetEmail({
      emailAddress,
      resetUrl
    })

    const { handlerInformation } = emailResponse
    const { rejected } = handlerInformation

    if (rejected && rejected.length == 0) {
      logger.info('Password reset email queued successfully', { userId })
    } else {
      logger.error('Failed to queue password reset email', { userId, rejected })
    }
  } catch (e) {
    throw new Error('Error in passwordResetTask', {
      userId,
      emailAddress,
      error: e.message,
      stack: e.stack
    })
  }
}

export const companySendsInvitation = async ({ invitationId, recipientEmail }) => {
  try {
    logger.info('Starting companySendsInvitation', { invitationId, recipientEmail })

    const invitation = await getInvitationWithDetails(invitationId)
    await sendInvitationEmail(invitation, invitation.token)

    logger.info('Completed companySendsInvitation', { invitationId })
  } catch (error) {
    logger.error('Error in companySendsInvitation task', {
      error,
      invitationId,
      recipientEmail
    })
    throw error
  }
}

export const adminSendsInvitationToUser = async ({ invitationId, recipientEmail }) => {
  try {
    logger.info('Starting adminSendsInvitationToUser', { invitationId, recipientEmail })

    const invitation = await getInvitationWithDetails(invitationId)
    const inviteUrl = `${process.env.FE_URL}/accept-invitation?token=${invitation.token}`
    await mailer.send(
      InvitationEmail({
        inviteUrl,
        senderEmail: invitation.sender.email,
        accountName: invitation.account.name,
      }),
      {
        to: recipientEmail,
        subject: `Join ${invitation.account.name} on SurveySort`,
        from: process.env.NO_REPLY_FROM_ADDRESS,
      }
    )

    logger.info('Completed adminSendsInvitationToUser', { invitationId })
  } catch (error) {
    logger.error('Error in adminSendsInvitationToUser task', {
      error,
      invitationId,
      recipientEmail
    })
    throw error
  }
}

export const sendNotification = async (job) => {
  try {
    await processNotificationJob(job)
  } catch (error) {
    logger.error('Notification task failed', {
      jobId: job.id,
      error: error.message
    })
    throw error
  }
}

export const sendDailyDigest = async () => {
  const rules = await db.notificationRule.findMany({
    where: {
      frequency: 'DAILY',
      isActive: true
    },
    include: {
      survey: true,
      user: true
    }
  })

  for (const rule of rules) {
    // Get last 24 hours of responses
    const responses = await db.surveyResponse.findMany({
      where: {
        surveyId: rule.surveyId,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    })

    if (responses.length > 0) {
      await sendNotificationMessage({
        ruleId: rule.id,
        accountId: rule.accountId,
        userId: rule.userId,
        event: 'SURVEY_RESPONSE_DAILY_DIGEST',
        surveyTitle: rule.survey.title,
        responseCount: responses.length,
        responses: formatDigestResponses(responses),
        channel: rule.channel,
        emailAddress: rule.user?.email
      })
    }
  }
}

export const sendWeeklyDigest = async () => {
  const rules = await db.notificationRule.findMany({
    where: {
      frequency: 'WEEKLY',
      isActive: true
    },
    include: {
      survey: true,
      user: true
    }
  })

  for (const rule of rules) {
    // Get last 7 days of responses
    const responses = await db.surveyResponse.findMany({
      where: {
        surveyId: rule.surveyId,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    })

    if (responses.length > 0) {
      await sendNotificationMessage({
        ruleId: rule.id,
        accountId: rule.accountId,
        userId: rule.userId,
        event: 'SURVEY_RESPONSE_WEEKLY_DIGEST',
        surveyTitle: rule.survey.title,
        responseCount: responses.length,
        responses: formatDigestResponses(responses),
        channel: rule.channel,
        emailAddress: rule.user?.email
      })
    }
  }
}

// Helper to format responses for digest
const formatDigestResponses = (responses) => {
  return responses.map(r => ({
    id: r.id,
    submittedAt: r.createdAt,
    answers: r.answers
  }))
}

