import { createLogger } from '@redwoodjs/api/logger'
import pino from 'pino'

/**
 * Configure logging based on environment
 * - Development: Console logging with pretty formatting
 * - Production: Axiom logging with structured output
 */
const getLoggerConfig =  () => {
//   // Development configuration
  if (process.env.NODE_ENV === 'development') {
    return {
      options: { 
        level: 'trace',
        redact: ['host', 'headers.authorization']
      },
      destination: pino.destination({
        sync: true // Synchronous logging in development
      })
    }
  }

  // Production configuration with Axiom transport
  const transport =  pino.transport({
    target: '@axiomhq/pino',
    options: {
    level: 'info',
      dataset: process.env.AXIOM_DATASET,
      token: process.env.AXIOM_TOKEN,
      // Async batch configuration
      maxBatchSize: 1000,        // Maximum number of events to batch
      maxBatchAge: 1000,         // Maximum age of a batch in ms
      sync: false,               // Enable async logging
      minLength: 4096,           // <PERSON><PERSON><PERSON> before writing
      maxRetries: 3,             // Number of retries on failed sends
    }
  })

  return {
    options: {
      level: 'info',
      redact: ['host', 'headers.authorization']
    },
    destination: transport
  }
}

/**
 * Initialize logger with async configuration
 * Falls back to synchronous console logging if initialization fails
 */
const initializeLogger =  () => {
  try {
    const config =  getLoggerConfig()
    return createLogger(config)
  } catch (error) {
    console.error('Failed to initialize logger:', error)
    // Fallback to synchronous console logging
    return createLogger({
      options: { 
        level: 'info',
        redact: ['host', 'headers.authorization']
      },
      destination: pino.destination({ sync: true })
    })
  }
}

// Create and export the logger
export const logger =  initializeLogger()

// Clean up transport on process exit
process.on('beforeExit', async () => {
  if (logger?.destination) {
    // Ensure all buffered logs are written
    await logger.destination.flushSync()
    await logger.destination.end()
  }
})
