import { logger } from 'src/lib/logger'
import { SlackTemplateData } from './notificationTemplates'

export const validateWebhook = async (webhookUrl) => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: "Testing webhook connection from SurveySort",
        blocks: [{
          type: "section",
          text: {
            type: "mrkdwn",
            text: "🔔 *Test notification*\nThis is a test message to verify the webhook configuration."
          }
        }]
      })
    })

    return response.ok
  } catch (error) {
    logger.error('Webhook validation failed', { webhookUrl, error })
    return false
  }
}

export const sendSlackNotification = async ({ event, surveyTitle, message, webhookUrl }) => {
  const slackPayload = SlackTemplateData.formatMessage({ event, surveyTitle, message })
  
  const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(slackPayload)
  })

  if (!response.ok) {
    throw new Error(`Webhook failed: ${response.statusText}`)
  }
} 