import { fetch } from '@whatwg-node/fetch'

export const generateQuestions = async (surveyInput) => {
  try {
    console.log('Generating questions with input:', surveyInput)

    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/survey-generator/generate-from-scratch`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
        body: JSON.stringify({
          title: surveyInput.title,
          audience: surveyInput.audience,
          surveyObjective: surveyInput.surveyObjective,
          additionalContext: surveyInput.additionalContext,
          productName: surveyInput.productName,
          accountId: surveyInput.accountId,
          isGeneratedByAi: true,
          questionTypes: surveyInput.questionTypes,
        }),
      }
    )

    if (!response.ok) {
      throw new Error(`API call failed with status ${response.status}`)
    }

    const data = await response.json()
    return data.surveyId
  } catch (error) {
    console.error('Error generating questions:', error)
    throw new Error('Failed to generate questions')
  }
}

export const getSurveyInsightChatResponse = async (surveyId, accountId, message) => {
  const response = await fetch(`${process.env.AI_SERVICE_URL}/agent/survey_agent_chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      surveyId,
      accountId,
      message,
    }),
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

export const getAssistantSuggestions = async (surveyId, accountId) => {
  const response = await fetch(
    `${process.env.AI_SERVICE_URL}/agent/assistant_suggestions/${surveyId}/${accountId}`,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

// Add this new function to aiService.js

export const fetchSurveyResponseAnalytics = async (surveyId) => {
  try {
    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/survey-response-analytics/${surveyId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
      }
    )

    if (!response.ok) {
      throw new Error(`API call failed with status ${response.status}`)
    }

    const data = await response.json()

    return data
  } catch (error) {
    console.error('Error fetching survey analytics:', error)
    throw new Error('Failed to fetch survey analytics')
  }
}

export const fetchSurveyQualityMetrics = async (surveyId) => {
  try {
    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/survey-quality-analytics/${surveyId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
      }
    )

    if (!response.ok) {
      throw new Error(`API call failed with status ${response.status}`)
    }

    const data = await response.json()

    return data
  } catch (error) {
    console.error('Error fetching survey quality metrics:', error)
    throw new Error('Failed to fetch survey quality metrics')
  }
}

export const generateTemplateBasedQuestions = async (templateInput) => {
  try {
    console.log('Generating template-based questions with input:', templateInput)

    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/survey-generator/generate-from-template`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
        body: JSON.stringify({
          templateId: templateInput.templateId,
          title: templateInput.title,
          audience: templateInput.audience,
          surveyObjective: templateInput.surveyObjective,
          productName: templateInput.productName,
          additionalContext: templateInput.additionalContext,
          accountId: templateInput.accountId,
          questionTypes: templateInput.questionTypes,
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        `API call failed with status ${response.status}: ${
          errorData.message || 'Unknown error'
        }`
      )
    }

    const data = await response.json()
    return data.surveyId
  } catch (error) {
    console.error('Error generating template questions:', error)
    throw new Error(`Failed to generate template questions: ${error.message}`)
  }
}

export const fetchBasicSurveyAnalytics = async (surveyId, timeframe = 'LAST_1_DAY') => {
  try {
    const response = await fetch(
      `${process.env.AI_SERVICE_URL}/analytics/surveys/${surveyId}/basic?timeframe=${timeframe}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.AI_SERVICE_API_KEY}`,
        },
      }
    )

    if (!response.ok) {
      throw new Error(`API call failed with status ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching basic survey analytics:', error)
    throw new Error('Failed to fetch basic survey analytics')
  }
}
