/**
 * @typedef { import("@prisma/client").PrismaClient } PrismaClient
 * @param {{db: PrismaClient}} db
 */
export default async ({ db }) => {
  function isPublicEmail(email) {
    const whitelistedAddress =
      process.env.CLERK_WHITELISTED_ADDRESSES.trim().split(',')
    if (whitelistedAddress.includes(email)) return false
    const blockedDomains = process.env.CLERK_BLOCKED_DOMAINS.trim().split(',')
    const domain = email.split('@')[1].toLowerCase()
    return blockedDomains.includes(domain)
  }

  const users = await db.user.findMany({
    select: {
      id: true,
      email: true,
    },
  })

  let updatedCount = 0

  for (const user of users) {
    const shouldBeCorporateUser = !isPublicEmail(user.email)
    // if (user.isCorporateUser !== shouldBeCorporateUser) {
    //   await db.user.update({
    //     where: { id: user.id },
    //     data: { isCorporateUser: shouldBeCorporateUser },
    //   })
    //   updatedCount++
    // }
  }

  console.log(`Updated isCorporateUser for ${updatedCount} users.`)
}
