// Don't forget to tell <PERSON>rism<PERSON> about your edits to this file using
// `yarn rw prisma migrate dev` or `yarn rw prisma db push`.
// `migrate` is like committing while `push` is for prototyping.
// Read more about both here:
// https://www.prisma.io/docs/orm/prisma-migrate

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = "native"
}

model User {
  id                         String             @id @default(cuid())
  email                      String             @unique
  hashedPassword             String             @default("")
  salt                       String             @default("")
  resetToken                 String?
  resetTokenExpiresAt        DateTime?
  verificationToken          String?
  verificationTokenExpiresAt DateTime?
  isVerified                 Boolean            @default(false)
  createdAt                  DateTime           @default(now())
  invitations                Invitation[]
  userRoles                  UserRole[]
  props                      Json?
  isDeleted                  Boolean            @default(false)
  notificationRules          NotificationRule[]
  notifications              Notification[]
}

model Account {
  id                         String                      @id @default(cuid())
  name                       String
  createdAt                  DateTime                    @default(now())
  userRoles                  UserRole[]
  invitations                Invitation[]
  surveys                    Survey[]
  surveyInsightConversations SurveyInsightConversation[]
  isDeleted                  Boolean                     @default(false)
  checkoutInProgress         Boolean                     @default(false)
  stripeCustomerId           String?
  trialEndsAt                DateTime?
  trialStartedAt             DateTime?                   @default(now())
  hasTrialExpired            Boolean                     @default(false)
  accessRestricted           Boolean                     @default(false)
  cancelScheduledFor         DateTime?
  currentPlanId              String?
  lastUsageReportAt          DateTime?
  notificationRules          NotificationRule[]
  notifications              Notification[]
  ReportExportRequest        ReportExportRequest[]
  suppressionEntries         SuppressionEntry[]
}

enum RoleName {
  ADMIN
  USER
}

model Role {
  id        String     @id @default(cuid())
  name      RoleName   @unique
  userRoles UserRole[]
}

model UserRole {
  id        String  @id @default(cuid())
  userId    String
  user      User    @relation(fields: [userId], references: [id])
  roleId    String
  role      Role    @relation(fields: [roleId], references: [id])
  accountId String
  account   Account @relation(fields: [accountId], references: [id])
  isDeleted Boolean @default(false)

  @@unique([userId, roleId, accountId])
}

model Invitation {
  id             String    @id @default(cuid())
  recipientEmail String
  accountId      String
  account        Account   @relation(fields: [accountId], references: [id])
  senderId       String
  sender         User      @relation(fields: [senderId], references: [id])
  token          String    @unique
  createdAt      DateTime  @default(now())
  expiresAt      DateTime
  acceptedAt     DateTime?
  isDeleted      Boolean   @default(false)
}

model Survey {
  id                                String                     @id @default(cuid())
  title                             String
  description                       String?
  audience                          String
  surveyObjective                   String
  additionalContext                 String?
  productName                       String?
  accountId                         String
  account                           Account                    @relation(fields: [accountId], references: [id])
  status                            SurveyStatus               @default(DRAFT)
  isDeleted                         Boolean?                   @default(false)
  isSuspended                       Boolean?                   @default(false)
  questions                         Question[]
  createdAt                         DateTime                   @default(now())
  updatedAt                         DateTime                   @updatedAt
  shouldColelctEmailFromParticipant Boolean?                   @default(false)
  surveyExternalUploadFilePath      String?
  isGeneratedByAi                   Boolean?                   @default(false)
  source                            SurveySource               @default(CREATED)
  surveyDetails                     Json?
  insightConversation               SurveyInsightConversation?
  settings                          SurveySettings?
  SurveyAnalytics                   SurveyAnalytics?
  notificationRules                 NotificationRule[]
  participants                      SurveyParticipant[]
}

enum SurveySource {
  CREATED
  IMPORTED
  INTEGRATED
}

enum QuestionType {
  MULTIPLE_CHOICE
  ESSAY
  RATING
  SCALE
  SHORT
  SCORING
  RANGE
  DATE_TIME
  IMAGE
  MATRIX
  INSTRUCTION
  EMOTICON
  ACCEPT_DENY
  LIKE_DISLIKE
  CONTACT_DETAILS
  WELCOME_MESSAGE
  THANK_YOU_MESSAGE
}

model Question {
  id             String       @id @default(cuid())
  title          String
  explainer      String?
  type           QuestionType
  required       Boolean      @default(false)
  surveyId       String
  survey         Survey       @relation(fields: [surveyId], references: [id])
  order          Int          @default(0)
  pageNumber     Int          @default(0)
  questionConfig Json?
  isDeleted      Boolean      @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([isDeleted])
  @@index([surveyId, pageNumber, order])
}

enum ReportExportStatus {
  SUBMITTED
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum RequestedPage {
  RESPONSE_ANALYTICS_PAGE
  REPORTS_PAGE
}

model ReportExportRequest {
  id            String             @id @default(uuid())
  surveyId      String
  name          String
  subtitle      String?
  type          String
  status        ReportExportStatus @default(SUBMITTED)
  downloadUrl   String?
  info          Json?
  requestedPage RequestedPage      @default(REPORTS_PAGE)
  accountId     String
  account       Account            @relation(fields: [accountId], references: [id])
  createdBy     String
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  
  @@index([surveyId])
  @@index([accountId])
}

model SurveyResponseEnrichedData {
  id                    String         @id @default(uuid())
  surveyId              String
  surveyResponseId      String         @unique
  surveyResponse        SurveyResponse @relation(fields: [surveyResponseId], references: [id])
  shouldEnrich          Boolean        @default(false)
  lastEnrichmentAttempt DateTime?

  // Authenticity Score Components
  authenticityScore Float?
  vpnDetected       Boolean @default(false)
  proxyDetected     Boolean @default(false)
  ipReputationScore Float?
  fraudRiskScore    Float?

  // Response Quality Score Components
  effortScorePerOpenEndedQuestion                Json?
  relevanceScorePerOpenEndedQuestion             Json?
  responseQualityExplanationPerOpenEndedQuestion Json?
  keystrokePatternScorePerOpenEndedQuestion      Json?

  straightLiningDetected Boolean?
  copyPastedDetected     Boolean?
  timeSpentOnSurvey      Int? // in seconds
  keystrokePatternScore  Float?
  isDuplicate            Boolean  @default(false)

  // Overall Quality score
  overallResponseQualityScore Float?

  // Sentiment score if there's a
  sentimentAttributesPerRelevantQuestion Json?

  // Existing fields
  submittedCountry   String?
  submittedCity      String?
  submittedLatitude  Float?
  submittedLongitude Float?

  // Contact details if there's a question for it
  submissionContactDetails Json?

  // For grading status
  questionStatuses  Json?
  isGradingComplete Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([surveyId])
  @@index([surveyResponseId])
}

model SurveyResponse {
  id                String                      @id @default(uuid())
  surveyId          String
  participantEmail  String?
  participantId     String?
  ipAddress         String
  fingerprint       String
  screenResolution  String
  timezone          String
  userAgent         String
  submissionTime    DateTime
  isComplete        Boolean                     @default(false)
  lastActiveTime    DateTime                    @updatedAt
  enrichedData      SurveyResponseEnrichedData?
  questionResponses Json?

  @@index([surveyId, isComplete])
  @@index([surveyId, fingerprint])
}

model SurveyAnalytics {
  id                           String @id @default(uuid())
  surveyId                     String @unique
  survey                       Survey @relation(fields: [surveyId], references: [id])
  descriptiveStatistics        Json?
  correlations                 Json?
  crossTabs                    Json?
  openEndedThemesPerQuestion   Json?
  openEndedInsightsPerQuestion Json?

  insights             Json?
  recommendations      Json?
  assistantSuggestions Json?
  lastCalculated       DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
}

model Conversation {
  id        String   @id @default(cuid())
  userId    String
  surveyId  String
  messages  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, surveyId])
}

model RW_DataMigration {
  version    String   @id
  name       String
  startedAt  DateTime
  finishedAt DateTime
}

enum SurveyStatus {
  DRAFT
  PUBLISHED
  COMPLETED
}

model SurveyInsightConversation {
  id        String   @id @default(cuid())
  surveyId  String   @unique
  survey    Survey   @relation(fields: [surveyId], references: [id])
  accountId String
  account   Account  @relation(fields: [accountId], references: [id])
  messages  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SurveySettings {
  id                  String  @id @default(cuid())
  surveyId            String  @unique
  survey              Survey  @relation(fields: [surveyId], references: [id])
  disclosure          String?
  customTheme         Json?
  requireEmail        Boolean @default(false)
  showProgressBar     Boolean @default(true)
  showNavigation      Boolean @default(true)
  companyLogo         String?
  removeBranding      Boolean @default(false)
  randomizationGroups Json?
  themeConfig         Json?
  previewImage        String?
  previewTitle        String?
  previewDescription  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SurveyQLibraryCategory {
  id          Int                       @id
  name        String
  description String? // Added description
  questions   SurveyQLibraryQuestions[]
  createdAt   DateTime                  @default(now())
  updatedAt   DateTime                  @updatedAt

  @@index([id])
}

model SurveyQLibraryQuestions {
  id          Int                    @id @default(autoincrement())
  title       String
  type        QuestionType
  description String?
  choices     Json?
  config      Json?
  categoryId  Int
  order       Int                    @default(0)
  library     SurveyQLibraryCategory @relation(fields: [categoryId], references: [id])
  createdAt   DateTime               @default(now())
  updatedAt   DateTime               @updatedAt

  @@index([categoryId])
}

// Add new models for notification system

enum NotificationChannel {
  EMAIL
  WEBHOOK
}

enum NotificationFrequency {
  IMMEDIATE
  DAILY
  WEEKLY
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
}

model NotificationRule {
  id            String                @id @default(cuid())
  surveyId      String
  survey        Survey                @relation(fields: [surveyId], references: [id])
  accountId     String
  account       Account               @relation(fields: [accountId], references: [id])
  userId        String?
  user          User?                 @relation(fields: [userId], references: [id])
  event         String
  channel       NotificationChannel
  frequency     NotificationFrequency
  isActive      Boolean               @default(true)
  template      String?
  webhookUrl    String?
  webhookSecret String?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
  notifications Notification[]

  @@index([surveyId])
  @@index([accountId])
  @@index([userId])
}

model Notification {
  id         String             @id @default(cuid())
  ruleId     String
  rule       NotificationRule   @relation(fields: [ruleId], references: [id])
  accountId  String
  account    Account            @relation(fields: [accountId], references: [id])
  userId     String?
  user       User?              @relation(fields: [userId], references: [id])
  status     NotificationStatus @default(PENDING)
  retryCount Int                @default(0)
  sentAt     DateTime?
  error      String?
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt

  @@index([ruleId])
  @@index([accountId])
  @@index([userId])
}

enum ParticipantStatus {
  PENDING     // never emailed
  SENT        // email delivered (not clicked)
  CLICKED     // link visited
  COMPLETED   // survey submitted
  BOUNCED     // hard bounce
  UNSUBSCRIBED
}

model SurveyParticipant {
  id          String             @id @default(cuid())
  surveyId    String
  survey      Survey             @relation(fields: [surveyId], references: [id])
  email       String
  name        String?            // optional "Jane Doe"
  props       Json?              // arbitrary per‑recipient merge vars
  status      ParticipantStatus  @default(PENDING)
  links       ParticipantLink[]
  firstSentAt DateTime?
  lastSentAt  DateTime?
  respondedAt DateTime?

  @@unique([surveyId, email])
  @@index([surveyId, status])
}

model ParticipantLink {
  id             String            @id @default(cuid())
  participantId  String
  participant    SurveyParticipant @relation(fields: [participantId], references: [id])
  token          String            @unique  // short random slug
  tokenHash      String            // SHA-256 hash of token for secure lookups
  batchId        String?           // Tracks generation batches
  expiresAt      DateTime?
  clickedAt      DateTime?
  createdAt      DateTime          @default(now())

  @@index([participantId])
}

model SuppressionEntry {
  id        String   @id @default(cuid())
  accountId String
  account   Account  @relation(fields: [accountId], references: [id])
  email     String
  reason    String?
  createdAt DateTime @default(now())

  @@unique([accountId, email])
}
