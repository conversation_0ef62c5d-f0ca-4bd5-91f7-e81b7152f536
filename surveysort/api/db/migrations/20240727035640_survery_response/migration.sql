-- CreateTable
CREATE TABLE "SurveyResponseEnrichedData" (
    "id" TEXT NOT NULL,
    "surveyResponseId" TEXT NOT NULL,
    "vpnDetected" BOOLEAN NOT NULL,
    "proxyDetected" BOOLEAN NOT NULL,
    "ipReputationScore" DOUBLE PRECISION NOT NULL,
    "submittedCountry" TEXT,
    "submittedCity" TEXT,
    "submittedLatitude" DOUBLE PRECISION,
    "submittedLongitude" DOUBLE PRECISION,
    "enrichmentTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SurveyResponseEnrichedData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveyResponse" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "participantEmail" TEXT,
    "participantId" TEXT,
    "ipAddress" TEXT NOT NULL,
    "fingerprint" TEXT NOT NULL,
    "screenResolution" TEXT NOT NULL,
    "timezone" TEXT NOT NULL,
    "userAgent" TEXT NOT NULL,
    "submissionTime" TIMESTAMP(3) NOT NULL,
    "isComplete" BOOLEAN NOT NULL DEFAULT false,
    "lastActiveTime" TIMESTAMP(3) NOT NULL,
    "questionResponses" JSONB,

    CONSTRAINT "SurveyResponse_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SurveyResponseEnrichedData_surveyResponseId_key" ON "SurveyResponseEnrichedData"("surveyResponseId");

-- CreateIndex
CREATE INDEX "SurveyResponse_surveyId_isComplete_idx" ON "SurveyResponse"("surveyId", "isComplete");

-- AddForeignKey
ALTER TABLE "SurveyResponseEnrichedData" ADD CONSTRAINT "SurveyResponseEnrichedData_surveyResponseId_fkey" FOREIGN KEY ("surveyResponseId") REFERENCES "SurveyResponse"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
