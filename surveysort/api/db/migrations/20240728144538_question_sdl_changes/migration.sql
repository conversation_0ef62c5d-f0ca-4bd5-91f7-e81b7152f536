/*
  Warnings:

  - You are about to drop the `SurveyQuestion` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropF<PERSON>ign<PERSON>ey
ALTER TABLE "SurveyQuestion" DROP CONSTRAINT "SurveyQuestion_surveyId_fkey";

-- DropTable
DROP TABLE "SurveyQuestion";

-- CreateTable
CREATE TABLE "Question" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "type" "QuestionType" NOT NULL,
    "required" BOOLEAN NOT NULL DEFAULT false,
    "surveyId" TEXT NOT NULL,
    "questionConfig" JSONB,

    CONSTRAINT "Question_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
