/*
  Warnings:

  - You are about to drop the column `accepted` on the `Invitation` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `Invitation` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `UserRole` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,roleId,accountId]` on the table `UserRole` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `recipientEmail` to the `Invitation` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accountId` to the `UserRole` table without a default value. This is not possible if the table is not empty.
  - Added the required column `roleId` to the `UserRole` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "RoleName" AS ENUM ('ADMIN', 'USER', 'MANAGER', 'GUEST');

-- DropIndex
DROP INDEX "User_accountId_key";

-- DropIndex
DROP INDEX "UserRole_name_userId_key";

-- AlterTable
ALTER TABLE "Invitation" DROP COLUMN "accepted",
DROP COLUMN "email",
ADD COLUMN     "acceptedAt" TIMESTAMP(3),
ADD COLUMN     "recipientEmail" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "UserRole" DROP COLUMN "name",
ADD COLUMN     "accountId" TEXT NOT NULL,
ADD COLUMN     "roleId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "Role" (
    "id" TEXT NOT NULL,
    "name" "RoleName" NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserRole_userId_roleId_accountId_key" ON "UserRole"("userId", "roleId", "accountId");

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
