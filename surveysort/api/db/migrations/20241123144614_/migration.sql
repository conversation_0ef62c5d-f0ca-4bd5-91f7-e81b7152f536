/*
  Warnings:

  - Added the required column `updatedAt` to the `Question` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `SurveyQLibraryCategory` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `SurveyQLibraryQuestions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `surveyId` to the `SurveyResponseEnrichedData` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `SurveyResponseEnrichedData` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `SurveySettings` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Question" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "SurveyQLibraryCategory" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "SurveyQLibraryQuestions" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "SurveyResponseEnrichedData" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "surveyId" TEXT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "SurveySettings" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- CreateIndex
CREATE INDEX "SurveyResponseEnrichedData_surveyId_idx" ON "SurveyResponseEnrichedData"("surveyId");

-- CreateIndex
CREATE INDEX "SurveyResponseEnrichedData_surveyResponseId_idx" ON "SurveyResponseEnrichedData"("surveyResponseId");
