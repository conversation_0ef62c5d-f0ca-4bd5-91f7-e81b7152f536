-- AlterTable
ALTER TABLE "Survey" ADD COLUMN     "disclosure" TEXT;

-- CreateTable
CREATE TABLE "SurveySettings" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "disclosure" TEXT,
    "customTheme" JSONB,
    "allowAnonymous" BO<PERSON>EAN NOT NULL DEFAULT false,
    "requireEmail" BOOLEAN NOT NULL DEFAULT false,
    "showProgressBar" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "SurveySettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SurveySettings_surveyId_key" ON "SurveySettings"("surveyId");

-- AddForeignKey
ALTER TABLE "SurveySettings" ADD CONSTRAINT "SurveySettings_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
