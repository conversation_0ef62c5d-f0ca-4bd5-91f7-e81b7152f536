-- CreateTable
CREATE TABLE "SurveyAnalytics" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "descriptiveStatistics" JSONB,
    "correlations" JSONB,
    "regressionAnalysis" JSONB,
    "demographicInsights" JSONB,
    "responseQuality" JSONB,
    "overallMetrics" JSONB,
    "significanceTests" JSONB,
    "openEndedAnalysis" JSONB,
    "insights" JSONB,
    "recommendations" J<PERSON>N<PERSON>,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SurveyAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SurveyAnalytics_surveyId_key" ON "SurveyAnalytics"("surveyId");

-- AddForeignKey
ALTER TABLE "SurveyAnalytics" ADD CONSTRAINT "SurveyAnalytics_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
