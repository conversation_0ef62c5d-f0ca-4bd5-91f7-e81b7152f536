-- CreateEnum
CREATE TYPE "ParticipantStatus" AS ENUM ('PENDING', 'SENT', 'CLICKED', 'COMPLETED', 'BOUNCED', 'UNSUBSCRIBED');

-- CreateTable
CREATE TABLE "SurveyParticipant" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "props" JSONB,
    "status" "ParticipantStatus" NOT NULL DEFAULT 'PENDING',
    "firstSentAt" TIMESTAMP(3),
    "lastSentAt" TIMESTAMP(3),
    "respondedAt" TIMESTAMP(3),

    CONSTRAINT "SurveyParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ParticipantLink" (
    "id" TEXT NOT NULL,
    "participantId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "tokenHash" TEXT NOT NULL,
    "batchId" TEXT,
    "expiresAt" TIMESTAMP(3),
    "clickedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ParticipantLink_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SuppressionEntry" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SuppressionEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SurveyParticipant_surveyId_status_idx" ON "SurveyParticipant"("surveyId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "SurveyParticipant_surveyId_email_key" ON "SurveyParticipant"("surveyId", "email");

-- CreateIndex
CREATE UNIQUE INDEX "ParticipantLink_token_key" ON "ParticipantLink"("token");

-- CreateIndex
CREATE INDEX "ParticipantLink_participantId_idx" ON "ParticipantLink"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "SuppressionEntry_accountId_email_key" ON "SuppressionEntry"("accountId", "email");

-- AddForeignKey
ALTER TABLE "SurveyParticipant" ADD CONSTRAINT "SurveyParticipant_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ParticipantLink" ADD CONSTRAINT "ParticipantLink_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "SurveyParticipant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SuppressionEntry" ADD CONSTRAINT "SuppressionEntry_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
