-- CreateEnum
CREATE TYPE "ReportExportStatus" AS ENUM ('SUBMITTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "RequestedPage" AS ENUM ('RESPONSE_ANALYTICS_PAGE', 'REPOR<PERSON>_PAGE');

-- CreateTable
CREATE TABLE "ReportExportRequest" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "subtitle" TEXT,
    "type" TEXT NOT NULL,
    "status" "ReportExportStatus" NOT NULL DEFAULT 'SUBMITTED',
    "downloadUrl" TEXT,
    "info" JSONB,
    "requestedPage" "RequestedPage" NOT NULL DEFAULT 'REPORTS_PAGE',
    "accountId" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportExportRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ReportExportRequest_surveyId_idx" ON "ReportExportRequest"("surveyId");

-- CreateIndex
CREATE INDEX "ReportExportRequest_accountId_idx" ON "ReportExportRequest"("accountId");

-- AddForeignKey
ALTER TABLE "ReportExportRequest" ADD CONSTRAINT "ReportExportRequest_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
