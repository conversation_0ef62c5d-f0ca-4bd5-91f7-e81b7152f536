/*
  Warnings:

  - The `choices` column on the `SurveyQLibraryQuestions` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Changed the type of `type` on the `SurveyQLibraryQuestions` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "SurveyQLibraryCategory" ADD COLUMN     "description" TEXT;

-- AlterTable
ALTER TABLE "SurveyQLibraryQuestions" ADD COLUMN     "config" JSONB,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 0,
DROP COLUMN "type",
ADD COLUMN     "type" "QuestionType" NOT NULL,
DROP COLUMN "choices",
ADD COLUMN     "choices" JSONB;
