-- CreateTable
CREATE TABLE "SurveyQLibraryCategory" (
    "id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "SurveyQLibraryCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveyQLibraryQuestions" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "choices" TEXT,
    "categoryId" INTEGER NOT NULL,

    CONSTRAINT "SurveyQLibraryQuestions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SurveyQLibraryCategory_id_idx" ON "SurveyQLibraryCategory"("id");

-- CreateIndex
CREATE INDEX "SurveyQLibraryQuestions_categoryId_idx" ON "SurveyQLibraryQuestions"("categoryId");

-- AddForeignKey
ALTER TABLE "SurveyQLibraryQuestions" ADD CONSTRAINT "SurveyQLibraryQuestions_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "SurveyQLibraryCategory"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
