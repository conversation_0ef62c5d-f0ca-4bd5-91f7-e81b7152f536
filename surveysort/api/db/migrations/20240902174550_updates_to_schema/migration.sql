/*
  Warnings:

  - You are about to drop the column `description` on the `Survey` table. All the data in the column will be lost.
  - Added the required column `audience` to the `Survey` table without a default value. This is not possible if the table is not empty.
  - Added the required column `surveyObjective` to the `Survey` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "SurveySource" AS ENUM ('CREATED', 'IMPORTED', 'INTEGRATED');

-- AlterTable
ALTER TABLE "Survey" DROP COLUMN "description",
ADD COLUMN     "additionalContext" TEXT,
ADD COLUMN     "audience" TEXT NOT NULL,
ADD COLUMN     "source" "SurveySource" NOT NULL DEFAULT 'CREATED',
ADD COLUMN     "surveyDetails" JSONB,
ADD COLUMN     "surveyObjective" TEXT NOT NULL;
