/*
  Warnings:

  - You are about to drop the `AISurveySuggestion` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "AISurveySuggestion" DROP CONSTRAINT "AISurveySuggestion_surveyId_fkey";

-- DropTable
DROP TABLE "AISurveySuggestion";

-- CreateTable
CREATE TABLE "SurveySuggestionCreateRequest" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "audience" TEXT NOT NULL,
    "surveyObjective" TEXT NOT NULL,
    "additionalContext" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "accountId" TEXT NOT NULL,

    CONSTRAINT "SurveySuggestionCreateRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SurveySuggestion" (
    "id" TEXT NOT NULL,
    "surveyObjective" TEXT NOT NULL,
    "surveyType" TEXT NOT NULL,
    "audience" TEXT NOT NULL,
    "questionSummary" TEXT NOT NULL,
    "createRequestId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SurveySuggestion_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "SurveySuggestionCreateRequest" ADD CONSTRAINT "SurveySuggestionCreateRequest_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SurveySuggestion" ADD CONSTRAINT "SurveySuggestion_createRequestId_fkey" FOREIGN KEY ("createRequestId") REFERENCES "SurveySuggestionCreateRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
