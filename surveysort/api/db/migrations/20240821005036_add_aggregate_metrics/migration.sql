-- AlterTable
ALTER TABLE "SurveyResponseEnrichedData" ADD COLUMN     "isDuplicate" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "SurveyResponseAggregateMetrics" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "totalResponses" INTEGER NOT NULL,
    "vpnProxyDetectionRate" DOUBLE PRECISION NOT NULL,
    "avgFraudRiskScore" DOUBLE PRECISION NOT NULL,
    "duplicatedResponseRate" DOUBLE PRECISION NOT NULL,
    "avgEffortScore" DOUBLE PRECISION NOT NULL,
    "copyPastedContentRate" DOUBLE PRECISION NOT NULL,
    "straightLiningRate" DOUBLE PRECISION NOT NULL,
    "completionRate" DOUBLE PRECISION NOT NULL,
    "avgTimeSpentOnSurvey" INTEGER NOT NULL,
    "suspiciousKeystrokePatternRate" DOUBLE PRECISION NOT NULL,
    "submittedTooFastRate" DOUBLE PRECISION NOT NULL,
    "avgOverallQualityScore" DOUBLE PRECISION NOT NULL,
    "highQualityPercentage" DOUBLE PRECISION NOT NULL,
    "lowQualityPercentage" DOUBLE PRECISION NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SurveyResponseAggregateMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SurveyResponseAggregateMetrics_surveyId_key" ON "SurveyResponseAggregateMetrics"("surveyId");

-- CreateIndex
CREATE INDEX "SurveyResponseAggregateMetrics_surveyId_idx" ON "SurveyResponseAggregateMetrics"("surveyId");

-- AddForeignKey
ALTER TABLE "SurveyResponseAggregateMetrics" ADD CONSTRAINT "SurveyResponseAggregateMetrics_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
