-- CreateTable
CREATE TABLE "AISurveySuggestion" (
    "id" TEXT NOT NULL,
    "surveyId" TEXT NOT NULL,
    "generatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "generationCount" INTEGER NOT NULL DEFAULT 0,
    "generatedContent" JSONB,

    CONSTRAINT "AISurveySuggestion_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "AISurveySuggestion" ADD CONSTRAINT "AISurveySuggestion_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
