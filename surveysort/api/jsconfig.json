{"compilerOptions": {"noEmit": true, "esModuleInterop": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "skipLibCheck": false, "rootDirs": ["./src", "../.redwood/types/mirror/api/src"], "paths": {"src/*": ["./src/*", "../.redwood/types/mirror/api/src/*"], "types/*": ["./types/*", "../types/*"], "@redwoodjs/testing": ["../node_modules/@redwoodjs/testing/api"]}, "typeRoots": ["../node_modules/@types", "./node_modules/@types"], "types": ["jest"], "jsx": "react-jsx"}, "include": ["src", "../.redwood/types/includes/all-*", "../.redwood/types/includes/api-*", "../types"]}