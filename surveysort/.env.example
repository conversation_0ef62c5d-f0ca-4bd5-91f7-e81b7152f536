# DATABASE_URL=file:./dev.db
# TEST_DATABASE_URL=file:./.redwood/test.db
# PRISMA_HIDE_UPDATE_MESSAGE=true
# LOG_LEVEL=trace
BREVO_API_KEY = ''
BREVO_SMTP_USERNAME = ''
BREVO_SMTP_PASSWORD = ''
BREVO_SMTP_RELAY_HOST = smtp-relay.brevo.com
NO_REPLY_FROM_ADDRESS = ''
IPAPI_KEY = ''
IPQUALITY_SCORE_KEY = ''
ABUSE_IP_DB_KEY= ''
CLERK_WHITELISTED_ADDRESSES=
CLERK_BLOCKED_DOMAINS = gmail.com,yahoo.com,outlook.com,hotmail.com,aol.com,protonmail.com,zoho.com
SURVEY_SORT_UPLOAD_TEMP_FOLDER = uploads
SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_UPLOAD = uploads
SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_DOWNLOAD = downloads
GCP_STORAGE_URL = https://storage.cloud.google.com
GOOGLE_SERVICE_ACCOUNT_KEY= ''
SURVEY_SORT_USER_UPLOADS_BUCKET_NAME=surveysort-dev
SURVEY_SORT_USER_IMAGE_UPLOADS_BUCKET_NAME=surveysort-dev-user-images
BLOCKED_DOMAINS=gmail.com,yahoo.com,outlook.com,hotmail.com,aol.com,protonmail.com,zoho.com
WHITELISTED_EMAILS=''

