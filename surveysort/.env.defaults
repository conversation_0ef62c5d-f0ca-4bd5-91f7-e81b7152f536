# These environment variables will be used by default if you do not create any
# yourself in .env. This file should be safe to check into your version control
# system. Any custom values should go in .env and .env should *not* be checked
# into version control.

# schema.prisma defaults
DATABASE_URL=postgres://redwood_user:password123@localhost:5432/redwood

# location of the test database for api service scenarios (defaults to ./.redwood/test.db if not set)
TEST_DATABASE_URL=postgres://redwood_user:password123@localhost:5432/survey_sort_test_db

# disables Prisma CLI update notifier
PRISMA_HIDE_UPDATE_MESSAGE=true

# Option to override the current environment's default api-side log level
# See: https://redwoodjs.com/docs/logger for level options, defaults to "trace" otherwise.
# Most applications want "debug" or "info" during dev, "trace" when you have issues and "warn" in production.
# Ordered by how verbose they are: trace | debug | info | warn | error | silent
# LOG_LEVEL=debug
BREVO_API_KEY = ''
BREVO_SMTP_USERNAME = ''
BREVO_SMTP_PASSWORD = ''
BREVO_SMTP_RELAY_HOST = smtp-relay.brevo.com
NO_REPLY_FROM_ADDRESS = ''
IPAPI_KEY = ''
IPQUALITY_SCORE_KEY = ''
ABUSE_IP_DB_KEY= ''
CLERK_WHITELISTED_ADDRESSES=
BLOCKED_DOMAINS=gmail.com,yahoo.com,outlook.com,hotmail.com,aol.com,protonmail.com,zoho.com
SURVEY_SORT_UPLOAD_TEMP_FOLDER = uploads
SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_UPLOAD = uploads
SURVEY_SORT_GCP_USER_BUCKET_SUBFOLDER_NAME_DOWNLOAD = downloads
