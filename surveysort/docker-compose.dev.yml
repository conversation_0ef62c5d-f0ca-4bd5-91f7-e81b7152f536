version: "3.8"

services:
  redwood:
    build:
      context: .
      dockerfile: ./Dockerfile
      target: base
    command: yarn rw dev
    volumes:
      - .:/home/<USER>/app
      - node_modules:/home/<USER>/app/node_modules
    ports:
      - "8910:8910"
    depends_on:
      - db
    environment:
      - DATABASE_URL=************************************/redwood
      - TEST_DATABASE_URL=************************************/redwood_test
      - SESSION_SECRET=super_secret_session_key_change_me_in_production_please
      - CI=
      - NODE_ENV=development
      - REDWOOD_API_HOST=0.0.0.0

  db:
    image: postgres:16-bookworm
    environment:
      POSTGRES_USER: redwood
      POSTGRES_PASSWORD: redwood
      POSTGRES_DB: redwood
    ports:
      - "5432:5432"
    volumes:
      - postgres:/var/lib/postgresql/data

  # After starting with `docker compose -f ./docker-compose.dev.yml up`,
  # use the console to run commands in the container:
  #
  # ```
  # docker compose -f ./docker-compose.dev.yml run --rm -it console /bin/bash
  # root@...:/home/<USER>/app# yarn rw prisma migrate dev
  # ```
  console:
    user: root
    build:
      context: .
      dockerfile: ./Dockerfile
      target: console
    tmpfs:
      - /tmp
    command: "true"
    environment:
      - DATABASE_URL=************************************/redwood
      - TEST_DATABASE_URL=************************************/redwood_test
    depends_on:
      - db

volumes:
  node_modules:
  postgres:
