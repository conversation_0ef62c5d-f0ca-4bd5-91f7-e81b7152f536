{"name": "web", "version": "0.0.0", "private": true, "browserslist": {"development": ["last 1 version"], "production": ["defaults"]}, "dependencies": {"@aws-sdk/client-s3": "^3.632.0", "@aws-sdk/s3-request-presigner": "^3.632.0", "@browserbasehq/sdk": "^2.0.0", "@browserbasehq/stagehand": "^1.9.0", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.4", "@mui/x-data-grid": "^7.12.1", "@nivo/heatmap": "^0.88.0", "@playwright/test": "^1.49.1", "@radix-ui/themes": "^3.1.1", "@redwoodjs/auth-dbauth-web": "8.4.2", "@redwoodjs/forms": "8.4.2", "@redwoodjs/router": "8.4.2", "@redwoodjs/web": "8.4.2", "@redwoodjs/web-server": "8.4.2", "@simplewebauthn/browser": "^7", "@tailwindcss/typography": "^0.5.14", "@tremor/react": "^3.17.4", "boxen": "^8.0.1", "chalk": "^5.4.1", "clientjs": "^0.2.1", "clsx": "^2.1.1", "colorette": "^2.0.20", "cuid": "^3.0.0", "daisyui": "^4.12.10", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "framer-motion": "^11.3.30", "openai": "^4.78.1", "posthog-js": "^1.203.1", "qrcode.react": "^4.1.0", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.3", "react-top-loading-bar": "^2.3.1", "roundtable-js": "^0.0.2-beta.2", "slate": "^0.103.0", "slate-react": "^0.110.1", "use-query-params": "^2.2.1", "zod": "^3.24.1"}, "devDependencies": {"@redwoodjs/vite": "8.4.2", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.19", "dotenv-cli": "^8.0.0", "postcss": "^8.4.39", "postcss-loader": "^8.1.1", "tailwindcss": "^3.4.4"}}