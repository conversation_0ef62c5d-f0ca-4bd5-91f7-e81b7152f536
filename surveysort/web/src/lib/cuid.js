// src/lib/cuidGenerator.js
import cuid from 'cuid'

export const generateCuid = ({ input = null }) => {
  if (input) {
    return generateDeterministicCuid(input)
  }
  return cuid()
}

const generateDeterministicCuid = async (input) => {
  const encoder = new TextEncoder()
  const data = encoder.encode(input)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray
    .map((byte) => byte.toString(16).padStart(2, '0'))
    .join('')
  const id = cuid.slug(hashHex) // Using cuid.slug to create a shorter, URL-friendly identifier
  return id
}
