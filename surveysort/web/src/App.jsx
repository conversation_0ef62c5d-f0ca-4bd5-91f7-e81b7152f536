import { InMemoryCache } from '@apollo/client'
import { FatalErrorBoundary, RedwoodProvider } from '@redwoodjs/web'
import { RedwoodApolloProvider } from '@redwoodjs/web/apollo'

import FatalErrorPage from 'src/pages/FatalErrorPage'
import Routes from 'src/Routes'
import Analytics from 'src/components/Analytics/Analytics'

import { AuthProvider, useAuth } from './auth'
import { FeatureFlagProvider } from 'src/components/FeatureFlagProvider/FeatureFlagProvider.jsx'

import './index.css'
import './scaffold.css'

const App = () => {
  const cache = new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          getPlans: {
            merge(existing, incoming) {
              return incoming
            },
          },
        },
      },
    },
  })

  return (
    <FatalErrorBoundary page={FatalErrorPage}>
      <RedwoodProvider titleTemplate="%PageTitle | %AppTitle">
        <AuthProvider>
          <RedwoodApolloProvider 
            useAuth={useAuth}
            graphQLClientConfig={{
              cache,
              defaultOptions: {
                query: {
                  fetchPolicy: 'cache-and-network',
                },
              },
            }}
          >
            <FeatureFlagProvider>
              <Routes />
              <Analytics 
                enablePostHog={true}
                enableTawkTo={false}
              />
            </FeatureFlagProvider>
          </RedwoodApolloProvider>
        </AuthProvider>
      </RedwoodProvider>
    </FatalErrorBoundary>
  )
}

export default App
