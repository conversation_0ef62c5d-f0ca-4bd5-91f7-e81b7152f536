export const customerSatisfaction = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 42,
    surveyName: 'Customer Satisfaction Survey',
    createdBy: '<PERSON>',
    createdOn: '2024-01-15T12:34:56Z',
    subheading: 'Feedback on customer service',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What is your name?',
        questionConfig: {
          choices: [],
          maxLength: '50',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'How satisfied are you with our service?',
        questionConfig: {
          choices: [
            'Very satisfied',
            'Satisfied',
            'Neutral',
            'Dissatisfied',
            'Very dissatisfied',
          ],
          rules: [{ operation: 'is selected', value: 'Very dissatisfied' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
          allowMultiple: false,
          allowWriteIn: false,
        },
      },
      {
        id: 3,
        type: 'rating',
        title: 'Rate our service on a scale of 1 to 5.',
        questionConfig: {
          rating: 5,
          rules: [{ operation: '<', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
      {
        id: 4,
        type: 'scale',
        title: 'How likely are you to recommend us to a friend?',
        questionConfig: {
          scale: 5,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
})

export const productFeedback = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 43,
    surveyName: 'Product Feedback Survey',
    createdBy: 'John Smith',
    createdOn: '2024-02-20T12:34:56Z',
    subheading: 'Opinions on new product features',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What product did you purchase?',
        questionConfig: {
          choices: [],
          maxLength: '100',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'Which features do you like?',
        questionConfig: {
          choices: ['Feature A', 'Feature B', 'Feature C'],
          rules: [{ operation: 'is selected', value: 'Feature C' }],
          condition: 'all',
          actions: [{ type: 'skip', questionNumber: '4' }],
          allowMultiple: true,
          allowWriteIn: true,
        },
      },
      {
        id: 3,
        type: 'scale',
        title: 'How would you rate the usability?',
        questionConfig: {
          scale: 10,
          rules: [{ operation: '>', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
      {
        id: 4,
        type: 'rating',
        title: 'Rate the product on a scale of 1 to 5.',
        questionConfig: {
          rating: 5,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
})

export const employeeEngagement = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 44,
    surveyName: 'Employee Engagement Survey',
    createdBy: 'Sarah Johnson',
    createdOn: '2024-03-10T12:34:56Z',
    subheading: 'Workplace satisfaction and engagement',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What is your department?',
        questionConfig: {
          choices: [],
          maxLength: '50',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'How do you feel about your work environment?',
        questionConfig: {
          choices: ['Positive', 'Neutral', 'Negative'],
          rules: [],
          condition: 'all',
          actions: [{ type: 'skip', questionNumber: 4 }],
          allowMultiple: false,
          allowWriteIn: false,
        },
      },
      {
        id: 3,
        type: 'rating',
        title: 'Rate your job satisfaction on a scale of 1 to 5.',
        questionConfig: {
          rating: 5,
          rules: [{ operation: '<', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
      {
        id: 4,
        type: 'scale',
        title:
          'How likely are you to recommend this company as a good place to work?',
        questionConfig: {
          scale: 10,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
})

export const complexSurvey = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 45,
    surveyName: 'Complex Survey with Rules and Actions',
    createdBy: 'Emily Davis',
    createdOn: '2024-04-25T14:34:56Z',
    subheading: 'Testing all rules, question types, and actions',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What is your name?',
        questionConfig: {
          choices: [],
          maxLength: '50',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'Select your favorite features',
        questionConfig: {
          choices: ['Feature A', 'Feature B', 'Feature C', 'Feature D'],
          rules: [{ operation: 'is selected', value: 'Feature C' }],
          condition: 'all',
          actions: [{ type: 'skip', questionNumber: '4' }],
          allowMultiple: true,
          allowWriteIn: true,
        },
      },
      {
        id: 3,
        type: 'rating',
        title: 'Rate your satisfaction with Feature A',
        questionConfig: {
          rating: 5,
          rules: [{ operation: '>', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
      {
        id: 4,
        type: 'scale',
        title: 'How likely are you to recommend Feature A to a friend?',
        questionConfig: {
          scale: 10,
          rules: [{ operation: '>=', value: '7' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
      {
        id: 5,
        type: 'short',
        title: 'Additional comments',
        questionConfig: {
          choices: [],
          maxLength: '200',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
})
export const advancedProductFeedback = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 46,
    surveyName: 'Advanced Product Feedback Survey',
    createdBy: 'Alex Johnson',
    createdOn: '2024-05-15T09:30:00Z',
    subheading: 'In-depth analysis of product features and user experience',
    questions: [
      {
        id: 1,
        type: 'multiple',
        title: 'Which of our products have you used? (Select all that apply)',
        questionConfig: {
          choices: ['Product A', 'Product B', 'Product C', 'Product D'],
          rules: [
            { operation: 'is selected', value: 'Product A' },
            { operation: 'is selected', value: 'Product B' },
          ],
          condition: 'any',
          actions: [{ type: 'show', questionNumber: 2 }],
          allowMultiple: true,
          allowWriteIn: false,
        },
      },
      {
        id: 2,
        type: 'matrix',
        title: "Rate the following aspects of the products you've used:",
        questionConfig: {
          rows: [
            { id: 'ease', text: 'Ease of Use' },
            { id: 'performance', text: 'Performance' },
            { id: 'reliability', text: 'Reliability' },
            { id: 'features', text: 'Feature Set' },
          ],
          columns: [
            { id: 'prodA', text: 'Product A' },
            { id: 'prodB', text: 'Product B' },
            { id: 'prodC', text: 'Product C' },
            { id: 'prodD', text: 'Product D' },
          ],
          cellType: 'rating',
          rating: 5,
          rules: [
            {
              operation: 'row_count_with_value',
              rowCount: 2,
              condition: 'greater_than',
              columnValue: 'prodA',
              compareValue: 4,
            },
          ],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 3 }],
        },
      },
      {
        id: 3,
        type: 'essay',
        title: 'What specific features of Product A do you find most valuable?',
        questionConfig: {
          minLength: 50,
          maxLength: 500,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 4,
        type: 'scale',
        title: 'How likely are you to recommend our products to others?',
        questionConfig: {
          scale: 10,
          labels: {
            1: 'Not at all likely',
            5: 'Neutral',
            10: 'Extremely likely',
          },
          rules: [
            { operation: '<=', value: '3' },
            { operation: '>=', value: '8' },
          ],
          condition: 'any',
          actions: [{ type: 'show', questionNumber: 5 }],
        },
      },
      {
        id: 5,
        type: 'multiple',
        title: 'What is the primary reason for your recommendation score?',
        questionConfig: {
          choices: [
            'Product Quality',
            'Customer Support',
            'Price',
            'Brand Reputation',
            'Innovative Features',
          ],
          rules: [{ operation: 'is selected', value: 'Customer Support' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
          allowMultiple: false,
          allowWriteIn: true,
        },
      },
    ],
  },
})

export const comprehensiveEmployeeSurvey = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 47,
    surveyName: 'Comprehensive Employee Satisfaction and Engagement Survey',
    createdBy: 'Emma Thompson',
    createdOn: '2024-06-01T10:00:00Z',
    subheading:
      'Assessing workplace environment, job satisfaction, and career growth',
    questions: [
      {
        id: 1,
        type: 'matrix',
        title: 'Rate your agreement with the following statements:',
        questionConfig: {
          rows: [
            { id: 'worklife', text: 'I have a good work-life balance' },
            {
              id: 'growth',
              text: 'I have opportunities for professional growth',
            },
            {
              id: 'communication',
              text: 'There is open and clear communication',
            },
            { id: 'recognition', text: 'My work is recognized and valued' },
          ],
          columns: [
            { id: 'stronglyDisagree', text: 'Strongly Disagree' },
            { id: 'disagree', text: 'Disagree' },
            { id: 'neutral', text: 'Neutral' },
            { id: 'agree', text: 'Agree' },
            { id: 'stronglyAgree', text: 'Strongly Agree' },
          ],
          cellType: 'radio',
          rules: [
            {
              operation: 'row_count_with_value',
              rowCount: 2,
              condition: 'greater_than',
              columnValue: 'disagree',
              compareValue: 0,
            },
          ],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 2 }],
        },
      },
      {
        id: 2,
        type: 'essay',
        title: 'What specific areas of your work experience need improvement?',
        questionConfig: {
          minLength: 100,
          maxLength: 1000,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 3,
        type: 'multiple',
        title:
          'Which of the following benefits are most important to you? (Select up to 3)',
        questionConfig: {
          choices: [
            'Health Insurance',
            'Retirement Plans',
            'Flexible Working Hours',
            'Professional Development',
            'Paid Time Off',
            'Remote Work Options',
          ],
          rules: [{ operation: 'is selected', value: 'Remote Work Options' }],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 4 }],
          allowMultiple: true,
          maxSelections: 3,
          allowWriteIn: false,
        },
      },
      {
        id: 4,
        type: 'scale',
        title: 'How many days per week would you prefer to work remotely?',
        questionConfig: {
          scale: 5,
          labels: {
            1: '1 day',
            3: '3 days',
            5: '5 days (full remote)',
          },
          rules: [{ operation: '>=', value: '4' }],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 5 }],
        },
      },
      {
        id: 5,
        type: 'multiple',
        title: 'What challenges do you anticipate with increased remote work?',
        questionConfig: {
          choices: [
            'Maintaining work-life balance',
            'Team collaboration',
            'Access to resources',
            'Staying motivated',
            'Technical issues',
          ],
          rules: [],
          condition: 'all',
          actions: [],
          allowMultiple: true,
          allowWriteIn: true,
        },
      },
      {
        id: 6,
        type: 'rating',
        title: 'Overall, how satisfied are you with your current job?',
        questionConfig: {
          rating: 10,
          rules: [{ operation: '<', value: '6' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: null }],
        },
      },
    ],
  },
})

export const advancedMarketResearch = (/* vars, { ctx, req } */) => ({
  endUserSurvey: {
    id: 48,
    surveyName: 'Advanced Market Research Survey',
    createdBy: 'Michael Brown',
    createdOn: '2024-07-10T11:15:00Z',
    subheading:
      'Comprehensive analysis of market trends and consumer preferences',
    questions: [
      {
        id: 1,
        type: 'multiple',
        title:
          'Which of the following product categories are you interested in?',
        questionConfig: {
          choices: [
            'Electronics',
            'Home Appliances',
            'Clothing',
            'Automotive',
            'Food and Beverage',
          ],
          rules: [],
          condition: 'all',
          actions: [],
          allowMultiple: true,
          allowWriteIn: false,
        },
      },
      {
        id: 2,
        type: 'matrix',
        title:
          'Rate the importance of the following factors when making a purchase:',
        questionConfig: {
          rows: [
            { id: 'price', text: 'Price' },
            { id: 'quality', text: 'Quality' },
            { id: 'brand', text: 'Brand Reputation' },
            { id: 'sustainability', text: 'Environmental Sustainability' },
            { id: 'convenience', text: 'Convenience of Purchase' },
          ],
          columns: [
            { id: 'notImportant', text: 'Not Important' },
            { id: 'slightlyImportant', text: 'Slightly Important' },
            { id: 'moderatelyImportant', text: 'Moderately Important' },
            { id: 'veryImportant', text: 'Very Important' },
            { id: 'extremelyImportant', text: 'Extremely Important' },
          ],
          cellType: 'radio',
          rules: [
            {
              operation: 'row_count_with_value',
              rowCount: 3,
              condition: 'greater_than',
              columnValue: 'veryImportant',
              compareValue: 0,
            },
          ],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 3 }],
        },
      },
      {
        id: 3,
        type: 'essay',
        title:
          'Please explain why these factors are particularly important to you.',
        questionConfig: {
          minLength: 50,
          maxLength: 500,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 4,
        type: 'scale',
        title:
          'How likely are you to try a new product from an unfamiliar brand?',
        questionConfig: {
          scale: 10,
          labels: {
            1: 'Very Unlikely',
            5: 'Neutral',
            10: 'Very Likely',
          },
          rules: [
            { operation: '<=', value: '3' },
            { operation: '>=', value: '8' },
          ],
          condition: 'any',
          actions: [{ type: 'show', questionNumber: 5 }],
        },
      },
      {
        id: 5,
        type: 'multiple',
        title: 'What would motivate you to try a new brand?',
        questionConfig: {
          choices: [
            'Lower price',
            'Higher quality',
            'Innovative features',
            'Positive reviews',
            'Environmental friendliness',
            'Social media influence',
          ],
          rules: [],
          condition: 'all',
          actions: [],
          allowMultiple: true,
          maxSelections: 3,
          allowWriteIn: true,
        },
      },
      {
        id: 6,
        type: 'rating',
        title:
          'How satisfied are you with the current options available in the market for your favorite product category?',
        questionConfig: {
          rating: 10,
          rules: [{ operation: '<', value: '5' }],
          condition: 'all',
          actions: [{ type: 'show', questionNumber: 7 }],
        },
      },
      {
        id: 7,
        type: 'essay',
        title:
          'What improvements or new products would you like to see in this category?',
        questionConfig: {
          minLength: 50,
          maxLength: 500,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
})

// Update the standard export to include these new scenarios
export const standard = (/* vars, { ctx, req } */) => {
  const scenarios = [
    customerSatisfaction,
    productFeedback,
    employeeEngagement,
    complexSurvey,
    advancedProductFeedback,
    comprehensiveEmployeeSurvey,
    advancedMarketResearch,
  ]
  return scenarios[
    Math.floor(Math.random() * scenarios.length)
  ](/* vars, { ctx, req } */)
}
