import { useState, useEffect } from 'react'
import { Link, routes } from '@redwoodjs/router'
import { Form, TextField, Label, Submit } from '@redwoodjs/forms'
import EndUserSurveyResponseSubmit from 'src/components/EndUserSurveyResponseSubmit'
import LoadingBar from 'react-top-loading-bar'
import Metadata from 'src/components/Metadata'

export const QUERY = gql`
  query FindEndUserSurveyQuery($id: String!) {
    endUserSurvey: endUserSurvey(id: $id) {
      id
      title
      description
      status
      settings {
        randomizationGroups {
          name
          questionIds
        }
        themeConfig {
          questionTextColor
          answerTextColor
          buttonColor
          backgroundColor
          progressBarColor
        }
        previewTitle
        previewDescription
        previewImage
        companyLogo
        removeBranding
        showNavigation
        showProgressBar
      }
      questions {
        id
        type
        title
        required
        pageNumber
        order
        questionConfig {
          choices
          rating
          scale
          minLength
          maxLength
          rules {
            operation
            value
          }
          condition
          actions {
            type
            questionNumber
          }
          allowMultiple
          allowWriteIn
          rows
          columns
          cellType
          includeTime
          requireName
          requireEmail
          requirePhone
          requireCompany
          requireAddress
          requireWebsite
          randomize
          randomizeChoices
          randomizeRows
          welcomeMessage
          welcomeDisclaimer
          requireDisclaimer
          thankYouMessage
        }
      }
    }
  }
`

export const Loading = () => (
  <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
    <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
      <div className="skeleton mb-4 h-8 w-3/4"></div>
      <div className="skeleton mb-6 h-4 w-full"></div>
      <div className="skeleton h-10 w-full"></div>
    </div>
  </div>
)

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }) => {
  if (error) {
    // Extract relevant error details
    const errorMsg = error?.graphQLErrors?.[0]?.extensions?.errorMsg || error?.message || 'An unexpected error occurred.';
    const errorCode = error?.graphQLErrors?.[0]?.extensions?.code || 'Unknown Error Code';

    return (
      <div className="error-container bg-red-50 border-l-4 border-red-500 p-4 rounded-md shadow-sm">
        <div className="flex items-center">
          <svg
            className="h-6 w-6 text-red-500 mr-2"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-3-3v6m2-8.938c3.22 0 5.832 2.387 5.832 5.334 0 2.947-2.612 5.333-5.832 5.333-3.22 0-5.832-2.386-5.832-5.333C3.168 7.448 5.78 5.062 9 5.062z"
            />
          </svg>
          <h2 className="text-lg font-medium text-red-700">Oops! Something went wrong.</h2>
        </div>
        <p className="text-red-700 mt-2">{errorMsg}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-600 text-white font-medium text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          Reload Page

        </button>
        <a
          href="https://surveysort.com"
          className="ml-2 mt-4 px-5 py-3 bg-blue-600 text-white font-medium text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Go Home
        </a>
      </div>
    );

  }
}

const EmailCollectionForm = ({ survey, onEmailSubmit }) => {
  const [email, setEmail] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    onEmailSubmit(email)
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">

        <Form onSubmit={handleSubmit} className="space-y-4">
          <Label
            name="email"
            className="block text-sm font-medium text-gray-700"
          >
            Email Address
          </Label>
          <TextField
            name="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
            validation={{
              required: true,
              pattern: { value: /^\S+@\S+\.\S+$/ },
            }}
          />
          <Submit className="btn btn-primary w-full">Start Survey</Submit>
        </Form>
      </div>
    </div>
  )
}

const SurveyUnavailable = ({ message }) => (
  <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
    <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
      <h1 className="mb-4 text-center text-2xl font-bold">
        Survey Unavailable
      </h1>
      <p className="text-center">{message}</p>
    </div>
  </div>
)

const PoweredBySurveysort = ({ settings }) => {
  if (settings?.removeBranding) return null

  return (
    <div className="fixed bottom-4 left-4 text-sm">
      Powered by{' '}
      <Link
        to="https://surveysort.com"
        style={{
          color: settings?.themeConfig?.buttonColor,
          textDecoration: 'none'
        }}
        className="hover:underline"
      >
        Surveysort.com
      </Link>
    </div>
  )
}

export const Success = ({ endUserSurvey }) => {
  const [progress, setProgress] = useState(0)
  const [email, setEmail] = useState(null)
  const [disclosureAccepted, setDisclosureAccepted] = useState(false)

  // Sort questions by pageNumber and order
  const sortedQuestions = [...endUserSurvey.questions].sort((a, b) => {
    if (a.pageNumber === b.pageNumber) {
      return a.order - b.order
    }
    return a.pageNumber - b.pageNumber
  })

  // Apply survey-level randomization
  const randomizeQuestionsForGroups = (questions, randomizationGroups) => {
    if (!randomizationGroups?.length) return questions

    console.log('Starting randomization with groups:', randomizationGroups)
    let questionsCopy = [...questions]

    randomizationGroups.forEach(group => {
      // Only randomize questions within the same page
      const pageGroups = {}

      // First, organize questions by page
      group.questionIds.forEach(id => {
        const question = questionsCopy.find(q => q.id === id)
        if (question) {
          const page = question.pageNumber
          pageGroups[page] = pageGroups[page] || []
          pageGroups[page].push(question)
        }
      })

      console.log('Page groups before shuffle:', pageGroups)

      // Create a new array with shuffled questions
      const newQuestionsCopy = [...questionsCopy]

      // For each page, shuffle the questions in that group
      Object.entries(pageGroups).forEach(([page, pageQuestions]) => {
        // Get indices of questions to shuffle
        const indices = pageQuestions.map(q =>
          questionsCopy.findIndex(origQ => origQ.id === q.id)
        )

        // Shuffle the questions
        const shuffledQuestions = [...pageQuestions]
          .sort(() => Math.random() - 0.5)

        // Replace questions at their original indices
        indices.forEach((index, i) => {
          if (index !== -1) {
            newQuestionsCopy[index] = shuffledQuestions[i]
          }
        })
      })

      questionsCopy = newQuestionsCopy
      console.log('Questions after shuffle:', questionsCopy)
    })

    return questionsCopy
  }

  // Apply question-level randomization for choices
  const randomizeQuestionChoices = (question) => {
    if (!question.questionConfig?.randomize) return question

    const config = { ...question.questionConfig }

    if (question.type === 'MULTIPLE_CHOICE' && Array.isArray(config.choices)) {
      const choices = [...config.choices]
      const indices = config.randomizeChoices?.length ?
        config.randomizeChoices :
        choices.map((_, i) => i)

      const toRandomize = indices.map(i => choices[i])
      const shuffled = [...toRandomize].sort(() => Math.random() - 0.5)

      indices.forEach((originalIndex, i) => {
        choices[originalIndex] = shuffled[i]
      })

      return {
        ...question,
        questionConfig: { ...config, choices }
      }
    }

    if (question.type === 'MATRIX' && Array.isArray(config.rows)) {
      const rows = [...config.rows]
      const indices = config.randomizeRows?.length ?
        config.randomizeRows :
        rows.map((_, i) => i)

      const toRandomize = indices.map(i => rows[i])
      const shuffled = [...toRandomize].sort(() => Math.random() - 0.5)

      indices.forEach((originalIndex, i) => {
        rows[originalIndex] = shuffled[i]
      })

      return {
        ...question,
        questionConfig: { ...config, rows }
      }
    }

    return question
  }

  // First apply survey-level randomization, then question-level
  const randomizedQuestions = randomizeQuestionsForGroups(
    sortedQuestions.map(q => randomizeQuestionChoices(q)),
    endUserSurvey.settings?.randomizationGroups
  )

  // Calculate total steps including pages
  const totalPages = Math.max(...sortedQuestions.map(q => q.pageNumber)) + 1
  const totalSteps = (endUserSurvey.settings?.disclosure ? 1 : 0) +
    (endUserSurvey.settings?.requireEmail ? 1 : 0) +
    totalPages

  const calculateProgress = (step) => {
    const progress = Math.min(100, Math.round((step / totalSteps) * 100))
    return progress
  }

  useEffect(() => {
    if (endUserSurvey.settings?.showProgressBar) {
      setProgress(calculateProgress(0))
    }
  }, [endUserSurvey.settings?.showProgressBar])

  if (endUserSurvey.status === 'DRAFT') {
    return <SurveyUnavailable message="This survey is not yet published." />
  }

  if (endUserSurvey.status === 'COMPLETED') {
    return <SurveyUnavailable message="This survey has been closed." />
  }

  // Show company logo if available
  const SurveyHeader = () => (
    endUserSurvey.settings?.companyLogo ? (
      <div className="mb-4 flex justify-center">
        <img
          src={endUserSurvey.settings.companyLogo}
          alt="Company Logo"
          className="max-h-16 w-auto"
        />
      </div>
    ) : null
  )

  if (endUserSurvey.settings?.disclosure && !disclosureAccepted) {
    return (
      <>
        {endUserSurvey.settings?.showProgressBar && (
          <LoadingBar
            containerClassName="bg-primary color-primary"
            progress={progress}
            onLoaderFinished={() => setProgress(0)}
          />
        )}
        <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
          <div className="w-full max-w-2xl rounded-lg bg-white p-8 shadow-lg">
            <SurveyHeader />
            <h1 className="mb-6 text-center text-3xl font-bold">{endUserSurvey.title}</h1>
            <h2 className="mb-4 text-center text-2xl font-bold">Disclosure</h2>
            <div className="mb-6 prose" dangerouslySetInnerHTML={{ __html: endUserSurvey.settings.disclosure }} />
            <div className="flex flex-col items-center">
              <label className="mb-4 flex items-center">
                <input type="checkbox" className="form-checkbox mr-2" required />
                <span>I understand and would like to proceed</span>
              </label>
              <button
                className="btn btn-primary"
                onClick={() => {
                  setDisclosureAccepted(true)
                  setProgress(calculateProgress(1))
                }}
              >
                Proceed
              </button>
            </div>
          </div>
        </div>
        <PoweredBySurveysort settings={endUserSurvey.settings} />
      </>
    )
  }


  const updateProgress = (currentPage) => {
    const step = (endUserSurvey.settings?.disclosure ? 1 : 0) +
      (endUserSurvey.settings?.requireEmail ? 1 : 0) +
      currentPage + 1
    setProgress(calculateProgress(step))
  }



  return (
    <>
      <Metadata
        title={endUserSurvey.settings?.previewTitle || endUserSurvey.title}
        description={endUserSurvey.settings?.previewDescription || endUserSurvey.description}
        ogType="website"
        ogImage={endUserSurvey.settings?.previewImage}
        ogUrl={`${window.location.origin}${routes.endUserSurveySubmit({ id: endUserSurvey.id })}`}
        twitterCard="summary_large_image"
        twitterImage={endUserSurvey.settings?.previewImage}
        twitterTitle={endUserSurvey.settings?.previewTitle || endUserSurvey.title}
        twitterDescription={endUserSurvey.settings?.previewDescription || endUserSurvey.description}
      >
        <meta property="og:site_name" content="SurveySort" />
        <meta name="twitter:creator" content="@surveysort" />
        <meta name="twitter:site" content="@surveysort" />
      </Metadata>

      {endUserSurvey.settings?.showProgressBar && (
        <LoadingBar
          color={endUserSurvey.settings?.themeConfig?.progressBarColor}
          progress={progress}
          onLoaderFinished={() => setProgress(0)}
        />
      )}
      <div
        className="flex min-h-screen flex-col items-center justify-center"
        style={{
          backgroundColor: endUserSurvey.settings?.themeConfig?.backgroundColor || '#f3f4f6',
          color: endUserSurvey.settings?.themeConfig?.questionTextColor
        }}
      >
        <div className="w-full max-w-4xl">
          <SurveyHeader />
          <EndUserSurveyResponseSubmit
            survey={{
              ...endUserSurvey,
              questions: randomizedQuestions
            }}
            participantEmail={email}
            updateProgress={updateProgress}
            showNavigation={endUserSurvey.settings?.showNavigation}
          />
        </div>
      </div>
      <PoweredBySurveysort settings={endUserSurvey.settings} />
    </>
  )
}
