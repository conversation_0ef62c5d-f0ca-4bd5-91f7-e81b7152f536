import { useQuery } from '@redwoodjs/web'
import { FilterBar } from './FilterBar'

const UNIFIED_FILTERS_QUERY = gql`
  query UnifiedFiltersQuery($surveyId: String!) {
    unifiedSurveyFilters(surveyId: $surveyId) {
      availableFilters {
        key
        type
        label
        description
        options {
          value
          label
        }
      }
      availableQuestionFilters {
        id
        title
        type
        filterType
        choices
        min
        max
        allowMultiple
        allowWriteIn
        rows
        columns
      }
    }
  }
`

export const Loading = () => (
  <div className="animate-pulse space-y-4">
    <div className="h-10 bg-base-200 rounded"></div>
    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-8 bg-base-200 rounded"></div>
      ))}
    </div>
  </div>
)

export const Empty = () => <div>No filters available</div>

export const Failure = ({ error }) => (
  <div className="text-error">Error loading filters: {error.message}</div>
)

export const Success = ({ 
  unifiedSurveyFilters, 
  filters, 
  onFilterChange, 
  onQuestionFilterChange 
}) => {
  return (
    <FilterBar
      filters={filters}
      onFilterChange={onFilterChange}
      onQuestionFilterChange={onQuestionFilterChange}
      availableFilters={unifiedSurveyFilters.availableFilters}
      availableQuestionFilters={unifiedSurveyFilters.availableQuestionFilters}
    />
  )
}

export const FilterBarCell = ({ 
  surveyId, 
  filters, 
  onFilterChange, 
  onQuestionFilterChange 
}) => {
  const { loading, error, data } = useQuery(UNIFIED_FILTERS_QUERY, {
    variables: { surveyId },
  })

  if (loading) return <Loading />
  if (error) return <Failure error={error} />
  if (!data?.unifiedSurveyFilters) return <Empty />

  return (
    <Success
      unifiedSurveyFilters={data.unifiedSurveyFilters}
      filters={filters}
      onFilterChange={onFilterChange}
      onQuestionFilterChange={onQuestionFilterChange}
    />
  )
} 