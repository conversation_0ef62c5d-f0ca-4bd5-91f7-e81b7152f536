import { Typography, Box } from '@mui/material'

const ExpandedResponseRow = ({ row }) => {
  return (
    <Box sx={{ margin: 1 }}>
      <Typography variant="h6" gutterBottom component="div">
        Response Details
      </Typography>
      <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
        {JSON.stringify(row.original, null, 2)}
      </pre>
    </Box>
  )
}

export default ExpandedResponseRow 