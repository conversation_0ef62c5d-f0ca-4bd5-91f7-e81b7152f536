import { StatusBadge } from './StatusBadge'

export const ResponseBadges = ({ response }) => {
  const badges = [
    {
      show: response.isHighQuality,
      label: 'High Quality',
      color: 'green',
      tooltip: 'Response meets quality standards and provides valuable insights'
    },
    {
      show: response.isIrrelevant,
      label: 'Irrelevant',
      color: 'red',
      tooltip: 'Response content is not relevant to the survey questions'
    },
    {
      show: response.isLowEffort,
      label: 'Low Effort',
      color: 'yellow',
      tooltip: 'Response shows minimal effort or engagement'
    },
    {
      show: response.isDuplicate,
      label: 'Duplicate',
      color: 'gray',
      tooltip: 'Similar response detected from same source'
    },
    {
      show: response.vpnDetected,
      label: 'VPN',
      color: 'gray',
      tooltip: 'VPN usage detected during submission'
    },
    {
      show: response.proxyDetected,
      label: 'Proxy',
      color: 'gray',
      tooltip: 'Proxy server usage detected'
    },
    {
      show: response.copyPastedDetected,
      label: 'Copy-Pasted',
      color: 'gray',
      tooltip: 'Content appears to be copied from elsewhere'
    },
    {
      show: response.straightLiningDetected,
      label: 'Straight-Lining',
      color: 'gray',
      tooltip: 'Same answer selected repeatedly in matrix questions'
    },
    {
      show: response.submittedTooFast,
      label: 'Too Fast',
      color: 'gray',
      tooltip: 'Response submitted unusually quickly'
    }
  ]

  return (
    <div className="flex flex-wrap gap-2">
      {badges.filter(b => b.show).map((badge, i) => (
        <StatusBadge 
          key={i} 
          color={badge.color} 
          label={badge.label} 
          tooltip={badge.tooltip}
        />
      ))}
    </div>
  )
} 