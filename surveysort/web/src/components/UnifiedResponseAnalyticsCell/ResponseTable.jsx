import { useState, Fragment } from 'react'
import { 
  ChevronDownIcon, 
  ChevronRightIcon,
  DocumentMagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import { motion, AnimatePresence } from 'framer-motion'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import { EmptyState } from './EmptyState'
import { ExpandedView } from './ExpandedView'
import { StatusBadge } from './StatusBadge'

dayjs.extend(relativeTime)
dayjs.extend(localizedFormat)

export const ResponseTable = ({
  responses,
  sortConfig,
  onSort,
  page,
  pageInfo,
  onPageChange,
  hidePagination = false
}) => {
  const [expandedId, setExpandedId] = useState(null)

  if (!responses?.length) {
    return (
      <div className="rounded-lg border border-base-300 bg-base-100/30 backdrop-blur-md">
        <EmptyState
          message={pageInfo?.totalCount === 0 ? "No matching responses found" : "No matching responses"}
          description={
              "Try adjusting your filters to find what you're looking for, or wait for more responses"
          }
          icon={pageInfo?.totalCount === 0 ? DocumentMagnifyingGlassIcon : FunnelIcon}
        />
      </div>
    )
  }

  return (
    <table className="table w-full rounded-lg border border-base-300 bg-base-100/30 backdrop-blur-md">
      <thead>
        <tr>
          <th className="w-1/12"></th>
          <th className="w-1/12">ID</th>
          <th className="w-3/12">Submission Time</th>
          <th className="w-2/12">Quality</th>
          <th className="w-4/12">Location</th>
        </tr>
      </thead>
      <tbody>
        <AnimatePresence>
          {responses.map((response) => (
            <Fragment key={response.id}>
              <motion.tr
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="hover cursor-pointer"
                onClick={() => setExpandedId(expandedId === response.id ? null : response.id)}
              >
                <td>
                  <button className="btn btn-ghost btn-xs">
                    {expandedId === response.id ? (
                      <ChevronDownIcon className="h-4 w-4" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4" />
                    )}
                  </button>
                </td>
                <td>{response.id.slice(0, 8)}</td>
                <td>
                  <div className="tooltip" data-tip={dayjs(response.submissionTime).format('LLL')}>
                    {dayjs(response.submissionTime).fromNow()}
                  </div>
                </td>
                <td>
                  {response.isHighQuality && (
                    <StatusBadge 
                      color="green" 
                      label="High Quality" 
                      tooltip="Response meets quality standards and provides valuable insights"
                    />
                  )}
                </td>
                <td>
                  {response.submittedCity && response.submittedCountry 
                    ? `${response.submittedCity}, ${response.submittedCountry}`
                    : response.submittedCountry || 'Unknown'}
                </td>
              </motion.tr>
              <AnimatePresence>
                {expandedId === response.id && (
                  <motion.tr
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <td colSpan={5} className="p-0 border-0 bg-base-100/30">
                      <ExpandedView response={response} />
                    </td>
                  </motion.tr>
                )}
              </AnimatePresence>
            </Fragment>
          ))}
        </AnimatePresence>
      </tbody>
      {!hidePagination && (
        <tfoot>
          <tr>
            <td colSpan={5} className="border-t border-base-300">
              <div className="flex items-center justify-start p-4">
                <div className="join">
                  <button
                    className="join-item btn btn-xs"
                    onClick={() => onPageChange(page - 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </button>
                  <button className="join-item btn btn-xs">
                    Page {page} of {pageInfo.totalPages}
                  </button>
                  <button
                    className="join-item btn btn-xs"
                    onClick={() => onPageChange(page + 1)}
                    disabled={page === pageInfo.totalPages}
                  >
                    Next
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tfoot>
      )}
    </table>
  )
} 