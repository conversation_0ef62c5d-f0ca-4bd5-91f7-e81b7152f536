import { ClockIcon, GlobeAltIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline'
import { ResponseBadges } from './ResponseBadges'
import { formatContactDetails, formatQuestionResponse } from './utils'

export const ExpandedView = ({ response }) => {
  const contactDetails = formatContactDetails(
    response.submissionContactDetails,
    response.questionResponses
  )

  return (
    <div className="min-w-0 w-full">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 p-4">
        {/* Left Column - Metadata */}
        <div className="space-y-4 min-w-0">
          <div className=" pb-2">
            <ResponseBadges response={response} />
          </div>
          
          {contactDetails && contactDetails.length > 0 && (
            <div className="border-t border-base-200 dark:border-base-700 pt-4">
              <h4 className="text-sm font-medium mb-2">Contact Details</h4>
              <dl className="space-y-2">
                {contactDetails.map(({ label, value }, i) => (
                  <div key={i} className="min-w-0">
                    <dt className="text-xs text-gray-500 dark:text-gray-400 truncate">{label}</dt>
                    <dd className="text-sm break-words">{value}</dd>
                  </div>
                ))}
              </dl>
            </div>
          )}

          <div className="border-t border-zinc-200 dark:border-zinc-700  my-4 space-y-">
            <div className="flex items-center gap-2 2 mt-4">
              <ClockIcon className="w-4 h-4 shrink-0 text-gray-500" />
              <div className="min-w-0">
                <span className="text-xs break-words">
                  Time Spent: {response.timeSpentOnSurvey}s
                </span>
              </div>
            </div>

            {response.submittedCountry && (
              <div className="flex overflow items-center gap-2">
                <GlobeAltIcon className="w-4 h-4 shrink-0 text-gray-500" />
                <div className="min-w-0">
                  <span className="text-xs break-words">
                    {response.submittedCity ? `${response.submittedCity}, ` : ''}{response.submittedCountry}
                  </span>
                </div>
              </div>
            )}

            {response.deviceInfo && (
              <div className="flex items-center gap-2">
                <DevicePhoneMobileIcon className="w-4 h-4 shrink-0 text-gray-500" />
                <div className="min-w-0">
                  <span className="text-xs break-words">
                    {[
                      response.deviceInfo.browser,
                      response.deviceInfo.os,
                      response.deviceInfo.device,
                      response.deviceInfo.screenResolution
                    ].filter(Boolean).join(' • ')}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Response Details */}
        <div className="lg:col-span-3 flex-col space-y-4">
          {response.questionResponses.map((qr, i) => (
            <div key={i} className="border-b border-zinc-200 dark:border-zinc-700 pb-2">
              <div className="font-medium text-sm break-words mb-1">{qr.title}</div>
              <div className="text-sm text-zinc-600 dark:text-zinc-300 whitespace-pre-wrap break-words overflow-x-auto rounded bg-base-200/50 p-2">
                {formatQuestionResponse(qr.response, qr.type)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 