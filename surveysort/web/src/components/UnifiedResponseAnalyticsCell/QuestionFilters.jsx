import { useState, useEffect } from 'react'
import { Dialog } from '@headlessui/react'
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline'

export const QuestionFilters = ({
  questions,
  currentFilters = [],
  onQuestionFilterChange,
}) => {
  const [selectedQuestions, setSelectedQuestions] = useState([])
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Initialize selected questions from currentFilters
  useEffect(() => {
    const questionIds = currentFilters.map(f => f.questionId)
    const selected = questions.filter(q => questionIds.includes(q.id))
    setSelectedQuestions(selected)
  }, [questions, currentFilters])

  const handleQuestionSelect = (question) => {
    setSelectedQuestions(prev => [...prev, question])
  }

  const handleFilterChange = (questionId, filterValue) => {
    console.log('Filter change:', questionId, filterValue) // Debug log
    onQuestionFilterChange(questionId, filterValue)
  }

  const handleRemoveQuestion = (questionId) => {
    setSelectedQuestions(prev => prev.filter(q => q.id !== questionId))
    onQuestionFilterChange(questionId, null) // Clear the filter
  }

  return (
    <div className="space-y-4">
      {selectedQuestions.map(question => {
        const currentFilter = currentFilters.find(f => f.questionId === question.id)
        return (
          <div key={question.id} className="flex items-start gap-2">
            <div className="flex-1">
              <div className="text-sm font-medium mb-1">{question.title}</div>
              <QuestionFilterInput
                question={question}
                value={currentFilter}
                onChange={(value) => handleFilterChange(question.id, value)}
              />
            </div>
            <button
              onClick={() => handleRemoveQuestion(question.id)}
              className="btn btn-ghost btn-sm p-1"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        )
      })}
      
      {/* Add filter button */}
      {questions.length > selectedQuestions.length && (
        <button
          onClick={() => setIsModalOpen(true)}
          className="btn btn-outline btn-sm gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Question Filter
        </button>
      )}

      {/* Modal for selecting questions */}
      <AddQuestionFilterModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        questions={questions.filter(q => !selectedQuestions.some(sq => sq.id === q.id))}
        onSelect={handleQuestionSelect}
      />
    </div>
  )
}

const QuestionFilterInput = ({ question, value, onChange }) => {
  switch (question.filterType) {
    case 'CHOICE':
      return (
        <select
          className="select select-sm w-full"
          value={value?.value || ''}
          onChange={(e) => {
            console.log('Question:', question) // Debug
            onChange({
              questionId: question.id,
              type: question.type, // Always include type
              value: e.target.value || null
            })
          }}
        >
          <option value="">Any</option>
          {question.choices?.map((choice) => (
            <option key={choice} value={choice}>{choice}</option>
          ))}
        </select>
      )

    case 'RANGE':
      return (
        <div className="flex gap-2">
          <input
            type="number"
            className="input input-sm w-1/2"
            placeholder="Min"
            min={question.min}
            max={question.max}
            value={value?.minValue || ''}
            onChange={(e) => onChange({
              questionId: question.id,
              type: question.type,
              minValue: e.target.value ? Number(e.target.value) : null,
              maxValue: value?.maxValue
            })}
          />
          <input
            type="number"
            className="input input-sm w-1/2"
            placeholder="Max"
            min={question.min}
            max={question.max}
            value={value?.maxValue || ''}
            onChange={(e) => onChange({
              questionId: question.id,
              type: question.type,
              minValue: value?.minValue,
              maxValue: e.target.value ? Number(e.target.value) : null
            })}
          />
        </div>
      )

    case 'MATRIX':
      return (
        <div className="flex gap-2">
          <select
            className="select select-sm w-1/2"
            value={value?.row || ''}
            onChange={(e) => onChange({
              questionId: question.id,
              type: question.type,
              row: e.target.value || null,
              column: value?.column
            })}
          >
            <option value="">Any row</option>
            {question.rows?.map((row) => (
              <option key={row} value={row}>{row}</option>
            ))}
          </select>
          <select
            className="select select-sm w-1/2"
            value={value?.column || ''}
            onChange={(e) => onChange({
              questionId: question.id,
              type: question.type,
              row: value?.row,
              column: e.target.value || null
            })}
          >
            <option value="">Any column</option>
            {question.columns?.map((col) => (
              <option key={col} value={col}>{col}</option>
            ))}
          </select>
        </div>
      )

    default:
      return null
  }
}

const AddQuestionFilterModal = ({ isOpen, onClose, questions, onSelect }) => {
  // Filter out unsupported question types
  const supportedTypes = ['CHOICE', 'RANGE', 'MATRIX']
  const filteredQuestions = questions.filter(q => 
    supportedTypes.includes(q.filterType)
  )

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full rounded-lg bg-white p-6">
          <Dialog.Title className="text-lg font-medium mb-4">
            Add Question Filter
          </Dialog.Title>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto">
            {filteredQuestions.map(question => (
              <div 
                key={question.id}
                className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                onClick={() => {
                  onSelect(question)
                  onClose()
                }}
              >
                <h3 className="font-medium mb-2">{question.title}</h3>
                <div className="text-sm text-gray-500">
                  {getQuestionTypeLabel(question.filterType)}
                  {question.choices && (
                    <div className="mt-1 text-xs">
                      {question.choices.length} options available
                    </div>
                  )}
                  {(question.min !== null && question.max !== null) && (
                    <div className="mt-1 text-xs">
                      Range: {question.min} - {question.max}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              className="btn btn-ghost btn-sm"
              onClick={onClose}
            >
              Cancel
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}

const getQuestionTypeLabel = (type) => {
  const typeMap = {
    'CHOICE': 'Multiple Choice',
    'RANGE': 'Range',
    'MATRIX': 'Matrix'
  }
  return typeMap[type] || type
}

export default QuestionFilters 