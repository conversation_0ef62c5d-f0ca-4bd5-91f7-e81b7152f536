export const CONTACT_FIELDS = [
  { id: 'name', label: 'Name' },
  { id: 'email', label: 'Email Address' },
  { id: 'phone', label: 'Phone Number' },
  { id: 'company', label: 'Company Name' },
  { id: 'address', label: 'Address' },
  { id: 'website', label: 'Website' },
]

export const formatContactDetails = (details, questionResponses = []) => {
  if (!details && !questionResponses?.length) return null
  
  // First try submission contact details
  let parsedDetails = details || {}
  
  // Then look for contact details question
  const contactQuestion = questionResponses?.find(qr => qr.type === 'CONTACT_DETAILS')
  if (contactQuestion) {
    try {
      const contactResponse = typeof contactQuestion.response === 'string' 
        ? JSON.parse(contactQuestion.response)
        : contactQuestion.response
        
      // Merge with existing details, preferring submission details
      parsedDetails = {
        ...contactResponse,
        ...parsedDetails
      }
    } catch (e) {
      console.error('Error parsing contact question response:', e)
    }
  }

  // Only return if we have any actual values
  const hasValues = Object.values(parsedDetails).some(v => v)
  if (!hasValues) return null

  return CONTACT_FIELDS.map(field => ({
    label: field.label,
    value: parsedDetails[field.id] || 'N/A'
  }))
}

export const formatQuestionResponse = (response, type) => {
  if (!response) return 'N/A'
  
  try {
    // Parse the response if it's a string
    let parsedResponse = response
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response)
      } catch (e) {
        console.error('Error parsing response:', e)
        return String(response)
      }
    }

    switch (type) {
      case 'MULTIPLE_CHOICE':
        return Array.isArray(parsedResponse) ? parsedResponse.join(', ') : parsedResponse
      case 'MATRIX':
        return Object.entries(parsedResponse)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n')
      case 'CONTACT':
        return Object.entries(parsedResponse)
          .filter(([_, value]) => value)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n')
      default:
        return typeof parsedResponse === 'object' 
          ? JSON.stringify(parsedResponse, null, 2) 
          : String(parsedResponse)
    }
  } catch (e) {
    console.error('Error formatting response:', e)
    return String(response)
  }
}

export const getQualityBadgeColor = (response) => {
  if (response.isHighQuality) return 'success'
  if (response.isLowEffort) return 'error'
  if (response.isIrrelevant) return 'warning'
  return 'neutral'
}

export const formatScore = (score) => Math.round(score)

export const getContactDisplay = (details) => {
  if (!details) return 'N/A'
  if (details.email) return { type: 'email', value: details.email }
  if (details.phone) return { type: 'phone', value: details.phone }
  return 'N/A'
} 