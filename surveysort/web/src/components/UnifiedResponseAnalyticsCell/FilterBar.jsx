import { useState, Fragment } from 'react'
import { Button } from '@tremor/react'
import { Switch } from 'src/components/catalyst/switch'
import classNames from 'classnames'
import {
  AdjustmentsHorizontalIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  ShieldCheckIcon,
  QuestionMarkCircleIcon,
  MapPinIcon,
  IdentificationIcon,
  ClockIcon,
  ComputerDesktopIcon,
} from '@heroicons/react/24/outline'
import { Popover, Combobox, Disclosure, Dialog } from '@headlessui/react'
import { ChevronUpIcon, PlusIcon, CheckIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid'
import { QuestionFilters } from './QuestionFilters'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// Initialize dayjs plugins
dayjs.extend(utc)
dayjs.extend(timezone)

export const FilterBar = ({
  filters,
  onFilterChange,
  onQuestionFilterChange,
  availableFilters,
  availableQuestionFilters,
  className,
}) => {
  const filterSections = [
    {
      id: 'quality',
      name: 'Quality Filters',
      icon: ShieldCheckIcon,
      filters: [
        { key: 'isHighQuality', label: 'High Quality' },
        { key: 'isLowEffort', label: 'Low Effort' },
        { key: 'isIrrelevant', label: 'Irrelevant' },
        { key: 'isDuplicate', label: 'Duplicate' },
        { key: 'straightLiningDetected', label: 'Straight-lining Detected' },
        { key: 'copyPastedDetected', label: 'Copy/Paste Detected' },
      ],
    },
    {
      id: 'security',
      name: 'Security Filters',
      icon: ShieldCheckIcon,
      filters: [
        { key: 'vpnDetected', label: 'VPN Detected' },
        { key: 'proxyDetected', label: 'Proxy Detected' },
      ],
    },
    {
      id: 'location',
      name: 'Location',
      icon: MapPinIcon,
      component: (
        <LocationFilter
          filters={filters}
          onFilterChange={onFilterChange}
          availableFilters={availableFilters}
        />
      ),
    },
    {
      id: 'questions',
      name: 'Question Filters',
      icon: QuestionMarkCircleIcon,
      component: (
        <QuestionFilters
          questions={availableQuestionFilters}
          currentFilters={filters.questionFilters || []}
          onQuestionFilterChange={onQuestionFilterChange}
        />
      ),
    },
    {
      id: 'status',
      name: 'Status Filters',
      icon: IdentificationIcon,
      filters: [
        { key: 'isComplete', label: 'Completed Responses' },
        { key: 'hasContactDetails', label: 'Has Contact Details' },
      ],
    },
    {
      id: 'sentiment',
      name: 'Sentiment',
      icon: QuestionMarkCircleIcon,
      component: (
        <SentimentFilter
          value={filters.sentiment}
          onChange={(value) => onFilterChange('sentiment', value)}
        />
      ),
    },
    {
      id: 'time',
      name: 'Time Range',
      icon: ClockIcon,
      component: (
        <DateRangeFilter
          value={filters.submissionTime}
          onChange={(value) => onFilterChange('submissionTime', value)}
        />
      ),
    },
  ]

  return (
    <div className={classNames('flex flex-col gap-4', className)}>
      {/* Filter sections - removed header with counts and actions */}
      <div className="flex flex-col gap-2">
        {filterSections.map((section) => (
          <Disclosure
            key={section.id}
            defaultOpen={true}
            as="div"
            className="rounded-lg bg-base-200"
          >
            {({ open }) => (
              <>
                <Disclosure.Button className="flex w-full items-center justify-between rounded-lg px-4 py-2 text-left text-sm font-medium hover:bg-base-300">
                  <div className="flex items-center gap-2">
                    <section.icon className="h-4 w-4" />
                    <span>{section.name}</span>
                  </div>
                  <ChevronUpIcon
                    className={classNames(
                      'h-4 w-4 transition-transform',
                      open ? '' : 'rotate-180'
                    )}
                  />
                </Disclosure.Button>
                <Disclosure.Panel className="px-4 pb-3">
                  {section.component || (
                    <div className="flex flex-col gap-2">
                      {section.filters.map((filter) => (
                        <label
                          key={filter.key}
                          className="flex cursor-pointer items-center gap-2"
                        >
                          <input
                            type="checkbox"
                            className="checkbox checkbox-sm"
                            checked={!!filters[filter.key]}
                            onChange={(e) =>
                              onFilterChange(filter.key, e.target.checked)
                            }
                          />
                          <span className="text-sm">{filter.label}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </Disclosure.Panel>
              </>
            )}
          </Disclosure>
        ))}
      </div>
    </div>
  )
}

// Subcomponents for specific filter types
const LocationFilter = ({ filters, onFilterChange, availableFilters }) => {
  const [countryQuery, setCountryQuery] = useState('')
  const [cityQuery, setCityQuery] = useState('')

  // Get available locations from filters
  const countryFilter = availableFilters?.find(f => f.key === 'submittedCountry')
  const cityFilter = availableFilters?.find(f => f.key === 'submittedCity')

  const countries = countryFilter?.options || []
  const cities = cityFilter?.options || []

  const filteredCountries = countryQuery === ''
    ? countries
    : countries.filter((country) =>
        country.label.toLowerCase().includes(countryQuery.toLowerCase())
      )

  const filteredCities = cityQuery === ''
    ? cities
    : cities.filter((city) =>
        city.label.toLowerCase().includes(cityQuery.toLowerCase())
      )

  return (
    <div className="flex flex-col gap-3">
      {/* Country Combobox */}
      <Combobox
        value={filters.submittedCountry || ''}
        onChange={(value) => onFilterChange('submittedCountry', value)}
      >
        <div className="relative">
          <div className="relative w-full cursor-default overflow-hidden rounded-lg bg-base-100 text-left focus:outline-none sm:text-sm">
            <Combobox.Input
              className="input input-sm w-full"
              placeholder="Select country"
              displayValue={(country) => {
                const option = countries.find(c => c.value === country)
                return option?.label || country
              }}
              onChange={(event) => setCountryQuery(event.target.value)}
            />
            <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-4 w-4 text-base-content/70"
                aria-hidden="true"
              />
            </Combobox.Button>
          </div>
          <Combobox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-base-100 py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
            {filteredCountries.length === 0 && countryQuery !== '' ? (
              <div className="relative cursor-default select-none py-2 px-4 text-base-content/70">
                Nothing found.
              </div>
            ) : (
              filteredCountries.map((country) => (
                <Combobox.Option
                  key={country.value}
                  className={({ active }) =>
                    `relative cursor-default select-none py-2 pl-10 pr-4 ${
                      active ? 'bg-primary text-primary-content' : 'text-base-content'
                    }`
                  }
                  value={country.value}
                >
                  {({ selected, active }) => (
                    <>
                      <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                        {country.label}
                      </span>
                      {selected ? (
                        <span
                          className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                            active ? 'text-primary-content' : 'text-primary'
                          }`}
                        >
                          <CheckIcon className="h-4 w-4" aria-hidden="true" />
                        </span>
                      ) : null}
                    </>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </div>
      </Combobox>

      {/* City Combobox */}
      <Combobox
        value={filters.submittedCity || ''}
        onChange={(value) => onFilterChange('submittedCity', value)}
      >
        <div className="relative">
          <div className="relative w-full cursor-default overflow-hidden rounded-lg bg-base-100 text-left focus:outline-none sm:text-sm">
            <Combobox.Input
              className="input input-sm w-full"
              placeholder="Select city"
              displayValue={(city) => {
                const option = cities.find(c => c.value === city)
                return option?.label || city
              }}
              onChange={(event) => setCityQuery(event.target.value)}
            />
            <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-4 w-4 text-base-content/70"
                aria-hidden="true"
              />
            </Combobox.Button>
          </div>
          <Combobox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-base-100 py-1 text-sm shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
            {filteredCities.length === 0 && cityQuery !== '' ? (
              <div className="relative cursor-default select-none py-2 px-4 text-base-content/70">
                Nothing found.
              </div>
            ) : (
              filteredCities.map((city) => (
                <Combobox.Option
                  key={city.value}
                  className={({ active }) =>
                    `relative cursor-default select-none py-2 pl-10 pr-4 ${
                      active ? 'bg-primary text-primary-content' : 'text-base-content'
                    }`
                  }
                  value={city.value}
                >
                  {({ selected, active }) => (
                    <>
                      <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                        {city.label}
                      </span>
                      {selected ? (
                        <span
                          className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                            active ? 'text-primary-content' : 'text-primary'
                          }`}
                        >
                          <CheckIcon className="h-4 w-4" aria-hidden="true" />
                        </span>
                      ) : null}
                    </>
                  )}
                </Combobox.Option>
              ))
            )}
          </Combobox.Options>
        </div>
      </Combobox>
    </div>
  )
}

const SentimentFilter = ({ value, onChange }) => {
  return (
    <select
      className="select select-sm w-full"
      value={value || ''}
      onChange={(e) => onChange(e.target.value || null)}
    >
      <option value="">Any sentiment</option>
      <option value="positive">Positive</option>
      <option value="negative">Negative</option>
      <option value="neutral">Neutral</option>
    </select>
  )
}

const DateRangeFilter = ({ value = {}, onChange }) => {
  const handleChange = (field, dateValue) => {
    if (!dateValue) {
      const newValue = { ...value, [field]: null }
      if (!newValue.start && !newValue.end) {
        onChange(null)
      } else {
        onChange(newValue)
      }
      return
    }

    // Convert local datetime to UTC ISO string
    const utcDate = dayjs(dateValue).utc().format()
    
    const newValue = { ...value, [field]: utcDate }
    if (!newValue.start && !newValue.end) {
      onChange(null)
    } else {
      onChange(newValue)
    }
  }

  // Convert UTC dates back to local for display
  const localStart = value?.start ? dayjs(value.start).format('YYYY-MM-DDTHH:mm') : ''
  const localEnd = value?.end ? dayjs(value.end).format('YYYY-MM-DDTHH:mm') : ''

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-1">
        <label className="text-xs">From</label>
        <input
          type="datetime-local"
          className="input input-sm w-full"
          value={localStart}
          onChange={(e) => handleChange('start', e.target.value)}
        />
      </div>
      <div className="flex flex-col gap-1">
        <label className="text-xs">To</label>
        <input
          type="datetime-local"
          className="input input-sm w-full"
          value={localEnd}
          onChange={(e) => handleChange('end', e.target.value)}
        />
      </div>
    </div>
  )
} 