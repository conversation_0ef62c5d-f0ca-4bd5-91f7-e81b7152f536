export const StatusBadge = ({ color, label, tooltip }) => {
  const colors = {
    green: 'bg-green-100 text-green-700 fill-green-500',
    red: 'bg-red-100 text-red-700 fill-red-500',
    yellow: 'bg-yellow-100 text-yellow-800 fill-yellow-500',
    gray: 'bg-gray-100 text-gray-600 fill-gray-400',
  }

  return (
    <div className="tooltip tooltip-bottom" data-tip={tooltip}>
      <span className={`inline-flex items-center gap-x-1.5 rounded-full px-1.5 py-0.5 text-xs font-medium ${colors[color]}`}>
        <svg viewBox="0 0 6 6" aria-hidden="true" className="size-1.5">
          <circle r={3} cx={3} cy={3} />
        </svg>
        {label}
      </span>
    </div>
  )
} 