import { useState, useEffect } from 'react'
import { useMutation, useQuery } from '@redwoodjs/web'
import { navigate, useLocation } from '@redwoodjs/router'
import { FilterBarCell } from './FilterBarCell'
import { ResponseTable } from './ResponseTable'
import { Dialog, DialogPanel, DialogBackdrop } from '@headlessui/react'
import { XMarkIcon, FunnelIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'
import qs from 'qs'
import { toast } from '@redwoodjs/web/toast'

export const EXPORT_UNIFIED_RESPONSES_MUTATION = gql`
  mutation exportUnifiedResponses(
   $input: ExportUnifiedResponsesInput!
  ) {
    exportUnifiedResponses(
      input: $input
    ) {
      count
      email
    }
  }
`

const UNIFIED_RESPONSES_QUERY = gql`
  query UnifiedSurveyResponses(
    $surveyId: String!
    $filters: UnifiedResponseFilters
    $page: Int
    $pageSize: Int
    $sortField: String
    $sortDirection: String
  ) {
    unifiedSurveyResponses(
      surveyId: $surveyId
      filters: $filters
      page: $page
      pageSize: $pageSize
      sortField: $sortField
      sortDirection: $sortDirection
    ) {
      responses {
        id
        surveyId
        submissionTime
        participantEmail
        questionResponses {
          questionId
          response
          title
          type
        }
        authenticityScore
        overallResponseQualityScore
        effortScorePerOpenEndedQuestion
        relevanceScorePerOpenEndedQuestion
        responseQualityExplanationPerOpenEndedQuestion
        keystrokePatternScorePerOpenEndedQuestion
        timeSpentOnSurvey
        keystrokePatternScore
        straightLiningDetected
        isDuplicate
        copyPastedDetected
        vpnDetected
        proxyDetected
        submittedCountry
        submittedCity
        submittedLatitude
        submittedLongitude
        deviceInfo {
          browser
          os
          device
          screenResolution
        }
        submittedTooFast
        isHighQuality
        isLowEffort
        isIrrelevant
        hasContactDetails
        questionStatuses
        isGradingComplete
        submissionContactDetails
      }
      pageInfo {
        hasNextPage
        endCursor
        totalPages
        currentPage
        totalCount
      }
      context {
        availableFilters {
          key
          type
          label
          description
          defaultValue
          options {
            value
            label
          }
          validation {
            min
            max
            required
            pattern
          }
          stat
          previousStat
        }
        availableQuestionFilters {
          id
          title
          type
          filterType
          choices
          min
          max
          allowMultiple
          allowWriteIn
          rows
          columns
          cellType
          fields
        }
      }
    }
  }
`

const UNIFIED_FILTERS_QUERY = gql`
  query UnifiedFiltersQuery($surveyId: String!) {
    unifiedSurveyFilters(surveyId: $surveyId) {
      availableFilters {
        key
        type
        label
        description
        options {
          value
          label
        }
      }
      availableQuestionFilters {
        id
        title
        type
        filterType
        choices
        min
        max
        allowMultiple
        allowWriteIn
        rows
        columns
      }
    }
  }
`

export const UnifiedResponseAnalyticsCell = ({ surveyId }) => {
  const location = useLocation()
  const [currentPage, setCurrentPage] = useState(1)
  const [sortConfig, setSortConfig] = useState({
    field: 'submissionTime',
    direction: 'desc',
  })
  const [filters, setFilters] = useState({
    questionFilters: [],
  })

  const [exportData, { loading: exportLoading, error: exportError }] = useMutation(EXPORT_UNIFIED_RESPONSES_MUTATION)
  // Initial state setup from URL parameters
  useEffect(() => {
    const params = qs.parse(location.search.substring(1))
    if (params.page) setCurrentPage(Number(params.page))
    if (params.sort) setSortConfig(JSON.parse(decodeURIComponent(params.sort)))
    if (params.filters) {
      const urlFilters = JSON.parse(decodeURIComponent(params.filters))
      setFilters(urlFilters)
    }
  }, [location.search])

  // Separate query for filters
  const { data: filtersData } = useQuery(UNIFIED_FILTERS_QUERY, {
    variables: { surveyId },
  })

  // Query for responses with filters
  const { loading, error, data } = useQuery(UNIFIED_RESPONSES_QUERY, {
    variables: {
      surveyId,
      filters,
      page: currentPage,
      pageSize: 20,
      sortField: sortConfig.field,
      sortDirection: sortConfig.direction
    },
    fetchPolicy: 'network-only',
  })

  useEffect(() => {
    setCurrentPage(1)
  }, [filters])

  const handleFilterChange = (key, value) => {
    // If value is null/undefined, remove the filter
    setFilters(prev => {
      if (value === null || value === undefined) {
        const { [key]: removed, ...rest } = prev
        return rest
      }
      return { ...prev, [key]: value }
    })
  }

  const handleQuestionFilterChange = (questionId, filterValue) => {
    setFilters(prev => ({
      ...prev,
      questionFilters: [
        ...(prev.questionFilters || []).filter(f => f.questionId !== questionId),
        filterValue ? {
          questionId,
          type: filterValue.type,
          ...filterValue
        } : null
      ].filter(Boolean)
    }))
  }

  const handleExport = async () => {
    // Implement your export logic here
    const queryParams = {
      surveyId,
      filters,
      sortField: sortConfig.field || "submissionTime",
      sortDirection: sortConfig.direction || "desc"
    }

    const { data } = await exportData(
      {
        variables:
          { input: { ...queryParams } }
      });
    console.log(data);
    toast.success(`Exported ${data.exportUnifiedResponses.count} responses and sent to ${data.exportUnifiedResponses.email}`, {
      duration: 4000,
    })
  }

  if (error) return <div className="alert alert-error">Error: {error.message}</div>

  return (
    <div className="h-full w-full flex flex-col">
      <div className="flex-1 overflow-hidden">
        <div className="h-full grid grid-cols-4 gap-6">
          {/* Filters Column */}
          <div className="col-span-1 overflow-y-auto">
            <FilterBarCell
              surveyId={surveyId}
              filters={filters}
              onFilterChange={handleFilterChange}
              onQuestionFilterChange={handleQuestionFilterChange}
            />
          </div>

          {/* Content Column */}
          <div className="col-span-3 overflow-y-auto">
            <div className="sticky top-0 z-10 flex justify-between items-center p-1 bg-base-100/50 backdrop-blur-sm mb-2">
              <span className="text-sm text-base-content/70">
                {data?.unifiedSurveyResponses.pageInfo.totalCount || 0} responses
              </span>
              <button
                onClick={handleExport}
                className="btn btn-ghost btn-sm"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                Export
              </button>
            </div>

            {loading || exportLoading ? (
              <div className="space-y-4">
                <div className="skeleton h-8 w-full"></div>
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="skeleton h-16 w-full"></div>
                ))}
              </div>
            ) : (
              data && (
                <ResponseTable
                  responses={data.unifiedSurveyResponses.responses}
                  sortConfig={sortConfig}
                  onSort={setSortConfig}
                  page={currentPage}
                  pageInfo={data.unifiedSurveyResponses.pageInfo}
                  onPageChange={setCurrentPage}
                />
              )
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UnifiedResponseAnalyticsCell
