import { useState } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import {
  EnvelopeIcon,
  PaperAirplaneIcon,
  EyeIcon,
  TagIcon,
} from '@heroicons/react/24/outline'

const SEND_SURVEY_EMAILS = gql`
  mutation SendSurveyEmails($input: SendEmailInput!) {
    sendSurveyEmails(input: $input)
  }
`

const EmailComposerForm = ({ 
  surveyId, 
  participantStats, 
  selectedParticipants = [],
  onClose 
}) => {
  const [emailSubject, setEmailSubject] = useState('')
  const [emailBody, setEmailBody] = useState('')
  const [previewMode, setPreviewMode] = useState(false)
  const [sending, setSending] = useState(false)
  
  const [sendSurveyEmails] = useMutation(SEND_SURVEY_EMAILS, {
    onCompleted: () => {
      toast.success('Emails queued for sending')
      setSending(false)
      if (onClose) onClose()
    },
    onError: (error) => {
      toast.error(`Failed to send emails: ${error.message}`)
      setSending(false)
    },
  })
  
  const handleSendEmails = async () => {
    if (!emailSubject || !emailBody) {
      toast.error('Please provide both subject and body for the email')
      return
    }
    
    setSending(true)
    
    try {
      await sendSurveyEmails({
        variables: {
          input: {
            surveyId,
            templateId: 'default', // This will be replaced with actual template ID when implemented
            participantIds: selectedParticipants,
          },
        },
      })
    } catch (error) {
      console.error('Error sending emails:', error)
    }
  }
  
  const renderMergeTagsHelper = () => (
    <div className="bg-base-200 p-4 rounded-lg mb-6">
      <div className="flex items-center mb-2">
        <TagIcon className="h-5 w-5 mr-2 text-primary" />
        <h3 className="text-sm font-medium">Available Merge Tags</h3>
      </div>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <code className="bg-base-300 px-1 py-0.5 rounded">{{ participant_name }}</code>
          <span className="ml-2 text-base-content/70">Recipient's name</span>
        </div>
        <div>
          <code className="bg-base-300 px-1 py-0.5 rounded">{{ survey_link }}</code>
          <span className="ml-2 text-base-content/70">Personalized survey link</span>
        </div>
        <div>
          <code className="bg-base-300 px-1 py-0.5 rounded">{{ survey_title }}</code>
          <span className="ml-2 text-base-content/70">Survey title</span>
        </div>
        <div>
          <code className="bg-base-300 px-1 py-0.5 rounded">{{ current_date }}</code>
          <span className="ml-2 text-base-content/70">Today's date</span>
        </div>
      </div>
    </div>
  )
  
  const renderPreview = () => {
    // Simple placeholder preview - in a real implementation, 
    // this would use a rich preview with actual merge tag replacements
    return (
      <div className="border rounded-lg p-4">
        <div className="bg-base-100 rounded-lg p-4 mb-4">
          <div className="mb-2 text-xs text-base-content/70">Subject:</div>
          <h3 className="text-base font-medium">{emailSubject || '(No subject)'}</h3>
        </div>
        
        <div className="bg-base-100 rounded-lg p-4">
          <div className="mb-2 text-xs text-base-content/70">Message:</div>
          <div className="prose max-w-none">
            {emailBody ? (
              <div dangerouslySetInnerHTML={{ __html: emailBody.replace(/\n/g, '<br/>') }} />
            ) : (
              <p className="text-base-content/50">(No message content)</p>
            )}
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="max-w-3xl mx-auto p-4">
      <div className="flex items-center mb-4">
        <EnvelopeIcon className="h-5 w-5 mr-2" />
        <h2 className="text-lg font-medium">Email Composer</h2>
        
        <div className="ml-auto">
          <button
            className={`btn btn-sm ${previewMode ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setPreviewMode(!previewMode)}
          >
            <EyeIcon className="h-4 w-4 mr-1" />
            {previewMode ? 'Edit' : 'Preview'}
          </button>
        </div>
      </div>
      
      <div className="alert alert-info mb-4 text-xs">
        <div>
          {selectedParticipants.length > 0 ? (
            <p>
              You are about to send emails to <strong>{selectedParticipants.length}</strong> selected participants.
            </p>
          ) : (
            <p>
              You are about to send emails to <strong>{participantStats?.pending || 0}</strong> pending participants.
            </p>
          )}
        </div>
      </div>
      
      {!previewMode ? (
        <div>
          {renderMergeTagsHelper()}
          
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text">Email Subject</span>
            </label>
            <input
              type="text"
              className="input input-bordered w-full"
              placeholder="Enter email subject"
              value={emailSubject}
              onChange={(e) => setEmailSubject(e.target.value)}
            />
          </div>
          
          <div className="form-control mb-6">
            <label className="label">
              <span className="label-text">Email Content</span>
            </label>
            <textarea
              className="textarea textarea-bordered h-64"
              placeholder="Enter email content. You can use merge tags like {{ participant_name }} or {{ survey_link }}."
              value={emailBody}
              onChange={(e) => setEmailBody(e.target.value)}
            />
          </div>
        </div>
      ) : (
        renderPreview()
      )}
      
      <div className="flex justify-end mt-6 space-x-2">
        <button className="btn btn-ghost" onClick={onClose}>
          Cancel
        </button>
        <button
          className="btn btn-primary"
          onClick={handleSendEmails}
          disabled={!emailSubject || !emailBody || sending}
        >
          {sending ? (
            <>
              <span className="loading loading-spinner loading-xs mr-2"></span>
              Sending...
            </>
          ) : (
            <>
              <PaperAirplaneIcon className="h-4 w-4 mr-2" />
              Send Emails
            </>
          )}
        </button>
      </div>
    </div>
  )
}

export default EmailComposerForm 