import EndUserSurveyResponseSubmit from './EndUserSurveyResponseSubmit'

export default { component: EndUserSurveyResponseSubmit }

// Mock data for different scenarios

const mockDataVersion1 = {
  surveyDetail: {
    id: 42,
    surveyName: 'Customer Satisfaction Survey',
    createdBy: '<PERSON>',
    createdOn: '2024-01-15T12:34:56Z',
    subheading: 'Feedback on customer service',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What is your name?',
        questionConfig: {
          choices: [],
          maxLength: '50',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'How satisfied are you with our service?',
        questionConfig: {
          choices: [
            'Very satisfied',
            'Satisfied',
            'Neutral',
            'Dissatisfied',
            'Very dissatisfied',
          ],
          rules: [],
          condition: 'all',
          actions: [],
          allowMultiple: false,
          allowWriteIn: false,
        },
      },
      {
        id: 3,
        type: 'rating',
        title: 'Rate our service on a scale of 1 to 5.',
        questionConfig: {
          choices: [],
          maxLength: '',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
}

const mockDataVersion2 = {
  surveyDetail: {
    id: 43,
    surveyName: 'Product Feedback Survey',
    createdBy: 'John <PERSON>',
    createdOn: '2024-02-20T12:34:56Z',
    subheading: 'Opinions on new product features',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What product did you purchase?',
        questionConfig: {
          choices: [],
          maxLength: '100',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'Which features do you like?',
        questionConfig: {
          choices: ['Feature A', 'Feature B', 'Feature C'],
          rules: [],
          condition: 'all',
          actions: [{ type: 'skip', questionNumber: '4' }],
          allowMultiple: true,
          allowWriteIn: true,
        },
      },
      {
        id: 3,
        type: 'scale',
        title: 'How would you rate the usability?',
        questionConfig: {
          choices: [],
          maxLength: '',
          rules: [{ operation: '>', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: '' }],
        },
      },
      {
        id: 4,
        type: 'rating',
        title: 'Rate the product on a scale of 1 to 5.',
        questionConfig: {
          choices: [],
          maxLength: '',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
}

const mockDataVersion3 = {
  surveyDetail: {
    id: 44,
    surveyName: 'Employee Engagement Survey',
    createdBy: 'Sarah Johnson',
    createdOn: '2024-03-10T12:34:56Z',
    subheading: 'Workplace satisfaction and engagement',
    questions: [
      {
        id: 1,
        type: 'short',
        title: 'What is your department?',
        questionConfig: {
          choices: [],
          maxLength: '50',
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
      {
        id: 2,
        type: 'multiple',
        title: 'How do you feel about your work environment?',
        questionConfig: {
          choices: ['Positive', 'Neutral', 'Negative'],
          rules: [],
          condition: 'all',
          actions: [{ type: 'skip', questionNumber: '4' }],
          allowMultiple: false,
          allowWriteIn: false,
        },
      },
      {
        id: 3,
        type: 'rating',
        title: 'Rate your job satisfaction on a scale of 1 to 5.',
        questionConfig: {
          choices: [],
          maxLength: '',
          rules: [{ operation: '<', value: '3' }],
          condition: 'all',
          actions: [{ type: 'end survey', questionNumber: '' }],
        },
      },
      {
        id: 4,
        type: 'scale',
        title:
          'How likely are you to recommend this company as a good place to work?',
        questionConfig: {
          choices: [],
          minLength: 0,
          maxLength: 1000,
          rules: [],
          condition: 'all',
          actions: [],
        },
      },
    ],
  },
}

export const Primary = {
  args: {
    surveyId: 42,
  },
  parameters: {
    mocks: {
      Query: {
        SurveyDetailQuery: () => mockDataVersion1,
      },
    },
  },
}

export const ProductFeedbackSurvey = {
  args: {
    surveyId: 43,
  },
  parameters: {
    mocks: {
      Query: {
        SurveyDetailQuery: () => mockDataVersion2,
      },
    },
  },
}

export const EmployeeEngagementSurvey = {
  args: {
    surveyId: 44,
  },
  parameters: {
    mocks: {
      Query: {
        SurveyDetailQuery: () => mockDataVersion3,
      },
    },
  },
}
