import { useState, useEffect, useMemo } from 'react'
import { ClientJS } from 'clientjs'
import { Form, Submit, FormError } from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { generateCuid } from 'src/lib/cuid'
import { SurveyProvider } from 'src/components/SurveyQuestions/SurveyContext'
import QuestionDisplay from 'src/components/SurveyQuestions/QuestionDisplay'
import { getRuleOperations } from 'src/components/QuestionForm/RuleActions';
const CREATE_SURVEY_RESPONSE_MUTATION = gql`
  mutation CreateSurveyResponse($input: CreateSurveyResponseInput!) {
    createSurveyResponse(input: $input) {
      id
      surveyId
    }
  }
`

const CREATE_QUESTION_RESPONSE_MUTATION = gql`
  mutation CreateNewQuestionResponse($input: CreateQuestionResponseInput!) {
    createQuestionResponse(input: $input) {
      id
      questionId
    }
  }
`

const client = new ClientJS()

const defaultTheme = {
  questionTextColor: 'hsl(var(--bc))', // base content color
  answerTextColor: 'hsl(var(--bc) / 0.8)', // base content with opacity
  buttonColor: 'hsl(var(--p))', // primary color
  backgroundColor: 'hsl(var(--b2))', // base-100 color
  progressBarColor: 'hsl(var(--p))', // primary color
  buttonTextColor: 'hsl(var(--bc))', // base content color
}

const EndUserSurveyResponseSubmit = ({ survey, participantEmail, updateProgress, token, completeSurveyParticipant }) => {
  const storageKey = `${survey.id}_surveyResponseId`;
  const [surveyResponseId, setSurveyResponseId] = useState(null)
  const [currentPage, setCurrentPage] = useState(0)
  const [answers, setAnswers] = useState({})
  const [formErrors, setFormErrors] = useState({})
  const [isSurveyCompleted, setIsSurveyCompleted] = useState(false)
  const [pastedAnswers, setPastedAnswers] = useState({})
  const [keystrokeData, setKeystrokeData] = useState({})
  const [opacity, setOpacity] = useState(1)
  const [startTime] = useState(new Date())
  const [skipList, setSkipList] = useState(new Set());

  // Modified question grouping logic
  const questionsByPage = useMemo(() => {
    const grouped = survey.questions.reduce((acc, question) => {
      const page = question.pageNumber || 0
      if (!acc[page]) acc[page] = []
      acc[page].push(question)
      return acc
    }, {})

    // If all questions have the same page number, create individual pages
    const pages = Object.keys(grouped)
    if (pages.length === 1) {
      // Create a page for each question
      return survey.questions.reduce((acc, question, index) => {
        if (!skipList.has(question.order + 1)) {
          acc[index] = [question]
        }
        return acc
      }, {})
    }

    return grouped
  }, [survey.questions])

  const currentQuestions = questionsByPage[currentPage] || []
  /* assuming the skip list is possible only with single pages for now
  TODO: if we have to support skip based on page and question . we should think of in v2*/

  const totalPages = skipList.size > 0 ? survey.questions.length : Object.keys(questionsByPage).length

  const [createSurveyResponse] = useMutation(CREATE_SURVEY_RESPONSE_MUTATION, {
    onCompleted: (data) => {
      setSurveyResponseId(data.createSurveyResponse.id)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const [createQuestionResponse] = useMutation(CREATE_QUESTION_RESPONSE_MUTATION, {
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleInputChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }))
    setFormErrors(prev => ({
      ...prev,
      [questionId]: null
    }))
  }

  const handleKeystroke = (questionId, event) => {
    const timestamp = new Date().getTime()
    setKeystrokeData(prev => ({
      ...prev,
      [questionId]: [
        ...(prev[questionId] || []),
        {
          key: event.key,
          timestamp
        }
      ]
    }))
  }

  const handlePaste = (questionId) => {
    setPastedAnswers(prev => ({
      ...prev,
      [questionId]: true
    }))
    toast.success('Content pasted into answer field')
  }

  const validateCurrentPage = () => {
    const errors = {}
    let hasErrors = false

    for (const question of currentQuestions) {
      if (question.questionConfig?.minLength && answers[question.id]?.length < question.questionConfig.minLength) {
        errors[question.id] = [`Minimum length is ${question.questionConfig.minLength}`]
        hasErrors = true
      }
      if (question.required && !answers[question.id]) {
        errors[question.id] = ['This field is required']
        hasErrors = true
      }
    }

    return { errors, hasErrors }
  }

  const submitQuestionResponses = async (questions, surveyRespId) => {
    const endTime = new Date()
    const timeToSubmit = (endTime - startTime) / 1000

    for (const question of questions) {
      const response = answers[question.id]
      if (!response) continue

      await createQuestionResponse({
        variables: {
          input: {
            surveyResponseId: surveyRespId,
            surveyId: survey.id,
            questionId: question.id,
            questionNumber: question.order,
            pageNumber: question.pageNumber || 0,
            response: JSON.stringify(response),
            timeToSubmit,
            keystrokeTimingData: keystrokeData[question.id] || [],
            isResponseCopyPasted: Boolean(pastedAnswers[question.id]),
            submissionTime: endTime,
          },
        },
      })
    }
  }

  const handleNextPage = async () => {
    const { errors, hasErrors } = validateCurrentPage()
    if (hasErrors) {
      setFormErrors(errors)
      return
    }

    try {
      // First submission - create survey response
      let surveyResponseId = localStorage.getItem(storageKey);
      if (!surveyResponseId) {
        const firstQuestion = currentQuestions[0]
        const endTime = new Date()
        const timeToSubmit = (endTime - startTime) / 1000

        const result = await createSurveyResponse({
          variables: {
            input: {
              surveyId: survey.id,
              participantEmail,
              participantId: generateCuid({}),
              fingerprint: client.getFingerprint().toString(),
              screenResolution: `${window.screen.width}x${window.screen.height}`,
              timezone: client.getTimeZone(),
              userAgent: client.getUserAgent(),
              submissionTime: endTime,
              lastActiveTime: endTime,
              questionResponse: {
                questionId: firstQuestion.id,
                questionNumber: firstQuestion.order || 0,
                pageNumber: firstQuestion.pageNumber || 0, // Handle no pageNumber
                response: JSON.stringify(answers[firstQuestion.id]),
                timeToSubmit,
                keystrokeTimingData: keystrokeData[firstQuestion.id] || [],
                isResponseCopyPasted: Boolean(pastedAnswers[firstQuestion.id]),
                submissionTime: endTime,
              },
            },
          },
        })

        surveyResponseId = result.data.createSurveyResponse.id;
        localStorage.setItem(storageKey, surveyResponseId);
        // Submit remaining questions
        await submitQuestionResponses(currentQuestions.slice(1), result.data.createSurveyResponse.id)
      } else {
        // Submit current page questions
        await submitQuestionResponses(currentQuestions, surveyResponseId)
      }

      // Handle completion or next page
      if (currentPage === totalPages - 1) {
        setIsSurveyCompleted(true)
        updateProgress(100)
        localStorage.removeItem(storageKey);

        // If token is provided, update participant status to COMPLETED
        if (token && completeSurveyParticipant) {
          console.log('Calling completeSurveyParticipant with token:', token)
          completeSurveyParticipant({
            variables: { token }
          }).then((result) => {
            console.log('Survey completion result:', result)
          }).catch((error) => {
            console.error('Survey completion error:', error)
          })
        }

        toast.success('Survey completed successfully!')
      } else {
        setOpacity(0)
        setTimeout(() => {
          setCurrentPage(prev => prev + 1)
          setOpacity(1)
          // Calculate progress based on total questions for no-page surveys
          const progress = totalPages === 1
            ? ((currentQuestions.indexOf(currentQuestions[0]) + 2) / currentQuestions.length) * 100
            : ((currentPage + 2) / totalPages) * 100
          updateProgress(Math.min(progress, 100))
        }, 300)
      }

      //Handle rules
      currentQuestions.forEach((question) => {
        const { type, questionConfig } = question;
        const { rules, actions } = questionConfig;
        // if (!question.required)
        //   return;
        // Determine the applicable operations based on question type
        const applicableOperations = getRuleOperations(type);


        // Check if the user's response satisfies any rule
        const userResponse = answers[question.id];
        const ruleSatisfied = questionConfig.condition === 'all'
          ? rules?.every((rule) => {
            const operation = applicableOperations.find(op => op.value === rule.operation);
            if (!operation) return false;

            switch (rule.operation) {
              case 'is selected':
                return userResponse.includes(rule.value);
              case 'is not selected':
                return !userResponse.includes(rule.value);
              case '>':
                return userResponse > rule.value;
              case '<':
                return userResponse < rule.value;
              case '>=':
                return userResponse >= rule.value;
              case '<=':
                return userResponse <= rule.value;
              case '==':
                return userResponse == rule.value;
              case '!=':
                return userResponse != rule.value;
              case 'is':
                return userResponse === rule.value;
              case 'is not':
                return userResponse !== rule.value;
              case 'begins with':
                return userResponse.startsWith(rule.value);
              case 'does not begin with':
                return !userResponse.startsWith(rule.value);
              case 'contains':
                return userResponse.includes(rule.value);
              case 'does not contain':
                return !userResponse.includes(rule.value);
              case 'is blank':
                return userResponse === '';
              case 'is not blank':
                return userResponse !== '';
              default:
                return false;
            }
          })
          : rules?.some((rule) => {
            const operation = applicableOperations.find(op => op.value === rule.operation);
            if (!operation) return false;

            switch (rule.operation) {
              case 'is selected':
                return userResponse.includes(rule.value);
              case 'is not selected':
                return !userResponse.includes(rule.value);
              case '>':
                return userResponse > rule.value;
              case '<':
                return userResponse < rule.value;
              case '>=':
                return userResponse >= rule.value;
              case '<=':
                return userResponse <= rule.value;
              case '==':
                return userResponse == rule.value;
              case '!=':
                return userResponse != rule.value;
              case 'is':
                return userResponse === rule.value;
              case 'is not':
                return userResponse !== rule.value;
              case 'begins with':
                return userResponse.startsWith(rule.value);
              case 'does not begin with':
                return !userResponse.startsWith(rule.value);
              case 'contains':
                return userResponse.includes(rule.value);
              case 'does not contain':
                return !userResponse.includes(rule.value);
              case 'is blank':
                return userResponse === '';
              case 'is not blank':
                return userResponse !== '';
              default:
                return false;
            }
          });
        // Execute actions if any rule is satisfied
        if (ruleSatisfied) {
          actions.forEach((action) => {
            switch (action.type) {
              case 'skip':
                // Logic to hide a specific question
                setAnswers(prev => ({
                  ...prev,
                  [action.questionNumber]: null
                }));
                // Logic to add the question number to the skipList
                setSkipList(prev => new Set(prev).add(action.questionNumber));
                break;
              case 'go to':
                // Logic to go to a specific question
                setCurrentPage(action.questionNumber - 1);
                break;
              case 'end survey':
                setIsSurveyCompleted(true);
                updateProgress(100);
                localStorage.removeItem(storageKey);

                // If token is provided, update participant status to COMPLETED
                if (token && completeSurveyParticipant) {
                  console.log('Calling completeSurveyParticipant with token (end survey action):', token)
                  completeSurveyParticipant({
                    variables: { token }
                  }).then((result) => {
                    console.log('Survey completion result (end survey action):', result)
                  }).catch((error) => {
                    console.error('Survey completion error (end survey action):', error)
                  })
                }

                toast.success('Survey completed successfully!');
                break;
              case 'disqualify participant':
                // Logic to disqualify participant
                toast.error('You have been disqualified from the survey.');
                setIsSurveyCompleted(true);
                break;
              default:
                break;
            }
          });
        }
      });

    } catch (error) {
      console.error('Error submitting:', error)
      toast.error('Failed to submit')
      localStorage.removeItem(storageKey);
    }
  }

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setOpacity(0)
      setTimeout(() => {
        setCurrentPage(prev => prev - 1)
        setOpacity(1)
        updateProgress((currentPage) / totalPages * 100)
      }, 300)
    }
  }

  // Update theme styles with defaults
  const themeStyles = {
    '--question-text': survey.settings?.themeConfig?.questionTextColor || defaultTheme.questionTextColor,
    '--answer-text': survey.settings?.themeConfig?.answerTextColor || defaultTheme.answerTextColor,
    '--primary': survey.settings?.themeConfig?.buttonColor || defaultTheme.buttonColor,
    '--background': survey.settings?.themeConfig?.backgroundColor || defaultTheme.backgroundColor,
    '--progress': survey.settings?.themeConfig?.progressBarColor || defaultTheme.progressBarColor,
    '--button-text': survey.settings?.themeConfig?.buttonTextColor || defaultTheme.buttonTextColor
  }

  if (isSurveyCompleted) {
    return (
      <div className="mx-auto" style={themeStyles}>
        <h1 className="text-2xl font-bold">Thank you for completing the survey!</h1>
      </div>
    )
  }

  return (
    <SurveyProvider value={{
      device: 'desktop',
      themeConfig: {
        ...defaultTheme,
        ...survey.settings?.themeConfig,
      }
    }}>
      <div className="mx-auto p-4" style={themeStyles}>
        <Form onSubmit={(e) => e.preventDefault()} className="form-control w-full">
          <div
            className="transition-opacity duration-300 ease-in-out"
            style={{ opacity }}
          >
            {currentQuestions.length > 0 ? currentQuestions.map(question => (
              !skipList.has(question.order + 1) ?
                (<div key={question.id} className="mb-6">
                  <QuestionDisplay
                    question={question}
                    value={answers[question.id]}
                    onChange={handleInputChange}
                    onKeystroke={handleKeystroke}
                    onPaste={handlePaste}
                    error={formErrors[question.id]}
                    themeConfig={survey.settings?.themeConfig}
                  />
                  {formErrors[question.id] && (
                    <div className="text-red-500 mt-1">
                      {formErrors[question.id].map((error, index) => (
                        <p key={index}>{error}</p>
                      ))}
                    </div>
                  )}
                </div>) :
                setCurrentPage(prev => prev + 1)
            )) : setTimeout(() => {
              if (currentPage <= totalPages - 1) {
                setIsSurveyCompleted(true);
                updateProgress(100);
                localStorage.removeItem(storageKey);

                // If token is provided, update participant status to COMPLETED
                if (token && completeSurveyParticipant) {
                  console.log('Calling completeSurveyParticipant with token (no questions):', token)
                  completeSurveyParticipant({
                    variables: { token }
                  }).then((result) => {
                    console.log('Survey completion result (no questions):', result)
                  }).catch((error) => {
                    console.error('Survey completion error (no questions):', error)
                  })
                }

                toast.success('Survey completed successfully!');
              } else {
                setCurrentPage(prev => prev + 1)
                setOpacity(1)
                updateProgress((currentPage) / totalPages * 100)
              }
            }, 50)}
          </div>
          <div className="mt-4 flex justify-between">
            {currentPage > 0 && (
              <button
                type="button"
                className="btn btn-outline btn-sm"
                style={{
                  borderColor: survey.settings?.themeConfig?.buttonColor || defaultTheme.buttonColor,
                  color: survey.settings?.themeConfig?.buttonTextColor || defaultTheme.buttonTextColor
                }}
                onClick={handlePreviousPage}
              >
                Previous
              </button>
            )}
            <button
              type="button"
              className="btn btn-sm ml-auto"
              style={{
                backgroundColor: survey.settings?.themeConfig?.buttonColor || defaultTheme.buttonColor,
                borderColor: survey.settings?.themeConfig?.buttonColor || defaultTheme.buttonColor,
                color: survey.settings?.themeConfig?.buttonTextColor || defaultTheme.buttonTextColor
              }}
              onClick={handleNextPage}
            >
              {currentPage === totalPages - 1 ? 'Submit' : 'Next'}
            </button>
          </div>
        </Form>
      </div>
    </SurveyProvider>
  )
}

export default EndUserSurveyResponseSubmit
