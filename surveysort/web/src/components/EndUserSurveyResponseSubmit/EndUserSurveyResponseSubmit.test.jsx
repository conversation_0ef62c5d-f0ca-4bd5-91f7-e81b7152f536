import React from 'react'

import { render, screen, fireEvent, waitFor } from '@redwoodjs/testing/web'

import EndUserSurveyResponseSubmit from './EndUserSurveyResponseSubmit'

const mockSurvey = {
  id: 1,
  surveyName: 'Test Survey',
  subheading: 'Test Subheading',
  questions: [
    {
      id: 1,
      type: 'short',
      title: 'Short Answer Question',
      required: true,
      questionConfig: {
        maxLength: 50,
      },
    },
    {
      id: 2,
      type: 'multiple',
      title: 'Multiple Choice Question',
      required: true,
      questionConfig: {
        choices: ['Option A', 'Option B', 'Option C'],
      },
    },
  ],
}

describe('EndUserSurveyResponseSubmit', () => {
  it('renders the survey title and subheading', () => {
    render(<EndUserSurveyResponseSubmit survey={mockSurvey} />)
    expect(screen.getByText('Test Survey')).toBeInTheDocument()
    expect(screen.getByText('Test Subheading')).toBeInTheDocument()
  })

  it('validates required questions', async () => {
    render(<EndUserSurveyResponseSubmit survey={mockSurvey} />)
    fireEvent.click(screen.getByText('Next'))
    await waitFor(() => {
      expect(screen.getByText('This field is required.')).toBeInTheDocument()
    })
  })

  it('handles short answer questions', async () => {
    render(<EndUserSurveyResponseSubmit survey={mockSurvey} />)
    const input = screen.getByLabelText('Short Answer Question')
    fireEvent.change(input, { target: { value: 'Test answer' } })
    fireEvent.click(screen.getByText('Next'))
    await waitFor(() => {
      expect(screen.getByText('Multiple Choice Question')).toBeInTheDocument()
    })
  })

  it('handles multiple choice questions', async () => {
    render(<EndUserSurveyResponseSubmit survey={mockSurvey} />)
    fireEvent.click(screen.getByText('Next')) // Move to multiple choice question
    const option = screen.getByLabelText('Option A')
    fireEvent.click(option)
    fireEvent.click(screen.getByText('Next'))
    // Add an assertion here for what should happen after the last question
  })
})
