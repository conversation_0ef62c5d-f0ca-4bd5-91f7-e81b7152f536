import { motion } from 'framer-motion'
import { 
  SparklesIcon, 
  BeakerIcon, 
  DocumentTextIcon,
  PuzzlePieceIcon,
  ArrowRightIcon,
  AdjustmentsHorizontalIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline'

const SurveyCreationMethodSelector = ({ onMethodSelect }) => {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  }

  return (
    <motion.div 
      className="space-y-6"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div 
        variants={item}
        className="p-6 rounded-lg border border-base-300 bg-base-100/50 backdrop-blur-lg hover:bg-base-100 transition-all duration-200"
        whileHover={{ scale: 1.02 }}
        onClick={() => onMethodSelect('scratch')}
      >
        <div className="flex items-start gap-4">
          <div className="p-3 rounded-lg bg-primary/10">
            <DocumentTextIcon className="h-6 w-6 text-primary" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">Create from Scratch</h3>
              <ArrowRightIcon className="h-4 w-4 text-base-content/50" />
            </div>
            <p className="mt-1 text-sm text-base-content/70">
              Start with a blank canvas and let our AI survey design agent help you create the perfect survey.
            </p>
            <div className="mt-4 flex items-center gap-4 text-sm text-base-content/70">
              <div className="flex items-center gap-1">
                <SparklesIcon className="h-4 w-4" />
                <span>AI-powered questions</span>
              </div>
              <div className="flex items-center gap-1">
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>Full customization</span>
              </div>
              <div className="flex items-center gap-1">
                <RocketLaunchIcon className="h-4 w-4" />
                <span>Start immediately</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div 
        variants={item}
        className="p-6 rounded-lg border border-base-300 bg-base-100/50 backdrop-blur-lg hover:bg-base-100 transition-all duration-200"
        whileHover={{ scale: 1.02 }}
        onClick={() => onMethodSelect('template')}
      >
        <div className="flex items-start gap-4">
          <div className="p-3 rounded-lg bg-primary/10">
            <PuzzlePieceIcon className="h-6 w-6 text-primary" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">Use a Template</h3>
              <ArrowRightIcon className="h-4 w-4 text-base-content/50" />
            </div>
            <p className="mt-1 text-sm text-base-content/70">
              Start with proven survey templates, customized by our AI to match your specific needs and context.
            </p>
            <div className="mt-4 flex items-center gap-4 text-sm text-base-content/70">
              <div className="flex items-center gap-1">
                <BeakerIcon className="h-4 w-4" />
                <span>Expert-designed</span>
              </div>
              <div className="flex items-center gap-1">
                <SparklesIcon className="h-4 w-4" />
                <span>AI customization</span>
              </div>
              <div className="flex items-center gap-1">
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>Quick setup</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default SurveyCreationMethodSelector 