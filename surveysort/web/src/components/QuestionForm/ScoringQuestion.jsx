import { useState, useEffect } from 'react';
import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/solid';
import { useFormContext, Controller } from 'react-hook-form';
import { Label } from '@redwoodjs/forms';

const ScoringQuestion = ({ question }) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext();

  const [scale, setScale] = useState(question?.questionConfig?.scale || 100);
  const maxScale = 100; // Fixed scale of 100

  useEffect(() => {
    setValue('questionConfig.scale', scale);
  }, [scale, setValue]);

  const handleScaleChange = (newScale) => {
    setScale(Math.max(0, Math.min(newScale, maxScale)));
  };


  return (
    <div className="space-y-2">
      <Label name="questionConfig.scale" className="label py-0.5">
        <span className="label-text text-xs">Scale</span>
      </Label>
      <div className="w-full flex flex-col items-center">
        <Controller
          control={control}
          name="questionConfig.scale"
          defaultValue={scale}
          render={({ field: { value, onChange } }) => (
            <div className="w-full">
              {/* Seek Bar */}
              <div className="relative flex items-center">
                <input
                  type="range"
                  min={0}
                  max={maxScale}
                  value={value}
                  onChange={(e) => {
                    const newValue = parseInt(e.target.value, 10);
                    setScale(newValue);
                    onChange(newValue);
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none focus:outline-none"
                  style={{
                    background: `linear-gradient(to right, var(--primary, #2563eb) ${(value / maxScale) * 100
                      }%, #e5e7eb ${(value / maxScale) * 100}%)`,
                  }}
                />
                {/* Icon */}
              </div>
              {/* Value */}
              <p className="text-center text-sm mt-2">Scale: {value}</p>
            </div>
          )}
        />
      </div>
      {errors?.questionConfig?.scale && (
        <span className="text-xs text-error">
          {errors.questionConfig.scale.message}
        </span>
      )}
    </div>
  );
};

export default ScoringQuestion;
