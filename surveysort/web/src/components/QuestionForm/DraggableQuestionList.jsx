import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { PlusIcon, DocumentDuplicateIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline'
import { toast } from '@redwoodjs/web/toast'

import QuestionSummary from './QuestionSummary'
import AddContentMenu from './AddContentMenu'
import PageBreak from './PageBreak'

// Helper functions for drag and drop
const getDestinationPage = (questions, destIndex) => {
  if (destIndex === 0) return 0
  return questions[Math.floor(destIndex / 2)].pageNumber
}

const getNewOrder = (questions, destIndex, destPageNumber) => {
  const pageQuestions = questions.filter(q => q.pageNumber === destPageNumber)
  return pageQuestions.length
}

// Add these helper functions at the top of the file
const resequencePageNumbers = (questions) => {
  const uniquePageNumbers = [...new Set(questions.map(q => q.pageNumber))].sort()
  const pageNumberMap = {}
  uniquePageNumbers.forEach((pageNumber, index) => {
    pageNumberMap[pageNumber] = index
  })

  return questions.map(q => ({
    ...q,
    pageNumber: pageNumberMap[q.pageNumber]
  }))
}

const getItemsWithPageBreaks = (questions) => {
  const items = []
  let currentPage = 0

  questions.forEach((question, index) => {
    if (question.pageNumber > currentPage) {
      // Add page break
      items.push({
        type: 'pageBreak',
        pageNumber: question.pageNumber,
        id: `page-break-${question.pageNumber}`,
      })
      currentPage = question.pageNumber
    }

    items.push({
      type: 'question',
      ...question,
    })
  })

  return items
}

const DraggableQuestionList = ({
  items,
  expandedQuestionId,
  onDragEnd,
  onQuestionSelect,
  onDeleteQuestion,
  onDuplicateQuestion,
  onMoveToPage,
  updateQuestion,
  onPreviewUpdate,
  onAddQuestion,
  onAddPageBreak,
  onDeletePageBreak,
  onOpenQuestionLibrary,
}) => {
  if (!items?.length) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-16rem)] text-center p-4">
        <div className="rounded-full bg-base-200 p-3">
          <ClipboardDocumentListIcon className="h-8 w-8 text-base-content/50" />
        </div>
        <h3 className="mt-4 text-sm font-medium text-base-content">No questions yet</h3>
        <p className="mt-1 text-sm text-base-content/70">
          Get started by adding your first question
        </p>
        <div className="mt-6 flex gap-2">
          <button
            onClick={onAddQuestion}
            className="btn btn-primary btn-sm gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            Add Question
          </button>
          <button
            onClick={onOpenQuestionLibrary}
            className="btn btn-outline btn-sm gap-2"
          >
            <DocumentDuplicateIcon className="h-4 w-4" />
            From Library
          </button>
        </div>
      </div>
    )
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="questions">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="overflow-hidden bg-base-100 shadow-sm ring-1 ring-base-300 rounded-xl w-full divide-y divide-base-300"
          >
            {items.map((item, index) => (
              <div key={item.id} className="group relative w-full">
                <Draggable
                  draggableId={item.id}
                  index={index}
                  isDragDisabled={expandedQuestionId === item.id}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={snapshot.isDragging ? 'opacity-50' : 'w-full'}
                    >
                      {item.type === 'pageBreak' ? (
                        <PageBreak
                          pageNumber={item.pageNumber}
                          onDelete={() => onDeletePageBreak(item.pageNumber)}
                          dragHandleProps={provided.dragHandleProps}
                        />
                      ) : (
                        <QuestionSummary
                          question={item}
                          index={index}
                          questionCount={items.length}
                          onEdit={() => onQuestionSelect(item.id)}
                          onDelete={() => onDeleteQuestion(item.id)}
                          onDuplicate={() => onDuplicateQuestion(item.id)}
                          onMovePage={() => onMoveToPage(item.id)}
                          onSave={updateQuestion}
                          dragHandleProps={expandedQuestionId === item.id ? null : provided.dragHandleProps}
              
                          isExpanded={expandedQuestionId === item.id}
                          onPreviewUpdate={onPreviewUpdate}
                          currentPage={item.pageNumber}
                        />
                      )}
                    </div>
                  )}
                </Draggable>
                <AddContentMenu
                  onAddQuestion={onAddQuestion}
                  onAddPageBreak={onAddPageBreak}
                  afterIndex={index}
                />
              </div>
            ))}
            {provided.placeholder}

            {/* Main add button with dropdown */}
            <div className="p-4 flex justify-end">
              <div className="dropdown dropdown-top dropdown-end">
                <label 
                  tabIndex={0} 
                  className={`btn btn-primary btn-sm`}
                >
                  <PlusIcon className="h-4 w-4" />
                </label>
                <ul 
                  tabIndex={0} 
                  className="dropdown-content z-[100] menu p-2 shadow-lg bg-base-100 rounded-box w-48"
                >
                  <li>
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        onAddQuestion()
                      }}
                      className="text-xs"
                    >
                      <PlusIcon className="h-4 w-4" />
                      New Question
                    </button>
                  </li>
                  <li>
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        onOpenQuestionLibrary()
                      }}
                      className="text-xs"
                    >
                      <DocumentDuplicateIcon className="h-4 w-4" />
                      Add from Library
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </Droppable>
    </DragDropContext>
  )
}

export default DraggableQuestionList
