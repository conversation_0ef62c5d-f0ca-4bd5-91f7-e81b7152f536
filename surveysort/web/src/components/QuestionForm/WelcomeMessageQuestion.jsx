import { useFormContext } from 'react-hook-form'
import { Label } from '@redwoodjs/forms'

const WelcomeMessageQuestion = () => {
  const { register } = useFormContext()

  return (
    <div className="space-y-4">
      <div>
        <Label name="questionConfig.welcomeMessage" className="label py-0.5">
          <span className="label-text text-xs">Welcome Message</span>
        </Label>
        <textarea
          {...register('questionConfig.welcomeMessage')}
          className="textarea textarea-bordered w-full"
          placeholder="Enter your welcome message..."
          rows={4}
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="label py-0.5">
            <span className="label-text text-xs">Require Disclaimer</span>
          </Label>
          <input
            type="checkbox"
            {...register('questionConfig.requireDisclaimer')}
            className="toggle toggle-xs toggle-primary"
          />
        </div>

        {/* Show disclaimer text field if required */}
        <div>
          <Label name="questionConfig.welcomeDisclaimer" className="label py-0.5">
            <span className="label-text text-xs">Disclaimer Text</span>
          </Label>
          <textarea
            {...register('questionConfig.welcomeDisclaimer')}
            className="textarea textarea-bordered w-full"
            placeholder="Enter disclaimer text..."
            rows={2}
          />
        </div>
      </div>
    </div>
  )
}

export default WelcomeMessageQuestion 