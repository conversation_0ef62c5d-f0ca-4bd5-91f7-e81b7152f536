import { useState, useEffect } from 'react'

import { PlusIcon, MinusIcon, ArrowsUpDownIcon } from '@heroicons/react/20/solid'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { useFieldArray, useFormContext } from 'react-hook-form'

import { Label } from '@redwoodjs/forms'

const MultipleChoiceQuestion = ({ index, question }) => {
  const {
    control,
    register,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useFormContext()

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: `questionConfig.choices`,
  })

  const watchChoices = watch(`questionConfig.choices`)
  const watchRandomize = watch('questionConfig.randomize')
  const [selectedRandomChoices, setSelectedRandomChoices] = useState([])

  useEffect(() => {
    if (question?.questionConfig) {
      if (question.questionConfig.randomizeChoices) {
        setSelectedRandomChoices(question.questionConfig.randomizeChoices)
      }
    }
  }, [question])

  useEffect(() => {
    if (watchRandomize) {
      if (selectedRandomChoices.length > 0) {
        setValue('questionConfig.randomizeChoices', selectedRandomChoices)
      }
    } else {
      if (selectedRandomChoices.length > 0) {
        setValue('questionConfig.randomizeChoices', [])
        setSelectedRandomChoices([])
      }
    }
  }, [watchRandomize, selectedRandomChoices, setValue])

  const handleDragEnd = (result) => {
    if (!result.destination) return
    move(result.source.index, result.destination.index)
  }

  const hasEmptyChoices = () => {
    if (!watchChoices) return false
    return watchChoices.some(choice =>
      choice && typeof choice === 'string' && choice.trim() === ''
    )
  }

  const handleTextAreaHeight = (e) => {
    e.target.style.height = 'inherit'
    e.target.style.height = `${e.target.scrollHeight}px`
  }

  return (
    <div className="space-y-4">
      <Label name={`questionConfig.choices`} className="label py-1">
        <span className="label-text text-xs">Choices</span>
      </Label>
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="choices">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {fields.map((field, choiceIndex) => (
                <Draggable
                  key={field.id}
                  draggableId={field.id}
                  index={choiceIndex}
                >
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className="mb-1 flex items-center space-x-1"
                    >
                      <button
                        type="button"
                        className="btn btn-ghost btn-xs px-0"
                        {...provided.dragHandleProps}
                      >
                        <ArrowsUpDownIcon className="h-4 w-4 text-base-content/50" />
                      </button>
                      <textarea
                        {...register(`questionConfig.choices.${choiceIndex}`, {
                          required: 'This field is required',
                        })}
                        defaultValue={field.value || ''}
                        className="textarea textarea-xs textarea-bordered w-full h-7 resize-none overflow-hidden"
                        placeholder={`Choice ${choiceIndex + 1}`}
                        onInput={handleTextAreaHeight}
                      />
                      <button
                        type="button"
                        onClick={() => remove(choiceIndex)}
                        className="btn btn-square btn-error btn-ghost btn-xs"
                      >
                        <MinusIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
      <button
        type="button"
        className="btn btn-primary btn-ghost btn-xs"
        onClick={() => append('')}
        disabled={hasEmptyChoices()}
      >
        <PlusIcon className="h-4 w-4" />
        <span className="text-xs">Add Choice</span>
      </button>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="label py-0.5">
            <span className="label-text text-xs">Answer Type</span>
          </Label>
          <select
            {...register('questionConfig.cellType')}
            className="select select-xs select-bordered w-48"
            defaultValue="radio"
          >
            <option value="radio">Single Choice</option>
            <option value="checkbox">Multiple Choice</option>
          </select>
        </div>

        <div className="flex items-center justify-between">
          <Label className="label py-0.5">
            <span className="label-text text-xs">Allow Write-in Answer</span>
          </Label>
          <input
            type="checkbox"
            {...register('questionConfig.allowWriteIn')}
            className="toggle toggle-xs toggle-primary"
          />
        </div>

        <div className="flex items-center justify-between">
          <Label className="label py-0.5">
            <span className="label-text text-xs">Randomize Choices</span>
          </Label>
          <input
            type="checkbox"
            {...register('questionConfig.randomize')}
            className="toggle toggle-xs toggle-primary"
          />
        </div>

        {watchRandomize && (
          <div className="space-y-2">
            <select
              className="select select-xs select-bordered w-full"
              onChange={(e) => {
                const choiceIndex = parseInt(e.target.value)
                if (isNaN(choiceIndex)) return
                const newChoices = [...selectedRandomChoices, choiceIndex]
                setSelectedRandomChoices(newChoices)
                setValue('questionConfig.randomizeChoices', newChoices)
              }}
              value=""
            >
              <option value="">Select choices to randomize...</option>
              {fields.map((field, idx) => {
                const choiceText = getValues(`questionConfig.choices.${idx}`)
                return (
                  <option
                    key={field.id}
                    value={idx}
                    disabled={selectedRandomChoices.includes(idx)}
                  >
                    {choiceText || `Choice ${idx + 1}`}
                  </option>
                )
              })}
            </select>

            {selectedRandomChoices.length > 0 && (
              <div className="space-y-1">
                {selectedRandomChoices.map((choiceIndex) => {
                  const choiceText = getValues(`questionConfig.choices.${choiceIndex}`)
                  return (
                    <div
                      key={choiceIndex}
                      className="flex items-center gap-1 text-xs p-1"
                    >
                      <span className="truncate flex-1">
                        {choiceText || `Choice ${choiceIndex + 1}`}
                      </span>
                      <button
                        type="button"
                        className="btn btn-ghost btn-xs"
                        onClick={() => {
                          const newChoices = selectedRandomChoices.filter(idx => idx !== choiceIndex)
                          setSelectedRandomChoices(newChoices)
                          setValue('questionConfig.randomizeChoices', newChoices)
                        }}
                      >
                        ×
                      </button>
                    </div>
                  )
                })}
              </div>
            )}

            <p className="text-xs text-base-content/70">
              {selectedRandomChoices.length === 0
                ? "All choices will be randomized if none are specifically selected"
                : "Only selected choices will be randomized"}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default MultipleChoiceQuestion
