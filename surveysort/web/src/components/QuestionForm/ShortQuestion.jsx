import { useFormContext } from 'react-hook-form'

import { Label, TextField } from '@redwoodjs/forms'

const ShortQuestion = ({ question }) => {
  const {
    register,
    formState: { errors },
  } = useFormContext()

  return (
    <div className="space-y-2">
      <Label name="questionConfig.maxLength" className="label py-1">
        <span className="label-text text-xs">Maximum Length of Answer</span>
      </Label>
      <TextField
        name="questionConfig.maxLength"
        defaultValue={question?.questionConfig?.maxLength}
        validation={{
          valueAsNumber: true,
          min: { value: 1, message: 'Maximum length must be at least 1' },
        }}
        {...register('questionConfig.maxLength', {
          valueAsNumber: true,
          min: { value: 1, message: 'Maximum length must be at least 1' },
        })}
        type="number"
        placeholder="Enter max length"
        className="input input-xs input-bordered w-full"
      />
      {errors?.questionConfig?.maxLength && (
        <span className="text-xs text-error">
          {errors.questionConfig.maxLength.message}
        </span>
      )}
    </div>
  )
}

export default ShortQuestion
