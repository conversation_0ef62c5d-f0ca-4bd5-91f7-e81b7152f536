import { useState } from 'react'
import { questionTypeIcons } from './constants'
import { ChatBubbleBottomCenterTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline'

// Add new icons for welcome and thank you messages
const extendedQuestionTypeIcons = {
  ...questionTypeIcons,
  WELCOME_MESSAGE: ChatBubbleBottomCenterTextIcon,
  THANK_YOU_MESSAGE: CheckCircleIcon,
}

const questionTypes = [
  {
    id: 'WELCOME_MESSAGE',
    name: 'Welcome Message',
    description: 'Add a welcome message with optional disclaimer',
    preview: 'Create a welcome message to greet participants. Add a disclaimer that requires acknowledgment before proceeding.',
  },
  {
    id: 'MULTIPLE_CHOICE',
    name: 'Multiple Choice',
    description: 'Let respondents choose from predefined options',
    preview: 'Create a list of options for respondents to choose from. Ideal for gathering specific preferences or opinions.',
  },
  {
    id: 'RATING',
    name: 'Rating',
    description: 'Collect feedback using star ratings',
    preview: 'Use star ratings to measure satisfaction, quality, or importance. Perfect for customer feedback and reviews.',
  },
  {
    id: 'SCALE',
    name: 'Scale',
    description: 'Numerical scale for measuring intensity',
    preview: 'Create a numerical scale for respondents to indicate level of agreement or intensity. Great for measuring attitudes or opinions.',
  },
  {
    id: 'SCORING',
    name: 'Scoring',
    description: 'Numerical Scoring for measuring intensity',
    preview: 'Create a numerical Score for respondents to indicate level of agreement or intensity. Great for measuring attitudes or opinions.',
  },
  {
    id: 'ESSAY',
    name: 'Essay',
    description: 'Collect detailed written responses',
    preview: 'Allow respondents to provide detailed written responses. Perfect for gathering qualitative feedback and explanations.',
  },
  {
    id: 'MATRIX',
    name: 'Matrix',
    description: 'Grid of questions with same response options',
    preview: 'Create a grid of questions that share the same set of responses. Efficient for related questions with consistent scales.',
  },
  {
    id: 'EMOTICON',
    name: 'Emoticon Scale',
    description: 'Visual feedback using emoticons',
    preview: 'Use emoticons for intuitive feedback collection. Great for measuring satisfaction or emotional responses.',
  },
  {
    id: 'ACCEPT_DENY',
    name: 'Accept/Deny',
    description: 'Simple binary choice questions',
    preview: 'Present a straightforward accept or deny choice. Perfect for terms acceptance or simple yes/no decisions.',
  },
  {
    id: 'LIKE_DISLIKE',
    name: 'Like/Dislike',
    description: 'Quick binary feedback',
    preview: 'Collect quick thumbs up/down feedback. Ideal for simple preference gathering or quick reactions.',
  },
  {
    id: 'DATE_TIME',
    name: 'Date & Time',
    description: 'Collect temporal information',
    preview: 'Gather date and time information. Useful for scheduling, availability, or temporal data collection.',
  },
  {
    id: 'CONTACT_DETAILS',
    name: 'Contact Details',
    description: 'Collect respondent information',
    preview: 'Gather contact information from respondents. Includes fields for name, email, phone, and other contact details.',
  },
  {
    id: 'THANK_YOU_MESSAGE',
    name: 'Thank You Message',
    description: 'Add a thank you message at the end',
    preview: 'Create a thank you message to show appreciation to participants after completing the survey.',
  },
]

const QuestionTypeSelector = ({ onSelect, selectedType }) => {
  const [hoveredType, setHoveredType] = useState(null)

  // Group question types by category
  const groupedTypes = {
    'Welcome & Thank You': questionTypes.filter(t =>
      ['WELCOME_MESSAGE', 'THANK_YOU_MESSAGE'].includes(t.id)
    ),
    'Basic Types': questionTypes.filter(t =>
      ['MULTIPLE_CHOICE', 'ESSAY', 'SHORT', 'RATING', 'SCALE', 'SCORING'].includes(t.id)
    ),
    'Advanced Types': questionTypes.filter(t =>
      ['MATRIX', 'EMOTICON', 'ACCEPT_DENY', 'LIKE_DISLIKE', 'DATE_TIME', 'CONTACT_DETAILS'].includes(t.id)
    ),
  }

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {Object.entries(groupedTypes).map(([group, groupTypes]) => (
        <div key={group} className="space-y-4">
          <h3 className="text-sm font-medium text-base-content/70">{group}</h3>
          <div className="space-y-2">
            {groupTypes.map((type) => {
              const Icon = extendedQuestionTypeIcons[type.id]
              return (
                <button
                  key={type.id}
                  type="button"
                  onClick={() => onSelect(type.id)}
                  className={`
                    relative flex w-full items-start space-x-3 rounded-lg p-3 hover:bg-base-200
                    ${selectedType === type.id ? 'bg-base-200 ring-1 ring-primary' : ''}
                  `}
                >
                  {Icon && (
                    <div className={`
                      flex-shrink-0
                      ${selectedType === type.id ? 'text-primary' : 'text-base-content/70'}
                    `}>
                      <Icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                  )}
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium">{type.name}</div>
                    <p className="text-xs text-base-content/70">
                      {type.description}
                    </p>
                  </div>
                </button>
              )
            })}
          </div>
        </div>
      ))}
    </div>
  )
}

export default QuestionTypeSelector
