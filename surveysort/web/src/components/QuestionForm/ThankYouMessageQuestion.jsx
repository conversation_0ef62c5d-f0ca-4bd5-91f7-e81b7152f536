import { useFormContext } from 'react-hook-form'
import { Label } from '@redwoodjs/forms'

const ThankYouMessageQuestion = () => {
  const { register } = useFormContext()

  return (
    <div className="space-y-4">
      <div>
        <Label name="questionConfig.thankYouMessage" className="label py-0.5">
          <span className="label-text text-xs">Thank You Message</span>
        </Label>
        <textarea
          {...register('questionConfig.thankYouMessage')}
          className="textarea textarea-bordered w-full"
          placeholder="Enter your thank you message..."
          rows={4}
        />
      </div>
    </div>
  )
}

export default ThankYouMessageQuestion 