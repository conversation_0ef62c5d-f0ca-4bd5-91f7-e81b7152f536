import { useState, useEffect } from 'react'
import { ChromePicker } from 'react-color'
import { 
  PaintBrushIcon, 
  DocumentTextIcon,
  CursorArrowRaysIcon, 
  ArrowPathIcon,
  DevicePhoneMobileIcon, 
  ComputerDesktopIcon,
  ChatBubbleBottomCenterTextIcon,
  SwatchIcon
} from '@heroicons/react/24/outline'

// Predefined themes
const PRESET_THEMES = [
  {
    name: 'Ocean Breeze',
    config: {
      backgroundColor: '#f0f9ff',
      questionTextColor: '#1e3a8a',
      answerTextColor: '#3b82f6',
      buttonColor: '#2563eb',
      progressBarColor: '#60a5fa'
    }
  },
  {
    name: 'Forest Calm',
    config: {
      backgroundColor: '#f0fdf4',
      questionTextColor: '#166534',
      answerTextColor: '#16a34a',
      buttonColor: '#15803d',
      progressBarColor: '#4ade80'
    }
  },
  {
    name: '<PERSON> Warmth',
    config: {
      backgroundColor: '#fff7ed',
      questionTextColor: '#9a3412',
      answerTextColor: '#ea580c',
      buttonColor: '#c2410c',
      progressBarColor: '#fb923c'
    }
  },
  {
    name: 'Purple Rain',
    config: {
      backgroundColor: '#faf5ff',
      questionTextColor: '#6b21a8',
      answerTextColor: '#9333ea',
      buttonColor: '#7e22ce',
      progressBarColor: '#a855f7'
    }
  },
  {
    name: 'Modern Mono',
    config: {
      backgroundColor: '#fafafa',
      questionTextColor: '#18181b',
      answerTextColor: '#3f3f46',
      buttonColor: '#27272a',
      progressBarColor: '#52525b'
    }
  },
  {
    name: 'Midnight Blue',
    config: {
      backgroundColor: '#0f172a',
      questionTextColor: '#e2e8f0',
      answerTextColor: '#64748b',
      buttonColor: '#2563eb',
      progressBarColor: '#3b82f6'
    }
  },
  {
    name: 'Rose Garden',
    config: {
      backgroundColor: '#fff1f2',
      questionTextColor: '#9f1239',
      answerTextColor: '#e11d48',
      buttonColor: '#be123c',
      progressBarColor: '#fb7185'
    }
  },
  {
    name: 'Autumn Gold',
    config: {
      backgroundColor: '#fffbeb',
      questionTextColor: '#92400e',
      answerTextColor: '#d97706',
      buttonColor: '#b45309',
      progressBarColor: '#fbbf24'
    }
  }
]

const ThemeSettingsDoc = ({ 
  themeConfig, 
  onColorChange, 
  onApply,
  previewDevice,
  setPreviewDevice,
  initialTheme 
}) => {
  const [showColorPicker, setShowColorPicker] = useState(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [showThemeList, setShowThemeList] = useState(false)

  // Check if there are unsaved changes by comparing with initialTheme
  useEffect(() => {
    const unsavedChanges = Object.entries(themeConfig).some(
      ([key, value]) => value !== initialTheme[key]
    )
    setHasUnsavedChanges(unsavedChanges)
  }, [themeConfig, initialTheme])

  const handleApply = () => {
    onApply()
    setHasUnsavedChanges(false)
  }

  const handleThemeSelect = (theme) => {
    Object.entries(theme.config).forEach(([key, value]) => {
      onColorChange({ hex: value }, key)
    })
    setShowThemeList(false)
  }

  // Close color picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showColorPicker && !event.target.closest('.color-picker-container')) {
        setShowColorPicker(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showColorPicker])

  return (
    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-base-200 px-4 py-2 flex items-center gap-3">
      {/* Device Toggle */}
      <div className="flex items-center gap-2">
        <button
          className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all ${
            previewDevice === 'mobile' 
              ? 'bg-base-200 text-primary ring-1 ring-primary/20' 
              : 'text-base-content/70 hover:bg-base-100'
          }`}
          onClick={() => setPreviewDevice('mobile')}
        >
          <DevicePhoneMobileIcon className="h-4 w-4" />
        </button>
        <button
          className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all ${
            previewDevice === 'desktop' 
              ? 'bg-base-200 text-primary ring-1 ring-primary/20' 
              : 'text-base-content/70 hover:bg-base-100'
          }`}
          onClick={() => setPreviewDevice('desktop')}
        >
          <ComputerDesktopIcon className="h-4 w-4" />
        </button>
      </div>

      {/* Separator */}
      <div className="w-px h-8 bg-base-200"></div>

      {/* Theme Selector */}
      <div className="relative">
        <button
          onClick={() => setShowThemeList(!showThemeList)}
          className="w-8 h-8 rounded-lg border shadow-sm flex items-center justify-center hover:ring-2 hover:ring-primary/20 transition-all"
        >
          <SwatchIcon className="h-4 w-4 text-[--primary]" />
        </button>

        {showThemeList && (
          <div className="absolute bottom-full left-0 mb-2 w-64 max-h-80 overflow-y-auto rounded-lg bg-white shadow-lg ring-1 ring-black/5 dark:bg-zinc-900 dark:ring-white/10">
            <div className="p-1">
              {PRESET_THEMES.map((theme, index) => (
                <button
                  key={theme.name}
                  onClick={() => handleThemeSelect(theme)}
                  className={`
                    w-full flex items-center gap-3 px-3 py-2 rounded-md text-left
                    transition-all duration-200 ease-in-out
                    hover:bg-zinc-950/[2.5%] dark:hover:bg-white/[2.5%]
                    focus:outline-none focus:bg-zinc-950/[2.5%] dark:focus:bg-white/[2.5%]
                    ${index > 0 ? 'mt-0.5' : ''}
                  `}
                >
                  <div className="flex -space-x-1">
                    {Object.values(theme.config).map((color, i) => (
                      <div
                        key={i}
                        className="w-5 h-5 rounded-full border border-white shadow-sm ring-1 ring-black/5"
                        style={{ 
                          backgroundColor: color,
                          zIndex: 5 - i,
                        }}
                      />
                    ))}
                  </div>
                  <span className="text-sm font-medium text-zinc-950 dark:text-white">
                    {theme.name}
                  </span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Color Controls */}
      <div className="flex items-center gap-3">
        {[
          { key: 'backgroundColor', icon: PaintBrushIcon, tooltip: 'Page Background' },
          { key: 'questionTextColor', icon: DocumentTextIcon, tooltip: 'Question Text' },
          { key: 'answerTextColor', icon: ChatBubbleBottomCenterTextIcon, tooltip: 'Answer Text' },
          { key: 'buttonColor', icon: CursorArrowRaysIcon, tooltip: 'Buttons' },
          { key: 'progressBarColor', icon: ArrowPathIcon, tooltip: 'Progress Bar' }
        ].map(({ key, icon: Icon, tooltip }) => (
          <div key={key} className="relative group color-picker-container">
            <button
              className="w-8 h-8 rounded-lg border shadow-sm flex items-center justify-center hover:ring-2 hover:ring-primary/20 transition-all"
              style={{ 
                backgroundColor: 'white',
                borderColor: themeConfig[key],
                color: themeConfig[key]
              }}
              onClick={() => setShowColorPicker(showColorPicker === key ? null : key)}
            >
              <Icon className="h-4 w-4" />
            </button>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 pointer-events-none">
              <div className="bg-gray-900 text-white text-[10px] px-2 py-0.5 rounded-md whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                {tooltip}
              </div>
            </div>

            {/* Color Picker Popover */}
            {showColorPicker === key && (
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 z-50">
                <div className="relative">
                  <ChromePicker
                    color={themeConfig[key]}
                    onChange={(color) => onColorChange(color, key)}
                  />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Apply Button - Animated */}
      <div className="overflow-hidden">
        <div 
          className={`
            transition-all duration-300 ease-in-out
            ${hasUnsavedChanges 
              ? 'opacity-100 max-w-[100px] ml-3' 
              : 'opacity-0 max-w-0 ml-0'
            }
          `}
        >
          <button
            className="btn btn-primary btn-sm rounded-lg whitespace-nowrap"
            onClick={handleApply}
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  )
}

export default ThemeSettingsDoc