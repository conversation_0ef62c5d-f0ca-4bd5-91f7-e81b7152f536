import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'

const EMOTICON_SCALES = {
  satisfaction: [
    { value: 5, label: '😄', text: 'Highly Satisfied' },
    { value: 4, label: '🙂', text: 'Satisfied' },
    { value: 3, label: '😐', text: 'Neutral' },
    { value: 2, label: '🙁', text: 'Dissatisfied' },
    { value: 1, label: '😞', text: 'Highly Dissatisfied' },
  ],
  // Add more scales as needed
}

const EmoticonQuestion = ({ question }) => {
  const { setValue } = useFormContext()

  useEffect(() => {
    // Convert the rich emoticon objects to strings for storage
    const choiceStrings = EMOTICON_SCALES.satisfaction.map(
      choice => `${choice.value}|${choice.label}|${choice.text}`
    )
    setValue('questionConfig.choices', choiceStrings)
  }, [setValue])

  return (
    <div className="px-4">
      <div className="flex justify-between">
        {EMOTICON_SCALES.satisfaction.map((choice) => (
          <div 
            key={choice.value} 
            className="flex flex-col items-center tooltip" 
            data-tip={choice.text}
          >
            <span className="text-xl">{choice.label}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default EmoticonQuestion 