import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, FormProvider } from 'react-hook-form'

import { Form, Submit, FormError, Label } from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { ArrowLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid'
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import { Dialog, DialogPanel, DialogBackdrop } from '@headlessui/react'

import QuestionFormFields from './QuestionFormFields'
import RuleActions from './RuleActions'
import QuestionTypeSelector from './QuestionTypeSelector'
import { questionTypeIcons } from './constants'
import { ChatBubbleBottomCenterTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline'

const CREATE_QUESTION_MUTATION = gql`
  mutation CreateQuestionMutation($input: CreateQuestionInput!) {
    createQuestion(input: $input) {
      id
      title
      type
      required
      surveyId
      questionConfig {
        choices
        minLength
        maxLength
        rules {
          operation
          value
        }
        condition
        actions {
          type
          questionNumber
        }
        rating
        scale
        allowMultiple
        allowWriteIn
        includeTime
        requireName
        requireEmail
        requirePhone
        requireCompany
        requireAddress
        requireWebsite
      }
    }
  }
`

const Loading = () => (
  <div className="mx-auto">
    <div className="skeleton mb-4 h-8 w-1/2"></div>
    <div className="skeleton mb-2 h-6 w-full"></div>
    <div className="skeleton mb-2 h-6 w-full"></div>
    <div className="skeleton mb-2 h-6 w-full"></div>
    <div className="skeleton h-10 w-1/4"></div>
  </div>
)

const questionTypes = [
  {
    id: 'WELCOME_MESSAGE',
    name: 'Welcome Message',
    description: 'Add a welcome message with optional disclaimer',
    preview: 'Create a welcome message to greet participants. Add a disclaimer that requires acknowledgment before proceeding.',
  },
  {
    id: 'MULTIPLE_CHOICE',
    name: 'Multiple Choice',
    description: 'Let respondents choose from predefined options',
    preview: 'Create a list of options for respondents to choose from. Ideal for gathering specific preferences or opinions.',
  },
  {
    id: 'RATING',
    name: 'Rating',
    description: 'Collect feedback using star ratings',
    preview: 'Use star ratings to measure satisfaction, quality, or importance. Perfect for customer feedback and reviews.',
  },
  {
    id: 'SCALE',
    name: 'Scale',
    description: 'Numerical scale for measuring intensity',
    preview: 'Create a numerical scale for respondents to indicate level of agreement or intensity. Great for measuring attitudes or opinions.',
  },
  {
    id: 'ESSAY',
    name: 'Essay',
    description: 'Collect detailed written responses',
    preview: 'Allow respondents to provide detailed written responses. Perfect for gathering qualitative feedback and explanations.',
  },
  {
    id: 'MATRIX',
    name: 'Matrix',
    description: 'Grid of questions with same response options',
    preview: 'Create a grid of questions that share the same set of responses. Efficient for related questions with consistent scales.',
  },
  {
    id: 'EMOTICON',
    name: 'Emoticon Scale',
    description: 'Visual feedback using emoticons',
    preview: 'Use emoticons for intuitive feedback collection. Great for measuring satisfaction or emotional responses.',
  },
  {
    id: 'ACCEPT_DENY',
    name: 'Accept/Deny',
    description: 'Simple binary choice questions',
    preview: 'Present a straightforward accept or deny choice. Perfect for terms acceptance or simple yes/no decisions.',
  },
  {
    id: 'LIKE_DISLIKE',
    name: 'Like/Dislike',
    description: 'Quick binary feedback',
    preview: 'Collect quick thumbs up/down feedback. Ideal for simple preference gathering or quick reactions.',
  },
  {
    id: 'DATE_TIME',
    name: 'Date & Time',
    description: 'Collect temporal information',
    preview: 'Gather date and time information. Useful for scheduling, availability, or temporal data collection.',
  },
  {
    id: 'CONTACT_DETAILS',
    name: 'Contact Details',
    description: 'Collect respondent information',
    preview: 'Gather contact information from respondents. Includes fields for name, email, phone, and other contact details.',
  },
  {
    id: 'THANK_YOU_MESSAGE',
    name: 'Thank You Message',
    description: 'Add a thank you message at the end',
    preview: 'Create a thank you message to show appreciation to participants after completing the survey.',
  },
]

const NewQuestionForm = ({ surveyId, onSuccess, isOpen, onClose }) => {
  const [questionType, setQuestionType] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  
  const methods = useForm({
    defaultValues: {
      type: '',
      title: '',
      required: false,
      questionConfig: {},
    },
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = methods

  const [createQuestion, { loading, error }] = useMutation(
    CREATE_QUESTION_MUTATION,
    {
      onCompleted: () => {
        reset()
        setQuestionType('')
        setSearchQuery('')
        onSuccess()
      },
      onError: (error) => {
        toast.error(`Error creating question: ${error.message}`)
      },
    }
  )

  const handleClose = () => {
    reset()
    setQuestionType('')
    setSearchQuery('')
    onClose()
  }

  // Group question types
  const questionGroups = {
    'Welcome & Thank You': questionTypes.filter(t => 
      ['WELCOME_MESSAGE', 'THANK_YOU_MESSAGE'].includes(t.id)
    ),
    'Basic Types': questionTypes.filter(t =>
      ['MULTIPLE_CHOICE', 'ESSAY', 'RATING', 'SCALE'].includes(t.id)
    ),
    'Advanced Types': questionTypes.filter(t =>
      ['MATRIX', 'EMOTICON', 'ACCEPT_DENY', 'LIKE_DISLIKE', 'DATE_TIME', 'CONTACT_DETAILS'].includes(t.id)
    )
  }

  // Filter questions based on search
  const filteredGroups = searchQuery === '' 
    ? questionGroups
    : Object.fromEntries(
        Object.entries(questionGroups).map(([group, types]) => [
          group,
          types.filter(type => 
            type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            type.description.toLowerCase().includes(searchQuery.toLowerCase())
          )
        ]).filter(([_, types]) => types.length > 0)
      )

  if (loading) return <Loading />

  const onSubmit = async (data) => {
    console.log('Original form data:', data)

    const input = {
      title: data.title,
      type: data.type,
      required: data.required,
      surveyId,
      questionConfig: {
        choices: data.questionConfig?.choices || [],
        ...(data.questionConfig?.minLength && {
          minLength: parseInt(data.questionConfig.minLength)
        }),
        ...(data.questionConfig?.maxLength && {
          maxLength: parseInt(data.questionConfig.maxLength)
        }),
        rules: data.questionConfig?.rules || [],
        condition: data.questionConfig?.condition || 'all',
        actions: data.questionConfig?.actions || [],
      }
    }

    if (data.questionConfig.scale) {
      input.questionConfig.scale = data.questionConfig.scale;
    }

    if (data.questionConfig.includeTime) {
      input.questionConfig.includeTime = data.questionConfig.includeTime;
    }

    // Remove any undefined, null, or empty string values from questionConfig
    Object.keys(input.questionConfig).forEach(key => {
      if (input.questionConfig[key] === undefined ||
        input.questionConfig[key] === null ||
        input.questionConfig[key] === '') {
        delete input.questionConfig[key]
      }
    })

    console.log('Formatted input:', input)

    try {
      const result = await createQuestion({ variables: { input } })
      console.log('Create question result:', result)
      if (result.data && result.data.createQuestion) {
        toast.success('Question created successfully')
        onSuccess(result.data.createQuestion)
      }
    } catch (error) {
      console.error('Error creating question:', error)
      toast.error(`Error creating question: ${error.message}`)
    }
  }

  const handleFieldChange = (e) => {
    // This function will handle changes for all fields
    const { name, value, type, checked } = e.target
    console.log('Field change:', name, value, type, checked)
    const fieldValue = type === 'checkbox' ? checked : value
    setValue(name, fieldValue)
  }

  const getIconColor = (type) => {
    const colors = {
      WELCOME_MESSAGE: 'bg-blue-500',
      MULTIPLE_CHOICE: 'bg-purple-500',
      RATING: 'bg-yellow-500',
      SCALE: 'bg-green-500',
      ESSAY: 'bg-pink-500',
      MATRIX: 'bg-indigo-500',
      EMOTICON: 'bg-orange-500',
      ACCEPT_DENY: 'bg-red-500',
      LIKE_DISLIKE: 'bg-teal-500',
      DATE_TIME: 'bg-cyan-500',
      CONTACT_DETAILS: 'bg-violet-500',
      THANK_YOU_MESSAGE: 'bg-emerald-500',
    }
    return colors[type] || 'bg-gray-500'
  }

  // Add extended icons object
  const extendedQuestionTypeIcons = {
    ...questionTypeIcons,
    WELCOME_MESSAGE: ChatBubbleBottomCenterTextIcon,
    THANK_YOU_MESSAGE: CheckCircleIcon,
  }

  return (
    <Dialog
      className="relative z-[99999]"
      open={isOpen}
      onClose={handleClose}
    >
      <DialogBackdrop
        className="fixed inset-0 bg-base-300/70 backdrop-blur-lg transition-opacity"
      />

      <div className="fixed inset-0 z-[99999] w-screen overflow-y-auto p-4 sm:p-6 md:p-20">
        <DialogPanel
          as={motion.div}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="mx-auto max-w-xl transform divide-y divide-base-200 overflow-hidden rounded-xl bg-base-100 shadow-xl ring-1 ring-base-300/5"
        >
          <FormProvider {...methods}>
            <Form onSubmit={handleSubmit(onSubmit)} className="form-control w-full">
              <FormError error={error} />

              <AnimatePresence mode="wait">
                {!questionType ? (
                  <motion.div
                    key="question-types"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2 }}
                    className="w-full"
                  >
                    {/* Search Input */}
                    <div className="relative border-b border-base-200">
                      <MagnifyingGlassIcon
                        className="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-base-content/50"
                        aria-hidden="true"
                      />
                      <input
                        type="text"
                        className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-base-content placeholder:text-base-content/50 focus:ring-0 sm:text-sm"
                        placeholder="Search question types..."
                        onChange={(e) => setSearchQuery(e.target.value)}
                        autoFocus
                      />
                    </div>

                    <div className="overflow-y-auto max-h-[calc(100vh-16rem)]">
                      <div className="divide-y divide-base-200">
                        {Object.entries(filteredGroups).map(([group, types]) => (
                          types.length > 0 && (
                            <div key={group} className="p-4">
                              <h3 className="text-sm font-medium text-base-content/70 mb-3">{group}</h3>
                              <div className="space-y-2">
                                {types.map((type) => {
                                  const Icon = extendedQuestionTypeIcons[type.id]
                                  return (
                                    <div
                                      key={type.id}
                                      onClick={() => {
                                        setQuestionType(type.id)
                                        setValue('type', type.id)
                                      }}
                                      className="group flex cursor-pointer select-none rounded-lg p-3 hover:bg-base-200"
                                    >
                                      <div className={`flex h-10 w-10 flex-none items-center justify-center rounded-lg ${getIconColor(type.id)}`}>
                                        {Icon && <Icon className="h-5 w-5 text-white" />}
                                      </div>
                                      <div className="ml-4 flex-auto">
                                        <p className="text-sm font-medium text-base-content group-hover:text-primary">
                                          {type.name}
                                        </p>
                                        <p className="text-sm text-base-content/70">
                                          {type.description}
                                        </p>
                                      </div>
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                          )
                        ))}

                        {/* No results state */}
                        {searchQuery !== '' && Object.values(filteredGroups).every(group => group.length === 0) && (
                          <div className="px-6 py-14 text-center text-sm">
                            <ExclamationCircleIcon className="mx-auto h-6 w-6 text-base-content/50" />
                            <p className="mt-4 font-semibold text-base-content">No results found</p>
                            <p className="mt-2 text-base-content/70">
                              No question types found for this search term. Please try again.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="question-form"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                    className="p-4"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <button
                          type="button"
                          onClick={() => {
                            setQuestionType('')
                            setValue('type', '')
                          }}
                          className="btn btn-ghost btn-xs px-1"
                        >
                          <ArrowLeftIcon className="h-4 w-4" />
                        </button>
                        <h3 className="text-sm font-medium">
                          New {questionType.toLowerCase().replace('_', ' ')} question
                        </h3>
                      </div>
                    </div>

                    <QuestionFormFields
                      register={register}
                      errors={errors}
                      watch={watch}
                      setValue={setValue}
                      questionType={questionType}
                      onChange={handleFieldChange}
                    />

                    <div className="mt-4 text-right">
                      <div>
                        <Submit
                          className={`btn btn-primary btn-sm ${loading ? 'btn-disabled' : ''
                            }`}
                        >
                          Add Question
                        </Submit>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </Form>
          </FormProvider>
        </DialogPanel>
      </div>
    </Dialog>
  )
}

export default NewQuestionForm
