import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

import {
  UPDATE_QUESTIONS_MUTATION,
  REORDER_QUESTIONS_MUTATION,
  UPDATE_QUESTION_MUTATION,
  DELETE_QUESTION_MUTATION,
  CREATE_QUESTION_MUTATION,
} from './QuestionFormQueries'

export const useQuestionMutations = ({ surveyId, refetch }) => {
  const [reorderQuestions] = useMutation(REORDER_QUESTIONS_MUTATION, {
    onCompleted: () => {
      // Don't show toast for reordering to avoid spam
      // toast.success('Questions reordered successfully')
    },
    onError: (error) => {
      toast.error(`Error reordering questions: ${error.message}`)
    },
  })

  const [updateQuestion] = useMutation(UPDATE_QUESTION_MUTATION, {
    onCompleted: () => {
      toast.success('Question updated successfully')
      refetch()
    },
    onError: (error) => {
      toast.error(`Error updating question: ${error.message}`)
    },
  })

  const [deleteQuestion] = useMutation(DELETE_QUESTION_MUTATION, {
    onCompleted: () => {
      toast.success('Question deleted successfully')
      refetch()
    },
    onError: (error) => {
      toast.error(`Error deleting question: ${error.message}`)
    },
  })

  const [createQuestion] = useMutation(CREATE_QUESTION_MUTATION, {
    onCompleted: () => {
      toast.success('Question duplicated successfully')
      refetch()
    },
    onError: (error) => {
      toast.error(`Error duplicating question: ${error.message}`)
    }
  })

  const recalculateQuestionOrder = (questions) => {
    let currentPage = 0
    let orderInPage = 0

    return questions.map(question => {
      if (question.pageNumber > currentPage) {
        currentPage = question.pageNumber
        orderInPage = 0
      }

      const updatedQuestion = {
        id: question.id,
        pageNumber: question.pageNumber,
        order: orderInPage
      }

      orderInPage++
      return updatedQuestion
    })
  }

  const handleDeleteQuestion = async (questionToDelete, questions) => {
    if (!questionToDelete) return

    try {
      const result = await deleteQuestion({
        variables: { id: questionToDelete },
      })

      if (result.data?.deleteQuestion) {
        const remainingQuestions = questions.filter(
          (field) => field.id !== questionToDelete
        )

        if (remainingQuestions.length > 0) {
          const reorderedQuestions = recalculateQuestionOrder(remainingQuestions)

          try {
            await reorderQuestions({
              variables: {
                input: {
                  surveyId,
                  questions: reorderedQuestions
                }
              }
            })
          } catch (reorderError) {
            console.error('Error reordering questions:', reorderError)
            toast.error(`Error reordering questions: ${reorderError.message}`)
          }
        }
      }
    } catch (error) {
      console.error('Error deleting question:', error)
      toast.error(`Error deleting question: ${error.message}`)
    }
  }

  return {
    reorderQuestions,
    updateQuestion,
    deleteQuestion,
    createQuestion,
    handleDeleteQuestion,
    recalculateQuestionOrder,
  }
}
