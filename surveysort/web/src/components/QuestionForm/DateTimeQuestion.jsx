import { useFormContext } from 'react-hook-form'
import { Label } from '@redwoodjs/forms'

const DateTimeQuestion = ({ question }) => {
  const { register } = useFormContext()

  return (
    <div className="flex w-full items-center justify-between  pt-2">
      <Label name="questionConfig.includeTime" className="label py-0">
        <span className="label-text text-xs">Include Time</span>
      </Label>
      <input
        type="checkbox"
        {...register('questionConfig.includeTime')}
        className="toggle toggle-xs"
      />
    </div>
  )
}

export default DateTimeQuestion 