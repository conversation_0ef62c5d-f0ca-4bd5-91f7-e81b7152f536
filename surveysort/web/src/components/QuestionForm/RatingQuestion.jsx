import { useState, useEffect } from 'react'

import { StarIcon as StarIconOutline } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid, PlusIcon, MinusIcon } from '@heroicons/react/24/solid'
import { useFormContext, Controller } from 'react-hook-form'

import { Label } from '@redwoodjs/forms'

const RatingQuestion = ({ question }) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext()
  const [rating, setRating] = useState(question?.questionConfig?.rating || 5)

  useEffect(() => {
    setValue('questionConfig.rating', rating)
  }, [rating, setValue])

  const handleAddStar = () => {
    setRating((prev) => Math.min(prev + 1, 10))
  }

  const handleRemoveStar = () => {
    setRating((prev) => Math.max(prev - 1, 1))
  }

  return (
    <div className="space-y-2">
      <Label name="questionConfig.rating" className="label py-0.5">
        <span className="label-text text-xs">Rating</span>
      </Label>
      <div className="w-full flex justify-center">
        <Controller
          control={control}
          name="questionConfig.rating"
          defaultValue={rating}
          render={({ field: { onChange, value } }) => (
            <div className="flex items-center space-x-1">
              {[...Array(rating)].map((_, index) => (
                <button
                  key={index}
                  type="button"
                  className="focus:outline-none"
                >
                  {index < value ? (
                    <StarIconSolid className="h-4 w-4 text-yellow-400" />
                  ) : (
                    <StarIconOutline className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              ))}
            </div>
          )}
        />
      </div>
      <div className="flex justify-center space-x-1">
        <button
          type="button"
          className="btn btn-primary btn-ghost btn-xs"
          onClick={handleAddStar}
        >
          <PlusIcon className="h-4 w-4" />
          <span className="text-xs">Add Star</span>
        </button>
        <button
          type="button"
          className="btn btn-error btn-ghost btn-xs"
          onClick={handleRemoveStar}
        >
          <MinusIcon className="h-4 w-4" />
          <span className="text-xs">Remove Star</span>
        </button>
      </div>
      {errors?.questionConfig?.rating && (
        <span className="text-xs text-error">
          {errors.questionConfig.rating.message}
        </span>
      )}
    </div>
  )
}

export default RatingQuestion
