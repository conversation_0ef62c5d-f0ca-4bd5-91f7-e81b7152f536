import { useState, useEffect } from 'react'
import { 
  PlusIcon, 
  MinusIcon 
} from '@heroicons/react/24/solid'
import { useFormContext, Controller } from 'react-hook-form'
import { Label } from '@redwoodjs/forms'

const ScaleQuestion = ({ question }) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext()
  const [scale, setScale] = useState(question?.questionConfig?.scale || 5)

  useEffect(() => {
    setValue('questionConfig.scale', scale)
  }, [scale, setValue])

  const handleAddScale = () => {
    setScale((prev) => Math.min(prev + 1, 10))
  }

  const handleRemoveScale = () => {
    setScale((prev) => Math.max(prev - 1, 2))
  }

  return (
    <div className="space-y-2">
      <Label name="questionConfig.scale" className="label py-0.5">
        <span className="label-text text-xs">Scale</span>
      </Label>
      <div className="w-full flex justify-center">
        <Controller
          control={control}
          name="questionConfig.scale"
          defaultValue={scale}
          render={({ field: { value } }) => (
            <div className="flex items-center space-x-1">
              {[...Array(scale)].map((_, index) => (
                <div
                  key={index}
                  className="h-8 w-8 rounded-lg bg-base-200 flex items-center justify-center text-xs font-medium"
                >
                  {index + 1}
                </div>
              ))}
            </div>
          )}
        />
      </div>
      <div className="flex justify-center space-x-1">
        <button
          type="button"
          className="btn btn-primary btn-ghost btn-xs"
          onClick={handleAddScale}
        >
          <PlusIcon className="h-4 w-4" />
          <span className="text-xs">Add Scale Point</span>
        </button>
        <button
          type="button"
          className="btn btn-error btn-ghost btn-xs"
          onClick={handleRemoveScale}
        >
          <MinusIcon className="h-4 w-4" />
          <span className="text-xs">Remove Scale Point</span>
        </button>
      </div>
      {errors?.questionConfig?.scale && (
        <span className="text-xs text-error">
          {errors.questionConfig.scale.message}
        </span>
      )}
    </div>
  )
}

export default ScaleQuestion
