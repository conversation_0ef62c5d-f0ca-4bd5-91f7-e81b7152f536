import { TrashIcon, ArrowsUpDownIcon } from '@heroicons/react/24/outline'

const PageBreak = ({ pageNumber, onDelete, dragHandleProps }) => {
  return (
    <div className="w-full bg-base-200 border-1 border-primary-200 text-secondary h-8 flex items-center justify-between px-4 rounded-lg">
      {/* Move icon on left */}
      <div {...dragHandleProps} className="cursor-move">
        <ArrowsUpDownIcon className="h-3 w-3 text-secondary" />
      </div>

      {/* Page number in center */}
      <span className="text-xs font-medium text-secondary">
        Page {pageNumber + 1}
      </span>

      {/* Delete button on right */}
      <button
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
          onDelete(pageNumber)
        }}
        className="btn btn-ghost btn-xs text-error hover:bg-error/10 p-0"
        title="Delete page break"
      >
        <TrashIcon className="h-3 w-3" />
      </button>
    </div>
  )
}

export default PageBreak
