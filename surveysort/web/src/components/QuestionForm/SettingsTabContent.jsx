import { useState, useCallback } from 'react'
import { useForm, Form } from '@redwoodjs/forms'
import { useQuery, useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { FileUploader } from 'src/components/catalyst/file-uploader'



import {
  SURVEY_SETTINGS_QUERY,
  UPDATE_SURVEY_SETTINGS_MUTATION,
  GET_PRESIGNED_URL_FOR_SURVEY_COMPANY_LOGO_UPLOAD,
} from './QuestionFormQueries'

const SettingsTabContent = ({ questions, surveyId }) => {
  const methods = useForm()
  const [groups, setGroups] = useState([])
  const [getSurveyCompanyLogoUploadPresignedUrl] = useMutation(GET_PRESIGNED_URL_FOR_SURVEY_COMPANY_LOGO_UPLOAD)
  const [updateSurveySettings] = useMutation(UPDATE_SURVEY_SETTINGS_MUTATION, {
    onCompleted: () => {
      toast.success('Settings updated');
    },
  })

  const { data: settingsData } = useQuery(SURVEY_SETTINGS_QUERY, {
    variables: { surveyId },
    onCompleted: (data) => {
      if (data?.surveySettings?.randomizationGroups) {
        setGroups(data.surveySettings.randomizationGroups)
      }
    },
  })

  const settings = settingsData?.surveySettings || {}

  const handleSave = async (field, value, toastId) => {
    try {
      await updateSurveySettings({
        variables: {
          surveyId,
          input: {
            [field]: value,
            randomizationGroups: groups.map((g, i) => ({
              name: `group_${i + 1}`,
              questionIds: g.questionIds
            }))
          }
        }
      });
      toast.dismiss(toastId);
    } catch (error) {
      toast.error(error.message)
    }
  }

  const handleLogoUpload = useCallback(async (file) => {
    try {

      if (file === null) {
        await handleSave('companyLogo', null);
        return;
      }

      // Show loading state
      const toastId = toast.loading('Uploading company logo image...')

      const { data } = await getSurveyCompanyLogoUploadPresignedUrl({
        variables: {
          input: {
            fileName: file.name,
            fileType: file.type,
            surveyId,
          },
        },
      })
      const { signedUrl, filePath } = data.getSurveyCompanyLogoUploadPresignedUrl
      const result = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (!result.ok) throw new Error('Upload failed')
      await handleSave('companyLogo', filePath, toastId);
    } catch (error) {
      toast.error('Failed to upload logo')
    }
  }, [])

  const questionsByPage = questions.reduce((acc, q) => {
    acc[q.pageNumber] = acc[q.pageNumber] || []
    acc[q.pageNumber].push(q)
    return acc
  }, {})

  const handleRemoveQuestion = async (groupIndex, questionId) => {
    const newGroups = groups.map(group => ({
      ...group,
      questionIds: [...group.questionIds]
    }))

    newGroups[groupIndex].questionIds = newGroups[groupIndex].questionIds
      .filter(id => id !== questionId)

    setGroups(newGroups)

    try {
      await updateSurveySettings({
        variables: {
          surveyId,
          input: {
            randomizationGroups: newGroups.map((g, i) => ({
              name: `group_${i + 1}`,
              questionIds: g.questionIds
            }))
          }
        }
      })
    } catch (error) {
      toast.error(error.message)
    }
  }

  const handleRemoveGroup = async (groupIndex) => {
    const newGroups = groups.filter((_, i) => i !== groupIndex)
    setGroups(newGroups)

    try {
      await updateSurveySettings({
        variables: {
          surveyId,
          input: {
            randomizationGroups: newGroups.map((g, i) => ({
              name: `group_${i + 1}`,
              questionIds: g.questionIds
            }))
          }
        }
      })
    } catch (error) {
      toast.error(error.message)
    }
  }

  return (
    <div className="space-y-4">
      {/* Display Settings */}
      <div className="overflow-hidden bg-base-100 shadow-sm ring-1 ring-base-300 rounded-xl w-full divide-y divide-base-300">
        <div className="collapse collapse-arrow">
          <input type="checkbox" defaultChecked />
          <div className="collapse-title text-xs font-medium">
            Display Settings
          </div>
          <div className="collapse-content space-y-3">
            {/* Progress Bar Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-xs">Show Progress Bar</div>
                <div className="text-xs text-base-content/70">Display progress as participants complete the survey</div>
              </div>
              <input
                type="checkbox"
                className="toggle toggle-primary toggle-sm"
                checked={settings.showProgressBar}
                onChange={(e) => handleSave('showProgressBar', e.target.checked)}
              />
            </div>

            {/* Navigation Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-xs">Show Navigation</div>
                <div className="text-xs text-base-content/70">Allow participants to navigate between pages</div>
              </div>
              <input
                type="checkbox"
                className="toggle toggle-primary toggle-sm"
                checked={settings.showNavigation}
                onChange={(e) => handleSave('showNavigation', e.target.checked)}
              />
            </div>

            {/* Company Logo */}
            <div>
              <div className="font-medium text-xs mb-1">Company Logo</div>
              <div className="text-xs text-base-content/70 mb-2">Upload your company logo</div>
              <FileUploader
                onUpload={handleLogoUpload}
                accept="image/*"
                maxSize={5242880} // 5MB
                currentFile={settings.companyLogo}
              />
            </div>

            {/* Remove Branding Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-xs">Remove Surveysort Branding</div>
                <div className="text-xs text-base-content/70">Hide "Powered by Surveysort" from the survey</div>
              </div>
              <input
                type="checkbox"
                className="toggle toggle-primary toggle-sm"
                checked={settings.removeBranding}
                onChange={(e) => handleSave('removeBranding', e.target.checked)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Question Randomization */}
      <div className="overflow-hidden bg-base-100 shadow-sm ring-1 ring-base-300 rounded-xl w-full divide-y divide-base-300">
        <div className="collapse collapse-arrow">
          <input type="checkbox" />
          <div className="collapse-title text-sm font-medium">
            Question Randomization
          </div>
          <div className="collapse-content">
            <div className="space-y-2">
              {groups.map((group, groupIndex) => (
                <div key={groupIndex} className="relative w-full">
                  {Object.entries(questionsByPage).map(([pageNum, pageQuestions]) => {
                    const selectedQuestions = group.questionIds
                      .map(id => questions.find(q => q.id === id))
                      .filter(q => q && q.pageNumber === parseInt(pageNum))

                    const availableQuestions = pageQuestions.filter(
                      q => !groups.some(g => g.questionIds.includes(q.id))
                    )

                    return (selectedQuestions.length > 0 || availableQuestions.length > 0) ? (
                      <div key={pageNum} className="p-2">
                        <div className="text-xs opacity-50 mb-1">Page {parseInt(pageNum) + 1}</div>
                        <select
                          className="select select-xs select-bordered w-full"
                          onChange={(e) => {
                            const questionId = e.target.value
                            if (!questionId) return

                            // Create deep copy of groups
                            const newGroups = groups.map(group => ({
                              ...group,
                              questionIds: [...group.questionIds]
                            }))

                            // Add question to the group
                            newGroups[groupIndex].questionIds = [...newGroups[groupIndex].questionIds, questionId]
                            setGroups(newGroups)
                          }}
                          value=""
                        >
                          <option value="">Add question...</option>
                          {pageQuestions.map((q) => (
                            <option
                              key={q.id}
                              value={q.id}
                              disabled={groups.some(g => g.questionIds.includes(q.id))}
                            >
                              {q.title}
                            </option>
                          ))}
                        </select>

                        {selectedQuestions.length > 0 && (
                          <div className="mt-1">
                            {selectedQuestions.map(question => (
                              <div
                                key={question.id}
                                className="flex items-center gap-1 text-xs p-1"
                              >
                                <span className="truncate flex-1">{question.title}</span>
                                <button
                                  type="button"
                                  className="btn btn-ghost btn-xs"
                                  onClick={() => handleRemoveQuestion(groupIndex, question.id)}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : null
                  })}

                  <div className="flex justify-end p-2">
                    <button
                      type="button"
                      className="btn btn-ghost btn-xs text-error"
                      onClick={() => handleRemoveGroup(groupIndex)}
                    >
                      Remove Group
                    </button>
                  </div>
                </div>
              ))}

              <div className="flex justify-between p-2">
                <button
                  type="button"
                  onClick={() => setGroups([...groups, { questionIds: [] }])}
                  className="btn btn-ghost btn-xs"
                >
                  Add Group
                </button>

                {groups.length > 0 && (
                  <button
                    type="button"
                    onClick={handleSave}
                    className="btn btn-xs btn-primary"
                  >
                    Save
                  </button>
                )}
              </div>

              {groups.length === 0 && (
                <div className="text-center p-4 text-base-content/70">
                  <p className="text-xs">Add a group to randomize questions within pages</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsTabContent
