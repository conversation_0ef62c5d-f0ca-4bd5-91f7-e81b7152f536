import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import { HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline'

const LIKE_DISLIKE_CHOICES = [
  { value: 'like', label: 'Like', text: 'I Like', icon: HandThumbUpIcon },
  { value: 'dislike', label: 'Dislike', text: 'I Dislike', icon: HandThumbDownIcon }
]

const LikeDislikeQuestion = ({ question }) => {
  const { setValue } = useFormContext()

  useEffect(() => {
    // Set initial values for the form
    setValue('type', 'LIKE_DISLIKE')
    setValue('required', false)
    setValue('pageNumber', 0)
    setValue('order', 0)
    setValue('questionConfig', {
      choices: LIKE_DISLIKE_CHOICES.map(
        choice => `${choice.value}|${choice.label}|${choice.text}`
      )
      // Don't set minLength and maxLength at all for this question type
    })
  }, [setValue])

  return (
    <div className="space-y-2 p-2">
      <div className="flex justify-center gap-8 mt-4">
        {LIKE_DISLIKE_CHOICES.map((choice) => {
          const Icon = choice.icon
          return (
            <div 
              key={choice.value} 
              className="flex flex-col items-center tooltip" 
              data-tip={choice.text}
            >
              <Icon className="h-8 w-8" />
              <span className="text-sm mt-1">{choice.label}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default LikeDislikeQuestion 