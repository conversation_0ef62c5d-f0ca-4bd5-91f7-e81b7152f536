import {
  DocumentDuplicateIcon,
  ArrowsRightLeftIcon,
  TrashIcon,
} from '@heroicons/react/24/solid'
import { DocumentTextIcon } from '@heroicons/react/24/outline'

import { questionTypeIcons } from './constants'
import SingleQuestionForm from './SingleQuestionForm'

const QuestionSummary = ({
  question,
  index,
  questionCount,
  onEdit,
  onDelete,
  onSave,
  dragHandleProps,
  isExpanded,
  onPreviewUpdate,
  currentPage
}) => {
  const TypeIcon = questionTypeIcons[question.type] || DocumentTextIcon
  const orderNumber = String(index + 1).padStart(2, '0')

  if (isExpanded) {
    return (

      <SingleQuestionForm
        question={question}
        questionCount={questionCount}
        onSave={onSave}
        onBack={onEdit}
        onDelete={onDelete}
   
        onPreviewUpdate={onPreviewUpdate}
        currentPage={currentPage}
      />

    )
  }

  const dragProps = (!isExpanded) ? dragHandleProps : {}

  return (
    <div
      {...dragProps}
      className={`relative flex justify-between gap-x-6 px-4 py-5 hover:bg-base-200 transition-colors cursor-pointer`}
      onClick={() => onEdit()}
      role="button"
      tabIndex={0}
    >
      <div className="flex min-w-0 gap-x-4">
        <div className="min-w-0 flex-auto">
          <div className="flex items-center gap-1">
            <span className="text-xs font-medium text-base-content/70">
              Question {orderNumber}
            </span>
            <TypeIcon className="h-3 w-3 text-primary" />
          </div>
          <h4 className="font-medium text-sm mt-1">{question.title}</h4>
        </div>
      </div>
    </div>
  )
}

export default QuestionSummary
