import NewQuestionForm from './NewQuestionForm'
import QuestionLibraryPopupComponent from 'src/components/QuestionLibrary/QuestionLibraryPopup'
import { Dialog, DialogPanel, DialogTitle, DialogBackdrop } from '@headlessui/react'

const QuestionFormDialogs = ({
  isNewQuestionDialogOpen,
  showQuestionLibrary,
  isDeleteDialogOpen,
  surveyId,
  closeNewQuestionDialog,
  setShowQuestionLibrary,
  closeDeleteDialog,
  handleNewQuestionSuccess,
  handleSelectedLibraryQuestions,
  handleDeleteQuestion,
}) => {
  return (
    <>
      <NewQuestionForm
        surveyId={surveyId}
        onSuccess={handleNewQuestionSuccess}
        isOpen={isNewQuestionDialogOpen}
        onClose={closeNewQuestionDialog}
      />

      {/* Question Library Dialog */}
      <Dialog
        open={showQuestionLibrary}
        onClose={() => setShowQuestionLibrary(false)}
        className="relative z-[99999]"
      >
        <DialogBackdrop
          className="fixed inset-0 bg-base-300/70 backdrop-blur-lg transition-opacity"
        />

        <div className="fixed inset-0 z-[99999] w-screen overflow-y-auto p-4 sm:p-6 md:p-20">
          <DialogPanel className="mx-auto max-w-2xl transform divide-y divide-base-200 overflow-hidden rounded-xl bg-base-100 shadow-xl ring-1 ring-base-300/5 transition-all">
            <div className="p-6">
              <DialogTitle className="text-lg font-medium">Question Library</DialogTitle>
              <p className="mt-1 text-sm text-base-content/70">
                Browse and select from our collection of pre-built questions. Add them directly to your survey or customize them to fit your needs.
              </p>
            </div>

            <div className="p-6">
              <QuestionLibraryPopupComponent
                onClose={() => setShowQuestionLibrary(false)}
                onDone={handleSelectedLibraryQuestions}
              />
            </div>
          </DialogPanel>
        </div>
      </Dialog>

      {/* Delete Question Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        className="relative z-[99999]"
      >
        <DialogBackdrop
          className="fixed inset-0 bg-base-300/70 backdrop-blur-lg transition-opacity"
        />

        <div className="fixed inset-0 z-[99999] w-screen overflow-y-auto p-4 sm:p-6 md:p-20">
          <DialogPanel className="mx-auto max-w-sm transform overflow-hidden rounded-xl bg-base-100 p-6 shadow-xl ring-1 ring-base-300/5 transition-all">
            <DialogTitle className="text-lg font-medium mb-4">Delete Question</DialogTitle>
            <p className="text-sm text-base-content/70 mb-6">
              Are you sure you want to delete this question?
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="btn btn-ghost btn-sm"
                onClick={closeDeleteDialog}
              >
                Cancel
              </button>
              <button
                className="btn btn-error btn-sm"
                onClick={handleDeleteQuestion}
              >
                Delete
              </button>
            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </>
  )
}

export default QuestionFormDialogs
