import { useState, useEffect } from 'react'
import { PlusIcon, MinusIcon } from '@heroicons/react/20/solid'
import { useFieldArray, useFormContext } from 'react-hook-form'

import { Label, TextField } from '@redwoodjs/forms'

const MatrixQuestion = () => {
  const { control, register, watch } = useFormContext()
  const [selectedRandomRows, setSelectedRandomRows] = useState([])

  const watchRandomize = watch('questionConfig.randomize')

  const {
    fields: rows,
    append: appendRow,
    remove: removeRow,
  } = useFieldArray({
    control,
    name: `questionConfig.rows`,
  })
  const {
    fields: columns,
    append: appendColumn,
    remove: removeColumn,
  } = useFieldArray({
    control,
    name: `questionConfig.columns`,
  })

  useEffect(() => {
    if (!watchRandomize) {
      setSelectedRandomRows([])
    }
  }, [watchRandomize])

  return (
    <div className="space-y-2">
      <div>
        <Label name={`questionConfig.rows`} className="label py-0.5">
          <span className="label-text text-xs">Rows</span>
        </Label>
        {rows.map((row, rowIndex) => (
          <div key={rowIndex} className="mb-1 flex items-center space-x-1">
            <TextField
              name={`questionConfig.rows.${rowIndex}`}
              defaultValue={row}
              className="input input-xs input-bordered w-full"
              placeholder={`Row ${rowIndex + 1}`}
              {...register(`questionConfig.rows.${rowIndex}`)}
            />
            <button
              type="button"
              onClick={() => removeRow(rowIndex)}
              className="btn btn-square btn-error btn-xs"
            >
              <MinusIcon className="h-4 w-4" />
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => appendRow('')}
          className="btn btn-primary btn-outline btn-xs"
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          <span className="text-xs">Add Row</span>
        </button>
      </div>

      <div>
        <Label name={`questionConfig.columns`} className="label py-0.5">
          <span className="label-text text-xs">Columns</span>
        </Label>
        {columns.map((column, columnIndex) => (
          <div key={columnIndex} className="mb-1 flex items-center space-x-1">
            <TextField
              name={`questionConfig.columns.${columnIndex}`}
              defaultValue={column}
              className="input input-xs input-bordered w-full"
              placeholder={`Column ${columnIndex + 1}`}
              {...register(`questionConfig.columns.${columnIndex}`)}
            />
            <button
              type="button"
              onClick={() => removeColumn(columnIndex)}
              className="btn btn-square btn-error btn-xs"
            >
              <MinusIcon className="h-4 w-4" />
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => appendColumn('')}
          className="btn btn-primary btn-outline btn-xs"
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          <span className="text-xs">Add Column</span>
        </button>
      </div>

      <div>
        <Label name={`questionConfig.cellType`} className="label py-0.5">
          <span className="label-text text-xs">Cell Type</span>
        </Label>
        <select
          {...register(`questionConfig.cellType`)}
          className="select select-xs select-bordered w-full"
        >
          <option value="radio">Single Choice (Radio)</option>
          <option value="checkbox">Multiple Choice (Checkbox)</option>
        </select>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="label py-0.5">
            <span className="label-text text-xs">Randomize Rows</span>
          </Label>
          <input
            type="checkbox"
            {...register('questionConfig.randomize')}
            className="toggle toggle-xs toggle-primary"
          />
        </div>

        {watchRandomize && (
          <div className="space-y-2">
            <select 
              className="select select-xs select-bordered w-full"
              onChange={(e) => {
                const rowIndex = parseInt(e.target.value)
                if (isNaN(rowIndex)) return

                setSelectedRandomRows([...selectedRandomRows, rowIndex])
              }}
              value=""
            >
              <option value="">Select rows to randomize...</option>
              {rows.map((row, idx) => (
                <option 
                  key={idx} 
                  value={idx}
                  disabled={selectedRandomRows.includes(idx)}
                >
                  {row.value || `Row ${idx + 1}`}
                </option>
              ))}
            </select>

            {selectedRandomRows.length > 0 && (
              <div className="space-y-1">
                {selectedRandomRows.map((rowIndex) => (
                  <div 
                    key={rowIndex} 
                    className="flex items-center gap-1 text-xs p-1"
                  >
                    <span className="truncate flex-1">
                      {rows[rowIndex]?.value || `Row ${rowIndex + 1}`}
                    </span>
                    <button
                      type="button"
                      className="btn btn-ghost btn-xs"
                      onClick={() => {
                        setSelectedRandomRows(
                          selectedRandomRows.filter(idx => idx !== rowIndex)
                        )
                      }}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}

            <p className="text-xs text-base-content/70">
              {selectedRandomRows.length === 0 
                ? "All rows will be randomized if none are specifically selected"
                : "Only selected rows will be randomized"}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default MatrixQuestion
