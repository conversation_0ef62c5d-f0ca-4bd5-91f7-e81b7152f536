import { useEffect, useState } from 'react'

import { useQuery, useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'


import SurveyPreview from './SurveyPreview'
import DraggableQuestionList from './DraggableQuestionList'
import QuestionFormDialogs from './QuestionFormDialogs'

import {
  SURVEY_QUESTIONS_QUERY,
  SURVEY_SETTINGS_QUERY,
  UPDATE_SURVEY_SETTINGS_MUTATION,
} from './QuestionFormQueries'

import { useQuestionMutations } from './useQuestionMutations'

import { useForm, FormProvider } from '@redwoodjs/forms'
import { Form } from '@redwoodjs/forms'

import SettingsTabContent from './SettingsTabContent'

// Add these helper functions at the top of the file
const resequencePageNumbers = (questions) => {
  const uniquePageNumbers = [...new Set(questions.map(q => q.pageNumber))].sort()
  const pageNumberMap = {}
  uniquePageNumbers.forEach((pageNumber, index) => {
    pageNumberMap[pageNumber] = index
  })

  return questions.map(q => ({
    ...q,
    pageNumber: pageNumberMap[q.pageNumber]
  }))
}

const getItemsWithPageBreaks = (questions) => {
  const items = []
  let currentPage = 0

  questions.forEach((question) => {
    if (question.pageNumber > currentPage) {
      items.push({
        type: 'pageBreak',
        pageNumber: question.pageNumber,
        id: `page-break-${question.pageNumber}`,
      })
      currentPage = question.pageNumber
    }

    items.push({
      type: 'question',
      ...question,
    })
  })

  return items
}

const ThemeTabContent = () => (
  <div className="p-2">
    <p className="text-xs text-base-content/70">Theme settings coming soon...</p>
  </div>
)

const tabs = [
  { name: 'Questions', section: 'questions' },
  { name: 'Settings', section: 'settings' },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

const QuestionForm = ({ surveyId }) => {
  const [selectedQuestion, setSelectedQuestion] = useState(null)
  const [items, setItems] = useState([]);
  const [showQuestionLibrary, setShowQuestionLibrary] = useState(false)
  const [selectedQuestions, setSelectedQuestions] = useState([])
  const [activeTab, setActiveTab] = useState('question')
  const [isDirty, setIsDirty] = useState(false)
  const [isNewQuestionDialogOpen, setNewQuestionDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [questionToDelete, setQuestionToDelete] = useState(null)
  const [isRefetching, setIsRefetching] = useState(false)
  const [previewDevice, setPreviewDevice] = useState('desktop')
  const [showPreview, setShowPreview] = useState(false)
  const [expandedQuestionId, setExpandedQuestionId] = useState(null)
  const [selectedPreviewQuestion, setSelectedPreviewQuestion] = useState(null)
  const [activeSection, setActiveSection] = useState('questions') // Add this state
  const [themeConfig, setThemeConfig] = useState({
    questionTextColor: '#000000',
    answerTextColor: '#4B5563',
    buttonColor: '#3B82F6',
    backgroundColor: '#FFFFFF',
    progressBarColor: '#3B82F6',
  })

  const { data: settingsData } = useQuery(SURVEY_SETTINGS_QUERY, {
    variables: { surveyId },
    onCompleted: (data) => {
      if (data?.surveySettings?.themeConfig) {
        setThemeConfig(data.surveySettings.themeConfig)
      }
    },
  })

  const [updateSurveySettings] = useMutation(UPDATE_SURVEY_SETTINGS_MUTATION, {
    onCompleted: () => {
      toast.success('Theme settings updated')
    },
  })

  const handleThemeChange = (color, type) => {
    setThemeConfig(prev => ({
      ...prev,
      [type]: color.hex
    }))
  }

  const handleThemeApply = async (newThemeConfig) => {
    try {
      await updateSurveySettings({
        variables: {
          surveyId,
          input: {
            themeConfig: newThemeConfig
          }
        }
      })
    } catch (error) {
      toast.error('Failed to update theme settings')
    }
  }

  const setTheItemsState = (fetchedData) => {
    // Use the fetchedData parameter to access the data
    const updatedItems = fetchedData?.surveyQuestions ?
      getItemsWithPageBreaks(resequencePageNumbers(fetchedData.surveyQuestions)) :
      []
    setItems(updatedItems);
  }

  const { loading, error, data, refetch } = useQuery(SURVEY_QUESTIONS_QUERY, {
    variables: { surveyId },
    fetchPolicy: "network-only",
    onCompleted: (fetchedData) => {
      setTheItemsState(fetchedData);
    }
  });

  const handleRefetch = async () => {
    try {
      const { data: refetchedData } = await refetch(); // Await the refetch result
      setTheItemsState(refetchedData);
    } catch (error) {
      console.error("Error refetching survey questions:", error);
    }
  };

  const {
    reorderQuestions,
    updateQuestion,
    deleteQuestion,
    createQuestion,
    handleDeleteQuestion,
    recalculateQuestionOrder, // Add this
  } = useQuestionMutations({ surveyId, refetch })


  const handleDragEnd = async (result) => {
    if (!result.destination) return

    const { source, destination } = result
    const sourceIndex = source.index
    const destinationIndex = destination.index

    const newItems = Array.from(items)
    const [movedItem] = newItems.splice(sourceIndex, 1)
    newItems.splice(destinationIndex, 0, movedItem)

    // Calculate page numbers and orders based on item positions
    let currentPageNumber = 0
    let orderInPage = 0
    const updatedQuestions = []

    newItems.forEach((item) => {
      if (item.type === 'pageBreak') {
        currentPageNumber++
        orderInPage = 0
      } else {
        // If it's not a page break, it's a question
        updatedQuestions.push({
          id: item.id,
          pageNumber: currentPageNumber,
          order: orderInPage++
        })
      }
    })

    console.log('newItems', newItems)
    console.log('updatedQuestions', updatedQuestions)

    try {
      await reorderQuestions({
        variables: {
          input: {
            surveyId,
            questions: updatedQuestions
          }
        }
      })
      await handleRefetch()
    } catch (error) {
      console.error('Error updating questions:', error)
      toast.error('Failed to update questions')
    }
  }

  const handleSelectedLibraryQuestions = async (selected) => {
    setSelectedQuestions(selected)
    selected.forEach(async (data) => { // TODO : convert to bulk API
      const input = {
        title: data.title,
        type: data.type,
        required: false, // Default value; adjust as needed
        surveyId, // Assuming categoryId corresponds to surveyId
        questionConfig: {
          choices: data.type === 'MULTIPLE_CHOICE' ? data.choices : [],
          minLength: 0,
          maxLength: 1000,
          rules: [],
          condition: 'all',
          actions: [],
          rating: data.type === 'RATING' ? Number(data?.choices[data.choices.length - 1]) : null, // Set rating if type is RATING
          scale: ['SCALE', 'SCORING'].includes(data.type) ? Number(data?.choices[data.choices.length - 1]) : null, // Set scale if type is SCALE
          rows: [],
          columns: [],
          cellType: '',
          allowMultiple: false,
          allowWriteIn: false,
        },
      };

      // Remove any undefined or null values from questionConfig
      Object.keys(input.questionConfig).forEach(
        (key) =>
          (input.questionConfig[key] === undefined || input.questionConfig[key] === null) &&
          delete input.questionConfig[key]
      );

      console.log('Formatted input:', input);

      try {
        const result = await createQuestion({ variables: { input } });
        console.log('Create question result:', result);
        if (result.data && result.data.createQuestion) {
          toast.success('Question created successfully');
        } else {
          throw new Error('Failed to create question');
        }
      } catch (error) {
        console.error('Error creating question:', error);
        toast.error(`Error creating question: ${error.message}`);
      }
    });
    setShowQuestionLibrary(false);
    await handleRefetch();
  }
  const handleBackClick = () => {

    setSelectedQuestion(null)
    setActiveTab('question')
    setIsDirty(false)
  }

  const handleTabClick = (tabName) => {
    setActiveTab(tabName)
    setIsDirty(false)
  }

  const openNewQuestionDialog = () => {

    setNewQuestionDialogOpen(true)
  }

  const openQuestionLibraryDialog = () => {

    setShowQuestionLibrary(true)
  }

  const closeNewQuestionDialog = () => setNewQuestionDialogOpen(false)

  const handleNewQuestionSuccess = () => {
    closeNewQuestionDialog()
    handleRefetch()
  }

  const openDeleteDialog = (id) => {

    console.log('openDeleteDialog', id)
    setQuestionToDelete(id)
    setDeleteDialogOpen(true)
  }

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false)
    setQuestionToDelete(null)
  }

  const handleDeleteDialogConfirm = () => {
    if (!questionToDelete) return
    handleDeleteQuestion(questionToDelete, data.surveyQuestions)
    closeDeleteDialog()
    handleRefetch()
  }

  const handleAddPageBreak = async (afterIndex) => {


    // Get all questions after this index
    const questionsAfterBreak = items
      .filter((item, idx) => idx > afterIndex && item.type !== 'pageBreak')
      .map(item => ({
        id: item.id,
        pageNumber: item.pageNumber + 1,
        order: item.order
      }))

    // Get all questions before this index
    const questionsBeforeBreak = items
      .filter((item, idx) => idx <= afterIndex && item.type !== 'pageBreak')
      .map(item => ({
        id: item.id,
        pageNumber: item.pageNumber,
        order: item.order
      }))

    const updatedQuestions = [...questionsBeforeBreak, ...questionsAfterBreak]

    try {
      await reorderQuestions({
        variables: {
          input: {
            surveyId,
            questions: recalculateQuestionOrder(updatedQuestions)
          }
        }
      })
      await handleRefetch()
    } catch (error) {
      console.error('Error adding page break:', error)
      toast.error('Failed to add page break')
    }
  }

  const handleDeletePageBreak = async (pageNumber) => {


    // Get all page breaks
    const pageBreaks = items.filter(item => item.type === 'pageBreak')
    if (pageBreaks.length < 1) {
      toast.error('Cannot delete the only page break')
      return
    }

    // Get all questions and their current page numbers
    const questions = items.filter(item => item.type !== 'pageBreak')

    // Calculate new page numbers for questions
    const updatedQuestions = questions.map(question => {
      // If question is on a page after the deleted break, decrease its page number
      if (question.pageNumber >= pageNumber) {
        return {
          id: question.id,
          pageNumber: question.pageNumber - 1,
          order: question.order
        }
      }
      // If question is on a page before the deleted break, keep its page number
      return {
        id: question.id,
        pageNumber: question.pageNumber,
        order: question.order
      }
    })

    try {
      // Use the recalculateQuestionOrder helper to ensure proper ordering
      const reorderedQuestions = recalculateQuestionOrder(updatedQuestions)

      await reorderQuestions({
        variables: {
          input: {
            surveyId,
            questions: reorderedQuestions
          }
        }
      })
      await handleRefetch({
        surveyId,
      });
    } catch (error) {
      console.error('Error removing page break:', error)
      toast.error('Failed to remove page break')
    }
  }

  const handleDuplicateQuestion = async (questionId) => {


    const questionToDuplicate = data.surveyQuestions.find(q => q.id === questionId)
    if (!questionToDuplicate) return

    const { id, __typename, ...questionData } = questionToDuplicate
    const pageQuestions = data.surveyQuestions.filter(
      q => q.pageNumber === questionData.pageNumber
    )

    try {
      // Create duplicated question
      await createQuestion({
        variables: {
          input: {
            ...questionData,
            title: `${questionData.title} (Copy)`,
            pageNumber: questionData.pageNumber,
            order: pageQuestions.length,
            surveyId
          }
        }
      })

      await handleRefetch()
    } catch (error) {
      console.error('Error duplicating question:', error)
      toast.error('Failed to duplicate question')
    }
  }

  const handleMoveToPage = async (questionId) => {


    // Implement page selection dialog and movement logic
    // This would need a new modal component for page selection
  }

  const handleQuestionSelect = (questionId) => {
    setExpandedQuestionId(expandedQuestionId === questionId ? null : questionId)
    setSelectedPreviewQuestion(
      data.surveyQuestions.find(q => q.id === questionId)
    )
  }

  // Add handler for preview updates
  const handlePreviewUpdate = (updatedQuestion) => {
    console.log('Received preview update:', updatedQuestion)
    setSelectedPreviewQuestion(updatedQuestion)
  }

  const renderTabContent = () => {
    switch (activeSection) {
      case 'settings':
        return <SettingsTabContent
          questions={data?.surveyQuestions || []}
          surveyId={surveyId}
        />
      default:
        return (
          <DraggableQuestionList
            items={items}
     
            expandedQuestionId={expandedQuestionId}
            onQuestionSelect={handleQuestionSelect}
            onDeleteQuestion={openDeleteDialog}
            onDuplicateQuestion={handleDuplicateQuestion}
            onMoveToPage={handleMoveToPage}
            updateQuestion={updateQuestion}
            onPreviewUpdate={handlePreviewUpdate}
            onAddQuestion={openNewQuestionDialog}
            onAddPageBreak={handleAddPageBreak}
            onDeletePageBreak={handleDeletePageBreak}
            onOpenQuestionLibrary={openQuestionLibraryDialog}
            onDragEnd={handleDragEnd}
          />
        )
    }
  }

  if (loading || isRefetching)
    return (
      <div className="mx-auto">
        <div className="skeleton mb-4 h-8 w-1/2"></div>
        <div className="skeleton mb-2 h-6 w-full"></div>
        <div className="skeleton mb-2 h-6 w-full"></div>
        <div className="skeleton mb-2 h-6 w-full"></div>
        <div className="skeleton h-10 w-1/4"></div>
      </div>
    )
  if (error) return <div>Error: {error.message}</div>

  return (
    <div className="h-full w-full">
      {/* Mobile Tabs */}
      <div className="sm:hidden">
        <label htmlFor="tabs" className="sr-only">
          Select a tab
        </label>
        <select
          id="tabs"
          name="tabs"
          value={activeSection}
          onChange={(e) => setActiveSection(e.target.value)}
          className="block w-full rounded-md border-base-300 focus:border-primary focus:ring-primary text-xs"
        >
          {tabs.map((tab) => (
            <option key={tab.name} value={tab.section}>
              {tab.name}
            </option>
          ))}
        </select>
      </div>

      {/* Main content area */}
      <div className={`${showPreview ? 'hidden sm:flex' : 'flex'} h-full w-full`}>
        {/* Questions Panel */}
        <div className={`w-1/3 bg-base-100 ${showPreview ? 'hidden sm:flex' : 'flex'} flex-col border-r border-base-300`}>
          {/* Desktop Tabs */}
          <div className="hidden sm:block">
            <nav className="flex space-x-1 p-2" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.name}
                  onClick={() => setActiveSection(tab.section)}
                  className={classNames(
                    'rounded-md px-3 py-1.5 text-xs font-medium',
                    activeSection === tab.section
                      ? 'bg-base-200 text-primary'
                      : 'text-base-content/70 hover:text-base-content hover:bg-base-200/50'
                  )}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto">
            {renderTabContent()}
          </div>
        </div>

        {/* Preview Panel */}
        <div className={`flex-1 bg-base-100 ${!showPreview ? 'hidden sm:block' : 'block'}`}>
          <SurveyPreview
            previewDevice={previewDevice}
            setPreviewDevice={setPreviewDevice}
            selectedQuestion={selectedPreviewQuestion}
            themeConfig={themeConfig}
            onThemeChange={handleThemeChange}
            onThemeApply={handleThemeApply}
          />
        </div>
      </div>

      <QuestionFormDialogs
        isNewQuestionDialogOpen={isNewQuestionDialogOpen}
        showQuestionLibrary={showQuestionLibrary}
        isDeleteDialogOpen={isDeleteDialogOpen}
        surveyId={surveyId}
        closeNewQuestionDialog={closeNewQuestionDialog}
        setShowQuestionLibrary={setShowQuestionLibrary}
        closeDeleteDialog={closeDeleteDialog}
        handleNewQuestionSuccess={handleNewQuestionSuccess}
        handleSelectedLibraryQuestions={handleSelectedLibraryQuestions}
        handleDeleteQuestion={handleDeleteDialogConfirm}
      />
    </div>
  )
}

export default QuestionForm
