import { gql } from '@redwoodjs/web'

export const SURVEY_QUESTIONS_QUERY = gql`
  query SurveyQuestionsQuery($surveyId: String!) {
    surveyQuestions(surveyId: $surveyId) {
      id
      title
      explainer
      type
      required
      surveyId
      pageNumber
      order
      questionConfig {
        choices
        minLength
        maxLength
        rules {
          operation
          value
        }
        condition
        actions {
          type
          questionNumber
        }
        rating
        scale
        includeTime
        allowMultiple
        allowWriteIn
        rows
        columns
        cellType
        requireName
        requireEmail
        requirePhone
        requireCompany
        requireAddress
        requireWebsite
        randomize
        randomizeChoices
        randomizeRows
      }
    }
  }
`

export const UPDATE_QUESTIONS_MUTATION = gql`
  mutation UpdateQuestions($input: UpdateQuestionsInput!) {
    updateQuestions(input: $input) {
      id
      title
      explainer
      type
      required
      surveyId
      questionConfig {
        choices
        minLength
        maxLength
        rules {
          operation
          value
        }
        condition
        actions {
          type
          questionNumber
        }
        rating
        scale
        allowMultiple
        allowWriteIn
        rows
        columns
        cellType
        requireName
        requireEmail
        requirePhone
        requireCompany
        requireAddress
        requireWebsite
        randomize
        randomizeChoices
        randomizeRows
      }
    }
  }
`

export const REORDER_QUESTIONS_MUTATION = gql`
  mutation ReorderQuestions($input: ReorderQuestionsInput!) {
    reorderQuestions(input: $input) {
      id
      order
    }
  }
`

export const UPDATE_QUESTION_MUTATION = gql`
  mutation UpdateQuestion($id: String!, $input: UpdateQuestionInput!) {
    updateQuestion(id: $id, input: $input) {
      id
      title
      explainer
      type
      required
      questionConfig {
        choices
        minLength
        maxLength
        rules {
          operation
          value
        }
        condition
        actions {
          type
          questionNumber
        }
        rating
        scale
        allowMultiple
        allowWriteIn
        rows
        columns
        cellType
        includeTime
        requireName
        requireEmail
        requirePhone
        requireCompany
        requireAddress
        requireWebsite
        randomize
        randomizeChoices
        randomizeRows
      }
    }
  }
`

export const DELETE_QUESTION_MUTATION = gql`
  mutation DeleteQuestion($id: String!) {
    deleteQuestion(id: $id) {
      id
    }
  }
`

export const CREATE_QUESTION_MUTATION = gql`
  mutation CreateQuestion($input: CreateQuestionInput!) {
    createQuestion(input: $input) {
      id
      title
      explainer
      type
      required
      surveyId
      pageNumber
      order
      questionConfig {
        choices
        minLength
        maxLength
        rules {
          operation
          value
        }
        condition
        actions {
          type
          questionNumber
        }
        rating
        scale
        allowMultiple
        allowWriteIn
        rows
        columns
        cellType
        requireName
        requireEmail
        requirePhone
        requireCompany
        requireAddress
        requireWebsite
        randomize
        randomizeChoices
        randomizeRows
      }
    }
  }
`

export const SURVEY_SETTINGS_QUERY = gql`
  query SurveySettingsQuery($surveyId: String!) {
    surveySettings(surveyId: $surveyId) {
      id
      themeConfig {
        questionTextColor
        answerTextColor
        buttonColor
        backgroundColor
        progressBarColor
      }
      companyLogo
      showNavigation
      showProgressBar
      removeBranding
    }
  }
`

export const UPDATE_SURVEY_SETTINGS_MUTATION = gql`
  mutation UpdateSurveySettingsMutation($surveyId: String!, $input: UpdateSurveySettingsInput!) {
    updateSurveySettings(surveyId: $surveyId, input: $input) {
      id
      themeConfig {
        questionTextColor
        answerTextColor
        buttonColor
        backgroundColor
        progressBarColor
      }
      companyLogo
      showNavigation
      showProgressBar
      removeBranding
    }
  }
`
export const GET_PRESIGNED_URL_FOR_SURVEY_COMPANY_LOGO_UPLOAD = gql`
  mutation getSurveyCompanyLogoUploadPresignedUrl($input: PresignedSurveyPreviewImageUrlInput!) {
    getSurveyCompanyLogoUploadPresignedUrl(input: $input) {
      signedUrl
      filePath
    }
  }
`
