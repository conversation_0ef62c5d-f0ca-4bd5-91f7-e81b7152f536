import { useState, useEffect } from 'react'
import { SurveyProvider } from 'src/components/SurveyQuestions/SurveyContext'
import QuestionPreview from './QuestionPreview'
import ThemeSettingsDoc from './ThemeSettingsDoc'

const SurveyPreview = ({ 
  previewDevice, 
  setPreviewDevice,
  selectedQuestion,
  themeConfig: savedThemeConfig,
  onThemeChange,
  onThemeApply
}) => {
  // Local state for theme changes
  const [localThemeConfig, setLocalThemeConfig] = useState(savedThemeConfig)

  // Update local theme when saved theme changes
  useEffect(() => {
    setLocalThemeConfig(savedThemeConfig)
  }, [savedThemeConfig])

  // Handle local theme changes
  const handleLocalThemeChange = (color, type) => {
    setLocalThemeConfig(prev => ({
      ...prev,
      [type]: color.hex
    }))
  }

  // Handle apply - pass the local changes to parent
  const handleApply = () => {
    // Strip __typename before sending to server
    const cleanConfig = {
      questionTextColor: localThemeConfig.questionTextColor,
      answerTextColor: localThemeConfig.answerTextColor,
      buttonColor: localThemeConfig.buttonColor,
      backgroundColor: localThemeConfig.backgroundColor,
      progressBarColor: localThemeConfig.progressBarColor
    }
    onThemeApply(cleanConfig)
  }

  return (
    <div 
      className="flex flex-col h-full relative survey-theme"
      style={{ 
        '--primary': localThemeConfig?.buttonColor || '#3B82F6',
        '--primary-focus': localThemeConfig?.buttonColor || '#3B82F6',
        '--primary-content': '#ffffff',
        '--answer-text': localThemeConfig?.answerTextColor || '#4B5563',
        '--question-text': localThemeConfig?.questionTextColor || '#000000',
        '--background': localThemeConfig?.backgroundColor || '#FFFFFF',
        '--progress': localThemeConfig?.progressBarColor || '#3B82F6',
      }}
    >
      {/* Preview Canvas */}
      <div 
        className="relative flex-1"
        style={{ 
          backgroundColor: localThemeConfig?.backgroundColor || '#FFFFFF'
        }}
      >
        <div className="h-full flex items-center justify-center p-4">
          <SurveyProvider device={previewDevice} isPreview={true}>
            <div 
              className={`
                ${previewDevice === 'mobile' ? 'w-full max-w-sm' : 'w-full max-w-2xl'}
                rounded-lg shadow-lg
              `}
              style={{ 
                color: localThemeConfig?.questionTextColor || '#000000'
              }}
            >
              <QuestionPreview 
                question={selectedQuestion} 
                themeConfig={localThemeConfig}
              />
            </div>
          </SurveyProvider>
        </div>
      </div>

      {/* Theme Settings Doc */}
      <ThemeSettingsDoc 
        themeConfig={localThemeConfig}
        onColorChange={handleLocalThemeChange}
        onApply={handleApply}
        previewDevice={previewDevice}
        setPreviewDevice={setPreviewDevice}
        initialTheme={savedThemeConfig}
      />
    </div>
  )
}

export default SurveyPreview
