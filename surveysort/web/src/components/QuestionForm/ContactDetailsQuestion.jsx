import { useFormContext } from 'react-hook-form'
import { Label } from '@redwoodjs/forms'

const CONTACT_FIELDS = [
  { id: 'requireName', label: 'Name' },
  { id: 'requireEmail', label: 'Email Address' },
  { id: 'requirePhone', label: 'Phone Number' },
  { id: 'requireCompany', label: 'Company Name' },
  { id: 'requireAddress', label: 'Address' },
  { id: 'requireWebsite', label: 'Website' },
]

const ContactDetailsQuestion = () => {
  const { register } = useFormContext()

  return (
    <div className="space-y-2">
      {CONTACT_FIELDS.map(field => (
        <div key={field.id} className="flex w-full items-center justify-between pt-2">
          <Label name={`questionConfig.${field.id}`} className="label py-0">
            <span className="label-text text-xs">Require {field.label}</span>
          </Label>
          <input
            type="checkbox"
            {...register(`questionConfig.${field.id}`)}
            className="toggle toggle-xs"
            defaultChecked={false}
          />
        </div>
      ))}
    </div>
  )
}

export default ContactDetailsQuestion 