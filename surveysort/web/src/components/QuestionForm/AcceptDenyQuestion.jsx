import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'

const ACCEPT_DENY_CHOICES = [
  { value: 'accept', label: 'Accept', text: 'I Accept' },
  { value: 'deny', label: 'Deny', text: 'I Deny' }
]

const AcceptDenyQuestion = ({ question }) => {
  const { setValue } = useFormContext()

  useEffect(() => {
    // Convert the choice objects to strings for storage
    const choiceStrings = ACCEPT_DENY_CHOICES.map(
      choice => `${choice.value}|${choice.label}|${choice.text}`
    )
    setValue('questionConfig.choices', choiceStrings)
  }, [setValue])

  return (
    <div className="space-y-2 p-2">
      <div className="flex justify-center gap-8 mt-4">
        {ACCEPT_DENY_CHOICES.map((choice) => (
          <div 
            key={choice.value} 
            className="flex flex-col items-center tooltip" 
            data-tip={choice.text}
          >
            <span className="text-lg font-medium">{choice.label}</span>
 
          </div>
        ))}
      </div>
    </div>
  )
}

export default AcceptDenyQuestion 