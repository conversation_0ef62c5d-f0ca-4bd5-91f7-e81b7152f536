import { useFormContext } from 'react-hook-form'

import { Label, TextField } from '@redwoodjs/forms'

import EssayQuestion from './EssayQuestion'
import MatrixQuestion from './MatrixQuestion'
import MultipleChoiceQuestion from './MultipleChoiceQuestion'
import RatingQuestion from './RatingQuestion'
import ScaleQuestion from './ScaleQuestion'
import ShortQuestion from './ShortQuestion'
import ScoringQuestion from './ScoringQuestion'
import EmoticonQuestion from './EmoticonQuestion'
import AcceptDenyQuestion from './AcceptDenyQuestion'
import LikeDislikeQuestion from './LikeDislikeQuestion'
import DateTimeQuestion from './DateTimeQuestion'
import ContactDetailsQuestion from './ContactDetailsQuestion'
import WelcomeMessageQuestion from './WelcomeMessageQuestion'
import ThankYouMessageQuestion from './ThankYouMessageQuestion'

const QuestionFormFields = ({ questionType, question }) => {
  const {
    register,
    control,
    formState: { errors },
    watch,
  } = useFormContext()

  // Watch title for preview updates
  watch('title')

  const handleTextAreaHeight = (e) => {
    e.target.style.height = 'inherit'
    e.target.style.height = `${e.target.scrollHeight}px`
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="space-y-2">
        <div>
          <Label name="title" className="label">
            <span className="label-text text-xs">Question Title</span>
          </Label>
          <textarea
            name="title"
            defaultValue={question?.title}
            placeholder="Type question title here"
            className="textarea textarea-xs textarea-bordered w-full h-7 resize-none overflow-hidden"
            onInput={handleTextAreaHeight}
            {...register('title', {
              required: 'Title is required',
            })}
          />
          {errors?.title && (
            <span className="text-xs text-error mt-0.5">{errors.title.message}</span>
          )}
        </div>

        <div>
          <Label name="explainer" className="label">
            <span className="label-text text-xs">Question Explainer</span>
            <span className="label-text-alt text-xs text-info-content">
              Appears as helper text above the question
            </span>
          </Label>
          <textarea
            name="explainer"
            defaultValue={question?.explainer}
            placeholder="Add additional context or instructions..."
            className="textarea textarea-xs textarea-bordered w-full h-7 resize-none overflow-hidden"
            onInput={handleTextAreaHeight}
            {...register('explainer')}
          />
        </div>
      </div>

      <div className="w-full">
        {questionType === 'MULTIPLE_CHOICE' && (
          <MultipleChoiceQuestion
            question={question}
            control={control}
            register={register}
            errors={errors}
          />
        )}
        {questionType === 'SHORT' && (
          <ShortQuestion
            register={register}
            errors={errors}
            question={question}
          />
        )}
        {questionType === 'ESSAY' && (
          <EssayQuestion
            register={register}
            errors={errors}
            question={question}
          />
        )}
        {questionType === 'RATING' && (
          <RatingQuestion
            register={register}
            errors={errors}
            question={question}
            control={control}
          />
        )}
        {questionType === 'SCALE' && (
          <ScaleQuestion
            register={register}
            errors={errors}
            question={question}
            control={control}
          />
        )}
        {questionType === 'SCORING' && (
          <ScoringQuestion
            register={register}
            errors={errors}
            question={question}
            control={control}
          />
        )}
        {questionType === 'MATRIX' && (
          <MatrixQuestion
            register={register}
            errors={errors}
            question={question}
            control={control}
          />
        )}
        {questionType === 'EMOTICON' && (
          <EmoticonQuestion
            question={question}
          />
        )}
        {questionType === 'ACCEPT_DENY' && (
          <AcceptDenyQuestion
            question={question}
          />
        )}
        {questionType === 'LIKE_DISLIKE' && (
          <LikeDislikeQuestion
            question={question}
          />
        )}
        {questionType === 'DATE_TIME' && (
          <DateTimeQuestion
            question={question}
          />
        )}
        {questionType === 'CONTACT_DETAILS' && (
          <ContactDetailsQuestion
            question={question}
          />
        )}
        {questionType === 'WELCOME_MESSAGE' && (
          <WelcomeMessageQuestion question={question} />
        )}
        {questionType === 'THANK_YOU_MESSAGE' && (
          <ThankYouMessageQuestion question={question} />
        )}
      </div>

      <div className="flex w-full items-center justify-between">
        <Label name="required" className="label py-0">
          <span className="label-text text-xs">Required</span>
        </Label>
        <input
          type="checkbox"
          {...register('required')}
          className="toggle toggle-xs"
        />
      </div>
    </div>
  )
}

export default QuestionFormFields
