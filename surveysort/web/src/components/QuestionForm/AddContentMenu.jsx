import { PlusIcon, DocumentDuplicateIcon } from '@heroicons/react/24/solid'

const AddContentMenu = ({ onAddQuestion, onAddPageBreak, afterIndex }) => {
  return (
    <div className="relative opacity-0 z-20 group-hover:opacity-100 transition-opacity">
      <div className="absolute left-1/2 -translate-x-1/2 flex justify-center">
        <div className="dropdown dropdown-top">
          {/* Show this button on hover */}
          <button tabIndex={0} className="btn btn-xs btn-circle btn-primary ">
            <PlusIcon className="h-4 w-4" />
          </button>

          {/* Menu only shows when button is clicked */}
          <ul tabIndex={0} className="dropdown-content menu menu-sm p-2 shadow bg-base-100 rounded-box w-48">
            <li>
              <a onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onAddQuestion(afterIndex)
              }}>
                <PlusIcon className="h-4 w-4" />
                Add Question
              </a>
            </li>
            <li>
              <a onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onAddPageBreak(afterIndex)
              }}>
                <DocumentDuplicateIcon className="h-4 w-4" />
                Add Page Break
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default AddContentMenu
