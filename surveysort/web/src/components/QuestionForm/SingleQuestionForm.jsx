import { useState, useCallback } from 'react'
import {
  ArrowLeftIcon,
  TrashIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/solid'
import { useForm, FormProvider } from 'react-hook-form'
import { Form, Submit, TextField, Label } from '@redwoodjs/forms'
import { toast } from '@redwoodjs/web/toast'
import QuestionF<PERSON>Fields from './QuestionFormFields'
import RuleActions from './RuleActions'
import { useQuestionPreview } from './hooks/useQuestionPreview'
import { useQuestionForm } from './hooks/useQuestionForm'
import { formatQuestionConfig } from './utils/questionUtils'
import { questionTypeIcons } from './constants'
import { DocumentTextIcon } from '@heroicons/react/24/outline'

// Move to separate components file
const FormHeader = ({ onBack, onDelete }) => (
  <div className="mb-1 bg-white flex items-center justify-between">
    <div className="flex items-center">
      <button
        type="button"
        onClick={onBack}
        className="btn btn-ghost btn-xs p-0.5"
 
      >
        <ArrowLeftIcon className="h-4 w-4" />
      </button>
      <h2 className="ml-2 text-xs text-base-content/70">Edit</h2>
    </div>

    <div className="dropdown dropdown-end z-50">
      <button
        type="button"
        className="btn btn-ghost btn-xs"
     
      >
        <EllipsisVerticalIcon className="h-4 w-4" />
      </button>

      <ul className="dropdown-content z-[1] menu p-1 shadow bg-base-100 rounded-box w-48">
        <li>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="text-error text-xs font-medium"
          >
            <TrashIcon className="h-4 w-4" />
            Delete
          </button>
        </li>
      </ul>
    </div>
  </div>
)

const FormContent = ({ content, formMethods, onSubmit }) => (
  <FormProvider {...formMethods}>
    <Form formMethods={formMethods} onSubmit={onSubmit}>
      {content}
      <div className="mt-2 bg-white text-right">
        <Submit
          className={`btn btn-outline btn-primary btn-xs`}
        >
          Save
        </Submit>
      </div>
    </Form>
  </FormProvider>
)

const SingleQuestionForm = ({
  question,
  questionCount,
  onSave,
  onBack,
  onDelete,
  onPreviewUpdate,
  currentPage
}) => {
  const [activeTab, setActiveTab] = useState('question')
  const TypeIcon = questionTypeIcons[question.type] || DocumentTextIcon
  const { formMethods, handleSubmit } = useQuestionForm({
    question,
    onSave,
    currentPage
  })

  const { handlePreviewUpdate } = useQuestionPreview({
    question,
    onPreviewUpdate,
    activeTab,
    currentPage,
    formMethods
  })

  const renderTabContent = useCallback((tabName) => {
    const content = tabName === 'question' ? (
      <QuestionFormFields
        question={question}
        questionType={question.type}
      />
    ) : (
      <RuleActions
        questionType={question.type}
        choices={question.questionConfig?.choices || []}
        currentQuestionNumber={question.order + 1}
        questionCount={questionCount}
      />
    )

    return (
      <FormContent
        content={content}
        formMethods={formMethods}
        onSubmit={handleSubmit}

      />
    )
  }, [question, formMethods, handleSubmit])

  return (
    <div className="bg-white p-2">
      <div className="flex items-center gap-1">
        <span className="text-xs font-medium text-base-content/70">
          Question {String(question.order + 1).padStart(2, '0')}</span>
        <TypeIcon className="h-3 w-3 text-primary" />
      </div>
      <FormHeader
        onBack={onBack}
        onDelete={onDelete}
     
      />

      <div className="space-y-1">
        <div role="tablist" className="tabs tabs-lifted tabs-xs">
          <a
            role="tab"
            className={`tab ${activeTab === 'question' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('question')}
          >
            Question
          </a>
          <a
            role="tab"
            className={`tab ${activeTab === 'rules' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('rules')}
          >
            Rules
          </a>
        </div>

        <div className="bg-white p-0.5">
          {renderTabContent(activeTab)}
        </div>
      </div>
    </div>
  )
}

export default SingleQuestionForm

