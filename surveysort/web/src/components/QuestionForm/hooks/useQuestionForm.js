import { useForm } from 'react-hook-form'
import { toast } from '@redwoodjs/web/toast'
import { formatQuestionConfig, cleanupConfig } from '../utils/questionUtils'

export const useQuestionForm = ({ question, onSave, currentPage }) => {
  const formMethods = useForm({
    defaultValues: {
      ...question,
      questionConfig: {
        ...question.questionConfig,
        rating: question.questionConfig?.rating?.toString() || '',
        scale: question.questionConfig?.scale?.toString() || '',
        randomize: question.questionConfig?.randomize || false,
        randomizeChoices: question.questionConfig?.randomizeChoices || [],
        randomizeRows: question.questionConfig?.randomizeRows || [],
      },
    },
    mode: 'onSubmit'
  })

  const handleSubmit = async (data) => {
 
    console.log('Before Submitting question with config:', data)
    try {
      const input = {
        title: data.title,
        type: data.type,
        required: data.required,
        pageNumber: currentPage,
        questionConfig: cleanupConfig(formatQuestionConfig({}, {
          ...data.questionConfig,
          randomize: Bo<PERSON><PERSON>(data.questionConfig?.randomize),
          randomizeChoices: data.questionConfig?.randomizeChoices || [],
          randomizeRows: data.questionConfig?.randomizeRows || [],
        }))
      }

      console.log('saving question with config:', input.questionConfig)

      return onSave({
        variables: {
          id: question.id,
          input,
        },
      })
    } catch (error) {
      console.error('Error saving question:', error)
      toast.error('Failed to save question')
    }
  }

  return {
    formMethods,
    handleSubmit
  }
} 