import { useState, useEffect, useRef, useCallback } from 'react'
import { isEqual } from 'lodash'
import { formatQuestionConfig } from '../utils/questionUtils'

export const useQuestionPreview = ({
  question,
  onPreviewUpdate,
  activeTab,
  currentPage,
  formMethods
}) => {
  const [lastPreviewData, setLastPreviewData] = useState(null)
  const previewTimeoutRef = useRef(null)
  const isInitialMount = useRef(true)

  const createPreviewData = useCallback((formData) => ({
    id: question.id,
    title: formData?.title || question.title,
    explainer: formData?.explainer || question.explainer,
    type: formData?.type || question.type,
    required: formData?.required ?? question.required,
    pageNumber: currentPage,
    questionConfig: formatQuestionConfig(
      question.questionConfig,
      formData?.questionConfig
    )
  }), [question, currentPage])

  // Handle form changes
  useEffect(() => {
    if (!onPreviewUpdate || activeTab !== 'question') return

    const subscription = formMethods.watch((formData) => {
      if (previewTimeoutRef.current) {
        clearTimeout(previewTimeoutRef.current)
      }

      previewTimeoutRef.current = setTimeout(() => {
        const previewData = createPreviewData(formData)
        
        if (!lastPreviewData || !isEqual(previewData, lastPreviewData)) {
          setLastPreviewData(previewData)
          onPreviewUpdate(previewData)
        }
      }, 300)
    })

    return () => {
      subscription.unsubscribe()
      if (previewTimeoutRef.current) {
        clearTimeout(previewTimeoutRef.current)
      }
    }
  }, [formMethods, createPreviewData, onPreviewUpdate, activeTab, lastPreviewData])

  // Initial setup and question changes
  useEffect(() => {
    if (!question || !onPreviewUpdate) return

    if (isInitialMount.current) {
      isInitialMount.current = false
      const initialData = createPreviewData(formMethods.getValues())
      setLastPreviewData(initialData)
      onPreviewUpdate(initialData)
    } else {
      // Only update on question change if it's different
      const newPreviewData = createPreviewData(formMethods.getValues())
      if (!isEqual(newPreviewData, lastPreviewData)) {
        setLastPreviewData(newPreviewData)
        onPreviewUpdate(newPreviewData)
      }
    }
  }, [question, onPreviewUpdate, createPreviewData, formMethods])

  return {
    currentPreview: lastPreviewData
  }
} 