import { useFieldArray, useFormContext } from 'react-hook-form'

import { Label, TextField } from '@redwoodjs/forms'

const multipleChoiceOperations = [
  { value: 'is selected', label: 'is selected' },
  { value: 'is not selected', label: 'is not selected' },
]

const ratingScaleOperations = [
  { value: '>', label: 'is greater than' },
  { value: '<', label: 'is less than' },
  { value: '>=', label: 'is greater than or equal to' },
  { value: '<=', label: 'is less than or equal to' },
  { value: '==', label: 'is equal to' },
  { value: '!=', label: 'is not equal to' },
]

const defaultRuleOperations = [
  { value: 'is', label: 'is' },
  { value: 'is not', label: 'is not' },
  { value: 'begins with', label: 'begins with' },
  { value: 'does not begin with', label: 'does not begin with' },
  { value: 'contains', label: 'contains' },
  { value: 'does not contain', label: 'does not contain' },
  { value: 'is blank', label: 'is blank' },
  { value: 'is not blank', label: 'is not blank' },
]

export const likeDislikeOperations = [
  { value: 'is selected', label: 'is selected' },
]

const actionTypes = [
  { value: 'skip', label: 'Skip' },
  { value: 'go to', label: 'Go to' },
  { value: 'end survey', label: 'End Survey' },
  { value: 'disqualify participant', label: 'Disqualify Participant' },
]

export const getRuleOperations = (questionType) => {
  if (questionType === 'MULTIPLE_CHOICE') {
    return multipleChoiceOperations
  } else if (questionType === 'RATING' || questionType === 'SCALE') {
    return ratingScaleOperations
  } else if (questionType === 'LIKE_DISLIKE') {
    return likeDislikeOperations
  }
  return defaultRuleOperations
}


const RuleActions = ({
  questionType,
  choices,
  currentQuestionNumber,
  questionCount,
}) => {
  const {
    control,
    register,
    watch,
    formState: { errors },
  } = useFormContext()
  const {
    fields: ruleFields,
    append: appendRule,
    remove: removeRule,
  } = useFieldArray({
    control,
    name: `questionConfig.rules`,
  })
  const {
    fields: actionFields,
    append: appendAction,
    remove: removeAction,
  } = useFieldArray({
    control,
    name: `questionConfig.actions`,
  })
  const watchRules = watch(`questionConfig.rules`) || ruleFields
  const watchActions = watch(`questionConfig.actions`) || actionFields

  console.log('Watched rules:', watchRules)


  if (questionType === 'MATRIX' || questionType === 'LIKE_DISLIKE') {
    return null // Don't render anything for non-matrix questions
  }

  return (
    <div className="space-y-2">
      <Label name="questionConfig.condition" className="label py-0.5">
        <span className="label-text text-xs">Condition</span>
      </Label>
      <select
        {...register(`questionConfig.condition`)}
        className="select select-xs select-bordered w-full"
      >
        <option value="all">All rules must match</option>
        <option value="any">Any rule must match</option>
      </select>

      <div className="space-y-2">
        <Label name="questionConfig.rules" className="label py-0.5">
          <span className="label-text text-xs">Rules</span>
        </Label>
        {ruleFields.map((rule, ruleIndex) => (
          <div key={rule.id} className="mb-1 flex items-center space-x-1">
            {questionType === 'MULTIPLE_CHOICE' && choices?.length ? (
              <>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.value`)}
                  className="select select-xs select-bordered w-1/3"
                >
                  {choices.map((choice, choiceIndex) => (
                    <option key={choiceIndex} value={choice}>
                      {choice}
                    </option>
                  ))}
                </select>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.operation`)}
                  className="select select-xs select-bordered w-1/3"
                >
                  {multipleChoiceOperations.map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                </select>
              </>
            ) : questionType === 'RATING' || questionType === 'SCALE' ? (
              <>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.operation`)}
                  className="select select-xs select-bordered w-1/3"
                >
                  {ratingScaleOperations.map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                </select>
                <TextField
                  {...register(`questionConfig.rules.${ruleIndex}.value`)}
                  placeholder="Value"
                  onKeyPress={(e) => {
                    if (!/[0-9]/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  className="input input-xs input-bordered w-1/2"
                />
              </>
            ) : questionType === 'LIKE_DISLIKE' && choices?.length ? (
              <>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.value`)}
                  className="select select-xs select-bordered w-1/3"
                >
                  {choices.map((choice, choiceIndex) => (
                    <option key={choiceIndex} value={choice}>
                      {choice}
                    </option>
                  ))}
                </select>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.operation`)}
                  className="select select-xs select-bordered w-1/3"
                >
                  {likeDislikeOperations.map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                </select>
              </>
            ) : (
              <>
                <select
                  {...register(`questionConfig.rules.${ruleIndex}.operation`)}
                  className="select select-xs select-bordered w-1/4"
                >
                  {getRuleOperations(questionType).map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                </select>
                {watchRules[ruleIndex]?.operation !== 'is blank' &&
                  watchRules[ruleIndex]?.operation !== 'is not blank' && (
                    <TextField
                      {...register(`questionConfig.rules.${ruleIndex}.value`)}
                      placeholder="Value"
                      className="input input-xs input-bordered w-1/2"
                    />
                  )}
              </>
            )}
            <button
              type="button"
              className="btn btn-error btn-xs"
              onClick={() => removeRule(ruleIndex)}
            >
              Remove
            </button>
          </div>
        ))}
        <button
          type="button"
          className="btn btn-primary btn-outline btn-xs"
          onClick={() => appendRule({ operation: 'is', value: '' })}
        >
          <span className="text-xs">Add Rule</span>
        </button>
      </div>

      <div className="space-y-2">
        <Label name="questionConfig.actions" className="label py-0.5">
          <span className="label-text text-xs">Actions</span>
        </Label>
        {actionFields.map((action, actionIndex) => (
          <div key={action.id} className="mb-1 flex items-center space-x-1">
            <select
              {...register(`questionConfig.actions.${actionIndex}.type`)}
              className="select select-xs select-bordered w-1/3"
            >
              {actionTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {(watchActions[actionIndex]?.type === 'skip' ||
              watchActions[actionIndex]?.type === 'go to') && (
                <>
                  <TextField
                    {...register(
                      `questionConfig.actions.${actionIndex}.questionNumber`,
                      {
                        validate: (value) => {
                          const number = parseInt(value, 10)
                          return (
                            (number > currentQuestionNumber &&
                              number <= questionCount) ||
                            'Invalid question number'
                          )
                        },
                      }
                    )}
                    type="number"
                    placeholder="Question Number"
                    className="input input-xs input-bordered w-1/4"
                  />
                  {errors?.questionConfig?.actions?.[actionIndex]?.questionNumber && (
                    <span className="text-xs text-error">
                      {errors.questionConfig.actions[actionIndex].questionNumber.message}
                    </span>
                  )}
                </>
              )}
            <button
              type="button"
              className="btn btn-error btn-xs"
              onClick={() => removeAction(actionIndex)}
            >
              Remove
            </button>
          </div>
        ))}
        <button
          type="button"
          className="btn btn-primary btn-outline btn-xs"
          onClick={() => appendAction({ type: 'skip', questionNumber: '' })}
        >
          <span className="text-xs">Add Action</span>
        </button>
      </div>
    </div>
  )
}

export default RuleActions
