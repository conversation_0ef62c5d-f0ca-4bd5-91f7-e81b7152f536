import { useFormContext } from 'react-hook-form'

import { Label, TextField } from '@redwoodjs/forms'

const EssayQuestion = ({ question }) => {
  const {
    register,
    formState: { errors },
  } = useFormContext()

  return (
    <div className="space-y-2">
      <div className="flex space-x-2">
        <div className="w-1/2">
          <Label name="questionConfig.minLength" className="label py-1">
            <span className="label-text text-xs">Minimum Length</span>
          </Label>
          <TextField
            name="questionConfig.minLength"
            defaultValue={question?.questionConfig?.minLength}
            validation={{
              valueAsNumber: true,
              min: { value: 1, message: 'Minimum length must be at least 1' },
            }}
            {...register('questionConfig.minLength', {
              valueAsNumber: true,
              min: { value: 1, message: 'Minimum length must be at least 1' },
            })}
            type="number"
            placeholder="Enter min length"
            className="input input-xs input-bordered w-full"
          />
          {errors?.questionConfig?.minLength && (
            <span className="mt-0.5 text-xs text-error">
              {errors.questionConfig.minLength.message}
            </span>
          )}
        </div>
        <div className="w-1/2">
          <Label name="questionConfig.maxLength" className="label py-1">
            <span className="label-text text-xs">Maximum Length</span>
          </Label>
          <TextField
            name="questionConfig.maxLength"
            defaultValue={question?.questionConfig?.maxLength}
            validation={{
              valueAsNumber: true,
              min: { value: 1, message: 'Maximum length must be at least 1' },
            }}
            {...register('questionConfig.maxLength', {
              valueAsNumber: true,
              min: { value: 1, message: 'Maximum length must be at least 1' },
            })}
            type="number"
            placeholder="Enter max length"
            className="input input-xs input-bordered w-full"
          />
          {errors?.questionConfig?.maxLength && (
            <span className="mt-0.5 text-xs text-error">
              {errors.questionConfig.maxLength.message}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default EssayQuestion
