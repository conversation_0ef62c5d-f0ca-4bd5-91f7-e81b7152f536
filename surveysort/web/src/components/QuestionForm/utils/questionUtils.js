export const formatQuestionConfig = (config = {}, formConfig = {}) => {
  const formatted = {
    ...config,
    choices: formConfig?.choices || config?.choices || [],
    rating: formConfig?.rating ? parseInt(formConfig?.rating) : config?.rating,
    scale: formConfig?.scale ? parseInt(formConfig?.scale) : config?.scale,
    minLength: formConfig?.minLength ? parseInt(formConfig?.minLength) : config?.minLength,
    maxLength: formConfig?.maxLength ? parseInt(formConfig?.maxLength) : config?.maxLength,
    rows: formConfig?.rows || config?.rows || [],
    columns: formConfig?.columns || config?.columns || [],
    cellType: formConfig?.cellType || config?.cellType,
    allowMultiple: formConfig?.allowMultiple || false,
    allowWriteIn: formConfig?.allowWriteIn || false,
    includeTime: formConfig?.includeTime || false,
    requireName: formConfig?.requireName || false,
    requireEmail: formConfig?.requireEmail || false,
    requirePhone: formConfig?.requirePhone || false,
    requireCompany: formConfig?.requireCompany || false,
    requireAddress: formConfig?.requireAddress || false,
    requireWebsite: formConfig?.requireWebsite || false,
    rules: formConfig?.rules || config?.rules || [],
    condition: formConfig?.condition || config?.condition || 'all',
    actions: formConfig?.actions || config?.actions || [],
    // Preserve randomization fields
    randomize: Boolean(formConfig?.randomize),
    randomizeChoices: Array.isArray(formConfig?.randomizeChoices) ? formConfig?.randomizeChoices : [],
    randomizeRows: Array.isArray(formConfig?.randomizeRows) ? formConfig?.randomizeRows : [],
  }

  return formatted
}

export const cleanupConfig = (config) => {
  const cleanConfig = { ...config }
  Object.keys(cleanConfig).forEach(key => {
    // Always keep randomization fields
    if (key.startsWith('randomize')) return

    if (cleanConfig[key] === undefined || cleanConfig[key] === null) {
      delete cleanConfig[key]
    }
    if (key.startsWith('rules')) {
      cleanConfig.rules.forEach(rule => {
        delete rule['__typename'];
      });
    }
    if (key.startsWith('actions')) {
      cleanConfig.actions.forEach(action => {
        delete action['__typename'];
      });
    }
  })

  return cleanConfig
}
