import { useState, useEffect } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { Form, Label, TextAreaField, Submit } from '@redwoodjs/forms'

export const QUERY = gql`
  query FullSurveySettingsQuery($surveyId: String!) {
    surveySettings(surveyId: $surveyId) {
      id
      surveyId
      disclosure
      customTheme
      requireEmail
      showProgressBar
    }
  }
`

const UPDATE_SURVEY_SETTINGS_MUTATION = gql`
  mutation UpdateFullSurveySettingsMutation($surveyId: String!, $input: UpdateSurveySettingsInput!) {
    updateSurveySettings(surveyId: $surveyId, input: $input) {
      id
      surveyId
      disclosure
      customTheme
      requireEmail
      showProgressBar
    }
  }
`

export const Loading = () => <div className="loading loading-spinner loading-lg"></div>

export const Empty = ({ surveyId }) => <SettingsForm surveyId={surveyId} initialSettings={{}} />

export const Failure = ({ error }) => (
  <div className="alert alert-error">{error?.message}</div>
)

export const Success = ({ surveySettings, surveyId }) => (
  <SettingsForm surveyId={surveyId} initialSettings={surveySettings} />
)

const SettingsForm = ({ surveyId, initialSettings }) => {
  const [updateSurveySettings] = useMutation(UPDATE_SURVEY_SETTINGS_MUTATION, {
    onCompleted: () => {
      toast.success('Survey settings updated successfully')
    },
    onError: (error) => {
      toast.error(`Failed to update survey settings: ${error.message}`)
    },
  })

  const [settings, setSettings] = useState(initialSettings)

  useEffect(() => {
    setSettings(initialSettings)
  }, [initialSettings])

  const onSubmit = async () => {
    try {
      const result = await updateSurveySettings({
        variables: {
          surveyId,
          input: {
            disclosure: settings.disclosure,
            requireEmail: settings.requireEmail,
            showProgressBar: settings.showProgressBar,
          },
        },
      })
      setSettings(result.data.updateSurveySettings)
    } catch (error) {
      console.error('Error updating survey settings:', error)
    }
  }

  const handleToggle = (field) => {
    setSettings((prev) => ({ ...prev, [field]: !prev[field] }))
  }

  const handleDisclosureChange = (e) => {
    setSettings((prev) => ({ ...prev, disclosure: e.target.value }))
  }

  return (
    <div className="bg-base-100">
      <div className="p-4">
        <h2 className="text-lg font-bold mb-4">Settings</h2>
        <Form onSubmit={onSubmit}>
          <div className="form-control">
            <Label name="disclosure" className="label">
              <span className="label-text">Disclosure Text</span>
            </Label>
            <TextAreaField
              name="disclosure"
              value={settings.disclosure || ''}
              onChange={handleDisclosureChange}
              className="textarea textarea-bordered h-32"
            />
          </div>


          <div className="form-control">
            <Label className="label cursor-pointer">
              <span className="label-text">Require Email</span>
              <input
                type="checkbox"
                name="requireEmail"
                className="toggle toggle-primary"
                checked={settings.requireEmail || false}
                onChange={() => handleToggle('requireEmail')}
              />
            </Label>
          </div>

          <div className="form-control">
            <Label className="label cursor-pointer">
              <span className="label-text">Show Progress Bar</span>
              <input
                type="checkbox"
                name="showProgressBar"
                className="toggle toggle-primary"
                checked={settings.showProgressBar || false}
                onChange={() => handleToggle('showProgressBar')}
              />
            </Label>
          </div>

          <div className="form-control mt-6 flex flex-row justify-end">
            <Submit className="btn btn-primary">Update Settings</Submit>
          </div>
        </Form>
      </div>
    </div>
  )
}