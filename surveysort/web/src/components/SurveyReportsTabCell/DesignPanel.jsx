import React, { useState } from 'react'
import { 
  DocumentTextIcon, 
  SwatchIcon,
  DocumentChartBarIcon 
} from '@heroicons/react/24/outline'

import ThemeSelector from './ThemeSelector'
import LogoUploader from './LogoUploader'
import ColorSettings from './ColorSettings'
import SpacingSettings from './SpacingSettings'
import TypographySettings from './TypographySettings'
import GeneralSettingsPanel from './GeneralSettingsPanel'
import ChartSettings from './ChartSettings'

const DesignPanel = ({ config, onConfigChange }) => {
  const [activePanel, setActivePanel] = useState('general')
  
  const handleColorChange = (color, field) => {
    onConfigChange({
      ...config,
      [field]: color,
    })
  }

  const handleInputChange = (field, value) => {
    onConfigChange({
      ...config,
      [field]: value,
    })
  }

  const handleThemeSelect = (theme) => {
    onConfigChange({
      ...config,
      ...theme,
      logo: config.logo, // Preserve logo if already set
      logoPosition: config.logoPosition, // Preserve logo position if already set
      name: config.name, // Preserve report name
      subtitle: config.subtitle, // Preserve report subtitle
    })
  }
  
  const handleTypographyChange = (field, value) => {
    onConfigChange({
      ...config,
      [field]: value,
    })
  }
  
  const handleSpacingChange = (field, value) => {
    onConfigChange({
      ...config,
      [field]: value,
    })
  }
  
  const handleChartConfigChange = (field, value) => {
    onConfigChange({
      ...config,
      chart: {
        ...config.chart,
        [field]: value,
      },
    })
  }

  return (
    <div className="bg-base-100 rounded-r-xl shadow-lg border-l border-base-300 h-full overflow-y-auto">
      <div className="sticky top-0 z-10 bg-base-100 border-b border-base-200 p-4">
        <h2 className="text-lg font-semibold">Report Design</h2>
      </div>

      <div className="p-4 space-y-4">
        {/* Panel navigation */}
        <div className="flex flex-wrap gap-2">
          {[
            { id: 'general', label: 'General', icon: DocumentTextIcon },
            { id: 'branding', label: 'Branding', icon: SwatchIcon },
            { id: 'typography', label: 'Typography', icon: DocumentTextIcon },
            { id: 'charts', label: 'Charts', icon: DocumentChartBarIcon },
          ].map((panel) => (
            <button
              key={panel.id}
              className={`btn btn-sm ${
                activePanel === panel.id ? 'btn-primary' : 'btn-outline'
              }`}
              onClick={() => setActivePanel(panel.id)}
            >
              <panel.icon className="h-4 w-4 mr-1" />
              {panel.label}
            </button>
          ))}
        </div>

        {/* General settings */}
        {activePanel === 'general' && (
          <GeneralSettingsPanel 
            name={config.name}
            subtitle={config.subtitle}
            onInputChange={handleInputChange}
          />
        )}

        {/* Branding settings */}
        {activePanel === 'branding' && (
          <div className="space-y-4">
            <ThemeSelector onSelectTheme={handleThemeSelect} />
            
            <LogoUploader 
              logo={config.logo}
              logoPosition={config.logoPosition}
              onLogoChange={(logo) => handleInputChange('logo', logo)}
              onPositionChange={(position) => handleInputChange('logoPosition', position)}
            />

            <ColorSettings 
              colors={{
                primaryColor: config.primaryColor,
                secondaryColor: config.secondaryColor,
                accentColor: config.accentColor,
                textColor: config.textColor,
                backgroundColor: config.backgroundColor,
                sectionHeaderColor: config.sectionHeaderColor,
                footerTextColor: config.footerTextColor,
              }}
              onColorChange={handleColorChange}
            />
            
            <SpacingSettings
              spacing={{
                pageMargin: config.pageMargin,
                gutterWidth: config.gutterWidth,
                borderRadius: config.borderRadius,
              }}
              onSpacingChange={handleSpacingChange}
            />
          </div>
        )}
        
        {/* Typography settings */}
        {activePanel === 'typography' && (
          <TypographySettings
            typography={{
              headerFontFamily: config.headerFontFamily,
              bodyFontFamily: config.bodyFontFamily,
              h1Size: config.h1Size,
              h2Size: config.h2Size,
              bodySize: config.bodySize,
              captionSize: config.captionSize,
            }}
            onTypographyChange={handleTypographyChange}
          />
        )}
        
        {/* Chart settings */}
        {activePanel === 'charts' && (
          <ChartSettings
            chartConfig={config.chart}
            onChartConfigChange={handleChartConfigChange}
          />
        )}
      </div>
    </div>
  )
}

export default DesignPanel 