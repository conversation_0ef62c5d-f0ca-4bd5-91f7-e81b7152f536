import { motion } from 'framer-motion'
import {
  ChevronRightIcon,
  ChevronDownIcon,
  ClockIcon,
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'

import ReportStatusBadge from './ReportStatusBadge'
import ReportTypeIcon from './ReportTypeIcon'
import ActionsMenu from './ActionsMenu'

// Report row component
const ReportRow = ({ report, isExpanded, onToggleExpand, onDelete, onDownload }) => {
  return (
    <motion.tr
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="hover cursor-pointer border-b border-base-200"
      onClick={() => onToggleExpand(report.id)}
    >
      <td className="py-3">
        <button className="btn btn-ghost btn-xs">
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4" />
          ) : (
            <ChevronRightIcon className="h-4 w-4" />
          )}
        </button>
      </td>
      <td className="font-medium py-3">{report.name}</td>
      <td className="py-3">
        <div className="flex items-center gap-1.5">
          <ReportTypeIcon type={report.type} />
          <span className="capitalize">
            {report.type.replace(/_/g, ' ')}
          </span>
        </div>
      </td>
      <td className="py-3">
        <ReportStatusBadge status={report.status} />
      </td>
      <td className="py-3">
        <div className="tooltip" data-tip={new Date(report.createdAt).toLocaleString()}>
          <div className="flex items-center gap-1 text-sm text-base-content/70">
            <ClockIcon className="h-3.5 w-3.5" />
            {formatDistanceToNow(new Date(report.createdAt), { addSuffix: true })}
          </div>
        </div>
      </td>
      <td className="py-3 text-right">
        <ActionsMenu 
          report={report} 
          onDelete={onDelete} 
          onDownload={onDownload} 
        />
      </td>
    </motion.tr>
  );
};

export default ReportRow; 