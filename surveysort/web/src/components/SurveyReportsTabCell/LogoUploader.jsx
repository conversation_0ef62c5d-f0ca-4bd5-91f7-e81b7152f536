import React from 'react'
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline'

const LogoUploader = ({ logo, logoPosition, onLogoChange, onPositionChange }) => {
  const handleLogoUpload = (e) => {
    const file = e.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onloadend = () => {
      onLogoChange(reader.result)
    }
    reader.readAsDataURL(file)
  }

  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base">Logo</h3>
        {logo ? (
          <div className="relative">
            <img 
              src={logo} 
              alt="Logo" 
              className="max-h-24 max-w-full object-contain border border-base-300 rounded-lg bg-white p-2" 
            />
            <button 
              className="absolute top-1 right-1 btn btn-xs btn-circle" 
              onClick={() => onLogoChange(null)}
            >
              <XMarkIcon className="h-3 w-3" />
            </button>
          </div>
        ) : (
          <div className="flex items-center justify-center border border-dashed border-base-300 rounded-lg py-8 bg-white/50">
            <label className="btn btn-outline btn-sm">
              <PhotoIcon className="h-4 w-4 mr-1" />
              Upload Logo
              <input
                type="file"
                onChange={handleLogoUpload}
                accept="image/*"
                className="hidden"
              />
            </label>
          </div>
        )}
        <div className="form-control">
          <label className="label">
            <span className="label-text">Logo Position</span>
          </label>
          <select
            className="select select-bordered w-full"
            value={logoPosition}
            onChange={(e) => onPositionChange(e.target.value)}
          >
            <option value="top-left">Top Left</option>
            <option value="top-center">Top Center</option>
            <option value="top-right">Top Right</option>
          </select>
        </div>
      </div>
    </div>
  )
}

export default LogoUploader 