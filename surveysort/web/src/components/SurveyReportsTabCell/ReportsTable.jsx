import { useState, Fragment } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronRightIcon,
  ChevronDownIcon,
  DocumentArrowDownIcon,
  DocumentMagnifyingGlassIcon,
  ClockIcon,
  TrashIcon,
  EllipsisVerticalIcon,
  ArrowPathIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'

import { EmptyState } from './EmptyState'

// Status badge component for displaying report status
const ReportStatusBadge = ({ status }) => {
  const statusConfig = {
    COMPLETED: {
      color: 'bg-success/10 text-success ring-success/20',
      label: 'Ready',
    },
    PROCESSING: {
      color: 'bg-info/10 text-info ring-info/20',
      label: 'Processing',
    },
    SUBMITTED: {
      color: 'bg-warning/10 text-warning ring-warning/20',
      label: 'Submitted',
    },
    FAILED: {
      color: 'bg-error/10 text-error ring-error/20',
      label: 'Failed',
    },
  }

  const config = statusConfig[status] || {
    color: 'bg-base-300 text-base-content ring-base-300/20',
    label: status,
  }

  return (
    <span
      className={`inline-flex items-center rounded-md px-1.5 py-0.5 text-[10px] font-medium ring-1 ring-inset ${config.color}`}
    >
      {config.label}
    </span>
  )
}

// Report type icon component
const ReportTypeIcon = ({ type }) => {
  const iconMap = {
    top_line: DocumentArrowDownIcon,
    // Add more report types as needed
  }

  const Icon = iconMap[type] || DocumentArrowDownIcon

  return <Icon className="h-4 w-4" />
}

// Download handler function
const useReportDownloader = () => {
  return (url) => {
    if (!url) return;
    
    try {
      // Create an anchor element to handle the download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'report.pdf'; // Suggested filename
      a.rel = 'noopener noreferrer';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
      }, 100);
    } catch (error) {
      console.error('Error downloading file:', error);
      // Fallback to window.open
      window.open(url, '_blank');
    }
  };
};

// Status message component
const StatusMessage = ({ status }) => {
  if (status === 'PROCESSING') {
    return (
      <span className="ml-2 text-info text-xs flex items-center">
        <ArrowPathIcon className="h-3 w-3 mr-1 animate-spin" />
        Processing your report...
      </span>
    );
  }
  
  if (status === 'FAILED') {
    return (
      <span className="ml-2 text-error text-xs flex items-center">
        <ExclamationCircleIcon className="h-3 w-3 mr-1" />
        Generation failed
      </span>
    );
  }
  
  return null;
};

// Actions menu component
const ActionsMenu = ({ report, onDelete, onDownload }) => {
  const handleDeleteReport = (e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(report.id, e);
    }
  };
  
  // For completed reports with download URL, show download button
  if (report.status === 'COMPLETED' && report.downloadUrl) {
    return (
      <button 
        className="btn btn-primary btn-xs"
        onClick={(e) => {
          e.stopPropagation();
          onDownload(report.downloadUrl);
        }}
      >
        <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
        Download
      </button>
    );
  }
  
  // Otherwise show dropdown menu with appropriate options
  return (
    <div className="dropdown dropdown-end">
      <button 
        className="btn btn-ghost btn-xs"
        onClick={(e) => e.stopPropagation()}
      >
        <EllipsisVerticalIcon className="h-4 w-4" />
      </button>
      <ul className="dropdown-content menu p-2 shadow-lg bg-base-100 rounded-box w-40 z-50">
        {report.status === 'PROCESSING' && (
          <li>
            <div className="flex items-center gap-1 text-info py-2">
              <ArrowPathIcon className="h-4 w-4 animate-spin" />
              Processing
            </div>
          </li>
        )}
        {report.status === 'FAILED' && (
          <li>
            <div className="flex items-center gap-1 text-error py-2">
              <ExclamationCircleIcon className="h-4 w-4" />
              Failed
            </div>
          </li>
        )}
        <li>
          <button 
            className="text-error py-2"
            onClick={handleDeleteReport}
          >
            <TrashIcon className="h-4 w-4" />
            Delete
          </button>
        </li>
      </ul>
    </div>
  );
};

// Report row component
const ReportRow = ({ report, isExpanded, onToggleExpand, onDelete, onDownload }) => {
  return (
    <motion.tr
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      className="hover cursor-pointer border-b border-base-200"
      onClick={() => onToggleExpand(report.id)}
    >
      <td className="py-3">
        <button className="btn btn-ghost btn-xs">
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4" />
          ) : (
            <ChevronRightIcon className="h-4 w-4" />
          )}
        </button>
      </td>
      <td className="font-medium py-3">{report.name}</td>
      <td className="py-3">
        <div className="flex items-center gap-1.5">
          <ReportTypeIcon type={report.type} />
          <span className="capitalize">
            {report.type.replace(/_/g, ' ')}
          </span>
        </div>
      </td>
      <td className="py-3">
        <ReportStatusBadge status={report.status} />
      </td>
      <td className="py-3">
        <div className="tooltip" data-tip={new Date(report.createdAt).toLocaleString()}>
          <div className="flex items-center gap-1 text-sm text-base-content/70">
            <ClockIcon className="h-3.5 w-3.5" />
            {formatDistanceToNow(new Date(report.createdAt), { addSuffix: true })}
          </div>
        </div>
      </td>
      <td className="py-3 text-right">
        <ActionsMenu 
          report={report} 
          onDelete={onDelete} 
          onDownload={onDownload} 
        />
      </td>
    </motion.tr>
  );
};

// Expanded detail view component
const ExpandedReportDetails = ({ report, onDownload }) => {
  return (
    <motion.tr
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.2 }}
    >
      <td colSpan={6} className="p-0 border-0 bg-base-200/20">
        <div className="p-5 text-sm">
          <dl className="grid grid-cols-2 gap-5">
            <div>
              <dt className="font-medium mb-1.5">Created By</dt>
              <dd className="text-base-content/70">{report.createdBy}</dd>
            </div>
            <div>
              <dt className="font-medium mb-1.5">Report Type</dt>
              <dd className="text-base-content/70 capitalize">{report.type.replace(/_/g, ' ')}</dd>
            </div>
            <div>
              <dt className="font-medium mb-1.5">Created On</dt>
              <dd className="text-base-content/70">{new Date(report.createdAt).toLocaleString()}</dd>
            </div>
            <div>
              <dt className="font-medium mb-1.5">Status</dt>
              <dd className="flex items-center">
                <ReportStatusBadge status={report.status} />
                <StatusMessage status={report.status} />
              </dd>
            </div>
            {report.subtitle && (
              <div className="col-span-2">
                <dt className="font-medium mb-1.5">Subtitle</dt>
                <dd className="text-base-content/70">{report.subtitle}</dd>
              </div>
            )}
            {report.status === 'COMPLETED' && report.downloadUrl && (
              <div className="col-span-2 mt-3">
                <button 
                  className="btn btn-primary btn-sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownload(report.downloadUrl);
                  }}
                >
                  <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                  Download Report
                </button>
                <p className="text-xs text-base-content/70 mt-2">
                  This download link will expire after 72 hours.
                </p>
              </div>
            )}
          </dl>
        </div>
      </td>
    </motion.tr>
  );
};

// Empty state component
const EmptyReportsState = () => (
  <div className="rounded-lg border border-base-300 bg-base-100/30 backdrop-blur-md p-6">
    <EmptyState
      message="No reports found"
      description="Create your first report to share survey insights with your team or stakeholders"
      icon={DocumentMagnifyingGlassIcon}
    />
  </div>
);

// Main reports table component
const ReportsTable = ({ reports, onDelete }) => {
  const [expandedId, setExpandedId] = useState(null);
  const handleDownload = useReportDownloader();

  if (!reports?.length) {
    return <EmptyReportsState />;
  }

  const toggleExpand = (id) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const handleDeleteReport = (id, e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };

  return (
    <div className="overflow-x-auto rounded-lg border border-base-300 bg-base-100/30 backdrop-blur-md">
      <table className="table w-full">
        <thead>
          <tr className="bg-base-200/50">
            <th className="w-1/12 py-3"></th>
            <th className="w-3/12 py-3">Name</th>
            <th className="w-2/12 py-3">Type</th>
            <th className="w-2/12 py-3">Status</th>
            <th className="w-3/12 py-3">Created</th>
            <th className="w-1/12 py-3 text-right pr-4">Actions</th>
          </tr>
        </thead>
        <tbody>
          <AnimatePresence>
            {reports.map((report) => (
              <Fragment key={report.id}>
                <ReportRow 
                  report={report}
                  isExpanded={expandedId === report.id}
                  onToggleExpand={toggleExpand}
                  onDelete={handleDeleteReport}
                  onDownload={handleDownload}
                />
                
                <AnimatePresence>
                  {expandedId === report.id && (
                    <ExpandedReportDetails 
                      report={report} 
                      onDownload={handleDownload}
                    />
                  )}
                </AnimatePresence>
              </Fragment>
            ))}
          </AnimatePresence>
        </tbody>
      </table>
    </div>
  );
};

export default ReportsTable; 