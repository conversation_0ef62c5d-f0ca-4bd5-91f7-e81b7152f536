import React from 'react'

const TypographySettings = ({ typography, onTypographyChange }) => {
  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base">Typography</h3>
        
        <div className="form-control w-full mb-2">
          <label className="label">
            <span className="label-text">Header Font Family</span>
          </label>
          <select
            className="select select-bordered w-full"
            value={typography.headerFontFamily}
            onChange={(e) => onTypographyChange('headerFontFamily', e.target.value)}
          >
            <option value="Inter, sans-serif">Inter</option>
            <option value="Montserrat, sans-serif">Montserrat</option>
            <option value="Poppins, sans-serif">Poppins</option>
            <option value="Lato, sans-serif">Lato</option>
            <option value="Playfair Display, serif">Playfair Display</option>
            <option value="Arial, sans-serif">Arial</option>
          </select>
        </div>
        
        <div className="form-control w-full mb-2">
          <label className="label">
            <span className="label-text">Body Font Family</span>
          </label>
          <select
            className="select select-bordered w-full"
            value={typography.bodyFontFamily}
            onChange={(e) => onTypographyChange('bodyFontFamily', e.target.value)}
          >
            <option value="Roboto, sans-serif">Roboto</option>
            <option value="Open Sans, sans-serif">Open Sans</option>
            <option value="Helvetica Neue, sans-serif">Helvetica</option>
            <option value="Arial, sans-serif">Arial</option>
            <option value="Georgia, serif">Georgia</option>
          </select>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">H1 Size</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={typography.h1Size}
              onChange={(e) => onTypographyChange('h1Size', e.target.value)}
            >
              <option value="28px">Small (28px)</option>
              <option value="32px">Medium (32px)</option>
              <option value="36px">Large (36px)</option>
            </select>
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">H2 Size</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={typography.h2Size}
              onChange={(e) => onTypographyChange('h2Size', e.target.value)}
            >
              <option value="22px">Small (22px)</option>
              <option value="24px">Medium (24px)</option>
              <option value="28px">Large (28px)</option>
            </select>
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Body Size</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={typography.bodySize}
              onChange={(e) => onTypographyChange('bodySize', e.target.value)}
            >
              <option value="12px">Small (12px)</option>
              <option value="14px">Medium (14px)</option>
              <option value="16px">Large (16px)</option>
            </select>
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Caption Size</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={typography.captionSize}
              onChange={(e) => onTypographyChange('captionSize', e.target.value)}
            >
              <option value="10px">Small (10px)</option>
              <option value="12px">Medium (12px)</option>
              <option value="14px">Large (14px)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TypographySettings 