import { 
  ArrowPathIcon,
  ExclamationCircleIcon 
} from '@heroicons/react/24/outline'

// Status message component with appropriate icons
const StatusMessage = ({ status }) => {
  if (status === 'PROCESSING') {
    return (
      <span className="ml-2 text-info text-xs flex items-center">
        <ArrowPathIcon className="h-3 w-3 mr-1 animate-spin" />
        Processing your report...
      </span>
    );
  }
  
  if (status === 'FAILED') {
    return (
      <span className="ml-2 text-error text-xs flex items-center">
        <ExclamationCircleIcon className="h-3 w-3 mr-1" />
        Generation failed
      </span>
    );
  }
  
  return null;
};

export default StatusMessage; 