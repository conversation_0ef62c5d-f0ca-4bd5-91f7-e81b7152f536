import React from 'react'

const SpacingSettings = ({ spacing, onSpacingChange }) => {
  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base">Layout & Spacing</h3>
        
        <div className="grid grid-cols-2 gap-3">
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Page Margin</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={spacing.pageMargin}
              onChange={(e) => onSpacingChange('pageMargin', e.target.value)}
            >
              <option value="0.75in">Narrow (0.75in)</option>
              <option value="1in">Standard (1in)</option>
              <option value="1.25in">Wide (1.25in)</option>
            </select>
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Gutter Width</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={spacing.gutterWidth}
              onChange={(e) => onSpacingChange('gutterWidth', e.target.value)}
            >
              <option value="12px">Narrow (12px)</option>
              <option value="16px">Standard (16px)</option>
              <option value="20px">Wide (20px)</option>
            </select>
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text">Border Radius</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={spacing.borderRadius}
              onChange={(e) => onSpacingChange('borderRadius', e.target.value)}
            >
              <option value="0px">Square (0px)</option>
              <option value="4px">Subtle (4px)</option>
              <option value="8px">Rounded (8px)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SpacingSettings 