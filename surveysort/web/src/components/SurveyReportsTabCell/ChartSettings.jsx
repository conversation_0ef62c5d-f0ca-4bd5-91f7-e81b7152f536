import React, { useState } from 'react'
import { PaintBrushIcon } from '@heroicons/react/24/outline'
import { ChromePicker } from 'react-color'

const ChartSettings = ({ chartConfig, onChartConfigChange }) => {
  const [showColorPicker, setShowColorPicker] = useState(null)
  
  // Helper function to determine text color based on background
  const getContrastColor = (hexColor) => {
    if (!hexColor) return '#000000';
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base">Chart Colors</h3>
        <div className="grid grid-cols-2 gap-3">
          {[
            { key: 'barFill', label: 'Bar Color' },
            { key: 'lineStroke', label: 'Line Color' },
            { key: 'markerFill', label: 'Marker Color' },
            { key: 'axisColor', label: 'Axis Color' },
            { key: 'gridColor', label: 'Grid Color' },
          ].map(({ key, label }) => (
            <div key={key} className="relative">
              <label className="label">
                <span className="label-text">{label}</span>
              </label>
              <div className="flex gap-2">
                <button
                  className="w-full h-8 rounded-md border shadow-sm flex items-center justify-between px-2 hover:ring-2 hover:ring-primary/20 transition-all"
                  style={{ 
                    backgroundColor: chartConfig[key] || '#ffffff',
                    color: getContrastColor(chartConfig[key] || '#ffffff')
                  }}
                  onClick={() => setShowColorPicker(showColorPicker === key ? null : key)}
                >
                  <span>{chartConfig[key] || '#FFFFFF'}</span>
                  <PaintBrushIcon className="h-3.5 w-3.5" />
                </button>
              </div>
              {showColorPicker === key && (
                <div className="absolute z-20 mt-1">
                  <div 
                    className="fixed inset-0" 
                    onClick={() => setShowColorPicker(null)}
                  ></div>
                  <div className="relative">
                    <ChromePicker
                      color={chartConfig[key] || '#ffffff'}
                      onChange={(color) => onChartConfigChange(key, color.hex)}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ChartSettings 