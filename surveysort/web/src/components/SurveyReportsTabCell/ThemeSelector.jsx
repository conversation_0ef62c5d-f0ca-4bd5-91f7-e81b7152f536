import React from 'react'

// Predefined themes with comprehensive configuration
const PRESET_THEMES = [
  {
    name: 'Professional Blue',
    primaryColor: '#2563eb',
    secondaryColor: '#60a5fa',
    accentColor: '#1e40af',
    textColor: '#111827',
    backgroundColor: '#ffffff',
    sectionHeaderColor: '#2563eb',
    footerTextColor: '#6b7280',

    headerFontFamily: 'Inter, sans-serif',
    bodyFontFamily: 'Roboto, sans-serif',
    h1Size: '32px',
    h2Size: '24px',
    bodySize: '14px',
    captionSize: '12px',

    pageMargin: '1in',
    gutterWidth: '16px',
    borderRadius: '4px',

    chart: {
      axisColor: '#4b5563',
      gridColor: '#e5e7eb',
      barFill: '#2563eb',
      lineStroke: '#1e40af',
      markerFill: '#60a5fa',
    }
  },
  {
    name: 'Modern Slate',
    primaryColor: '#475569',
    secondaryColor: '#94a3b8',
    accentColor: '#334155',
    textColor: '#0f172a',
    backgroundColor: '#f9fafb',
    sectionHeaderColor: '#334155',
    footerTextColor: '#6b7280',

    headerFontFamily: 'Montserrat, sans-serif',
    bodyFontFamily: 'Open Sans, sans-serif',
    h1Size: '30px',
    h2Size: '22px',
    bodySize: '14px',
    captionSize: '11px',

    pageMargin: '0.75in',
    gutterWidth: '12px',
    borderRadius: '6px',

    chart: {
      axisColor: '#374151',
      gridColor: '#d1d5db',
      barFill: '#475569',
      lineStroke: '#334155',
      markerFill: '#94a3b8',
    }
  },
  {
    name: 'Corporate Green',
    primaryColor: '#059669',
    secondaryColor: '#34d399',
    accentColor: '#047857',
    textColor: '#1f2937',
    backgroundColor: '#ffffff',
    sectionHeaderColor: '#059669',
    footerTextColor: '#4b5563',

    headerFontFamily: 'Lato, sans-serif',
    bodyFontFamily: 'Helvetica Neue, sans-serif',
    h1Size: '34px',
    h2Size: '26px',
    bodySize: '15px',
    captionSize: '12px',

    pageMargin: '1in',
    gutterWidth: '20px',
    borderRadius: '5px',

    chart: {
      axisColor: '#065f46',
      gridColor: '#d1fae5',
      barFill: '#059669',
      lineStroke: '#047857',
      markerFill: '#34d399',
    }
  },
  {
    name: 'Vibrant Purple',
    primaryColor: '#7c3aed',
    secondaryColor: '#a78bfa',
    accentColor: '#6d28d9',
    textColor: '#111827',
    backgroundColor: '#ffffff',
    sectionHeaderColor: '#7c3aed',
    footerTextColor: '#6b7280',

    headerFontFamily: 'Poppins, sans-serif',
    bodyFontFamily: 'Arial, sans-serif',
    h1Size: '32px',
    h2Size: '24px',
    bodySize: '14px',
    captionSize: '12px',

    pageMargin: '0.9in',
    gutterWidth: '14px',
    borderRadius: '4px',

    chart: {
      axisColor: '#5b21b6',
      gridColor: '#ede9fe',
      barFill: '#7c3aed',
      lineStroke: '#6d28d9',
      markerFill: '#a78bfa',
    }
  },
  {
    name: 'Elegant Gold',
    primaryColor: '#b45309',
    secondaryColor: '#fbbf24',
    accentColor: '#92400e',
    textColor: '#1f2937',
    backgroundColor: '#ffffff',
    sectionHeaderColor: '#b45309',
    footerTextColor: '#4b5563',

    headerFontFamily: 'Playfair Display, serif',
    bodyFontFamily: 'Georgia, serif',
    h1Size: '36px',
    h2Size: '28px',
    bodySize: '16px',
    captionSize: '13px',

    pageMargin: '1in',
    gutterWidth: '18px',
    borderRadius: '6px',

    chart: {
      axisColor: '#92400e',
      gridColor: '#fef3c7',
      barFill: '#b45309',
      lineStroke: '#92400e',
      markerFill: '#fbbf24',
    }
  }
]

// Theme selector component
const ThemeSelector = ({ onSelectTheme }) => {
  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base mb-3">Report Themes</h3>
        <div className="grid grid-cols-1 gap-3">
          {PRESET_THEMES.map((theme) => (
            <button
              key={theme.name}
              className="btn btn-outline btn-sm justify-start normal-case h-auto py-2"
              onClick={() => onSelectTheme(theme)}
            >
              <div className="flex items-center gap-2">
                <div className="flex -space-x-1">
                  {[theme.primaryColor, theme.secondaryColor, theme.accentColor].map((color, i) => (
                    <div
                      key={i}
                      className="w-4 h-4 rounded-full border border-white/80 shadow-sm"
                      style={{ backgroundColor: color, zIndex: 3 - i }}
                    />
                  ))}
                </div>
                <span>{theme.name}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export { PRESET_THEMES }
export default ThemeSelector 