// Status badge component for displaying report status
const ReportStatusBadge = ({ status }) => {
  const statusConfig = {
    COMPLETED: {
      color: 'bg-success/10 text-success ring-success/20',
      label: 'Ready',
    },
    PROCESSING: {
      color: 'bg-info/10 text-info ring-info/20',
      label: 'Processing',
    },
    SUBMITTED: {
      color: 'bg-warning/10 text-warning ring-warning/20',
      label: 'Submitted',
    },
    FAILED: {
      color: 'bg-error/10 text-error ring-error/20',
      label: 'Failed',
    },
  }

  const config = statusConfig[status] || {
    color: 'bg-base-300 text-base-content ring-base-300/20',
    label: status,
  }

  return (
    <span
      className={`inline-flex items-center rounded-md px-1.5 py-0.5 text-[10px] font-medium ring-1 ring-inset ${config.color}`}
    >
      {config.label}
    </span>
  )
}

export default ReportStatusBadge; 