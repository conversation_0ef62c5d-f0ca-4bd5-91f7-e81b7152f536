import React, { useState } from 'react'
import { 
  ArrowLeftIcon,
  CheckIcon,
  DocumentTextIcon,
  DocumentChartBarIcon
} from '@heroicons/react/24/outline'

import DesignPanel from './DesignPanel'
import PdfPreview from './PdfPreview'


// Main component
const ToplineReportDesigner = ({ surveyId, onSave, onCancel }) => {
  const [config, setConfig] = useState({
    name: 'Topline Survey Report',
    subtitle: '',
    primaryColor: '#2563eb',
    secondaryColor: '#60a5fa',
    accentColor: '#1e40af',
    textColor: '#111827',
    backgroundColor: '#ffffff',
    sectionHeaderColor: '#2563eb',
    footerTextColor: '#6b7280',

    headerFontFamily: 'Inter, sans-serif',
    bodyFontFamily: 'Roboto, sans-serif',
    h1Size: '32px',
    h2Size: '24px',
    bodySize: '14px',
    captionSize: '12px',

    pageMargin: '1in',
    gutterWidth: '16px',
    borderRadius: '4px',

    chart: {
      axisColor: '#4b5563',
      gridColor: '#e5e7eb',
      barFill: '#2563eb',
      lineStroke: '#1e40af',
      markerFill: '#60a5fa',
    },
    
    logo: null,
    logoPosition: 'top-right',
  })

  const handleSave = (format) => {
    const reportData = {
      ...config,
      format
    }
    onSave(reportData)
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b border-base-300">
        <div className="flex items-center gap-2">
          <button 
            className="btn btn-ghost btn-sm"
            onClick={onCancel}
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back
          </button>
          <h2 className="text-lg font-semibold">Create Topline Report</h2>
        </div>
        
        <div className="flex gap-2">
          <button 
            className="btn btn-outline btn-sm"
            onClick={onCancel}
          >
            Cancel
          </button>
          <div className="dropdown dropdown-end">
            <label tabIndex={0} className="btn btn-primary btn-sm">
              <CheckIcon className="h-4 w-4 mr-1" />
              Save & Download
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 ml-1">
                <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
              </svg>
            </label>
            <ul tabIndex={0} className="dropdown-content menu p-1 shadow-lg bg-base-100 rounded-box w-60 z-[9999]">
              <li>
                <button onClick={() => handleSave('pdf')}>
                  <DocumentTextIcon className="h-4 w-4" />
                  <div>
                    <div className="font-medium">PDF Format</div>
                    <span className="text-xs opacity-70">High-quality document</span>
                  </div>
                </button>
              </li>
              <li>
                <button onClick={() => handleSave('excel')}>
                  <DocumentChartBarIcon className="h-4 w-4" />
                  <div>
                    <div className="font-medium">Excel Format</div>
                    <span className="text-xs opacity-70">Editable data tables</span>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div className="flex-1 grid grid-cols-2 overflow-hidden">
        <PdfPreview config={config} />
        <DesignPanel config={config} onConfigChange={setConfig} />
      </div>
    </div>
  )
}

export default ToplineReportDesigner 