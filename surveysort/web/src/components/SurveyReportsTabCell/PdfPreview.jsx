import React from 'react'

const PdfPreview = ({ config }) => {
  return (
    <div className="bg-base-200 p-4 h-full flex flex-col">
      <div className="text-sm text-base-content/70 mb-2">Preview</div>
      <div className="flex-1 rounded-lg bg-white overflow-hidden shadow-lg border border-base-300 relative">
        {/* Cover page preview with updated styling */}
        <div 
          className="absolute inset-0 flex flex-col p-8"
          style={{ backgroundColor: config.backgroundColor || '#ffffff' }}
        >
          {/* Logo */}
          {config.logo && (
            <div className={`mb-4 flex ${
              config.logoPosition === 'top-center' ? 'justify-center' : 
              config.logoPosition === 'top-right' ? 'justify-end' : 'justify-start'
            }`}>
              <img 
                src={config.logo} 
                alt="Company Logo" 
                className="max-h-12 max-w-[160px] object-contain" 
              />
            </div>
          )}
          
          {/* Report title */}
          <div className="mt-16 text-center">
            <h1 
              className="font-bold mb-2"
              style={{ 
                color: config.textColor || '#000000',
                fontFamily: config.headerFontFamily || 'Inter, sans-serif',
                fontSize: config.h1Size || '32px'
              }}
            >
              {config.name || 'Survey Report'}
            </h1>
            {config.subtitle && (
              <h2 
                style={{ 
                  color: config.textColor || '#000000',
                  fontFamily: config.headerFontFamily || 'Inter, sans-serif',
                  fontSize: config.h2Size || '24px'
                }}
              >
                {config.subtitle}
              </h2>
            )}
            <div 
              className="mt-2 h-1 w-24 mx-auto rounded-full" 
              style={{ backgroundColor: config.primaryColor || '#3b82f6' }}
            ></div>
          </div>
          
          {/* Mock section header */}
          <div className="mt-16 mb-2"
               style={{ 
                 fontFamily: config.headerFontFamily || 'Inter, sans-serif',
                 fontSize: config.h2Size || '24px',
                 color: config.sectionHeaderColor || '#2563eb'
               }}>
            Key Findings
          </div>
          
          {/* Mock sections */}
          <div className="mt-4 space-y-4">
            <div className="h-8 rounded-lg w-full" 
                 style={{ 
                   backgroundColor: `${config.secondaryColor}20` || '#60a5fa20',
                   borderRadius: config.borderRadius || '4px'
                 }}></div>
                 
            <div className="flex gap-4">
              <div className="h-32 rounded-lg w-1/2"
                   style={{ 
                     backgroundColor: `${config.primaryColor}10` || '#3b82f610',
                     borderRadius: config.borderRadius || '4px'
                   }}></div>
              <div className="h-32 rounded-lg w-1/2"
                   style={{ 
                     backgroundColor: `${config.accentColor}10` || '#2563eb10',
                     borderRadius: config.borderRadius || '4px'
                   }}></div>
            </div>
            
            <div className="mt-4 h-24 rounded-lg"
                 style={{ 
                   backgroundColor: `${config.secondaryColor}15` || '#60a5fa15',
                   borderRadius: config.borderRadius || '4px'
                 }}></div>
          </div>
          
          {/* Footer */}
          <div className="absolute bottom-4 left-8 right-8 text-center border-t pt-2" 
               style={{ 
                 borderColor: `${config.footerTextColor}30` || '#6b728030',
                 color: config.footerTextColor || '#6b7280',
                 fontFamily: config.bodyFontFamily || 'Roboto, sans-serif',
                 fontSize: config.captionSize || '12px'
               }}>
            <div>Preview Only</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PdfPreview 