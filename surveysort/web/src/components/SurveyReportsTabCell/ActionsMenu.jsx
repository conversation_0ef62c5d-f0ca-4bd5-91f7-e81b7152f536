import {
  DocumentArrowDownIcon,
  ArrowPathIcon,
  ExclamationCircleIcon,
  TrashIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline'

// Actions menu component for report row
const ActionsMenu = ({ report, onDelete, onDownload }) => {
  const handleDeleteReport = (e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(report.id, e);
    }
  };
  
  // For completed reports with download URL, show download button
  if (report.status === 'COMPLETED' && report.downloadUrl) {
    return (
      <button 
        className="btn btn-primary btn-xs"
        onClick={(e) => {
          e.stopPropagation();
          onDownload(report.downloadUrl);
        }}
      >
        <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
        Download
      </button>
    );
  }
  
  // Otherwise show dropdown menu with appropriate options
  return (
    <div className="dropdown dropdown-end">
      <button 
        className="btn btn-ghost btn-xs"
        onClick={(e) => e.stopPropagation()}
      >
        <EllipsisVerticalIcon className="h-4 w-4" />
      </button>
      <ul className="dropdown-content menu p-2 shadow-lg bg-base-100 rounded-box w-40 z-50">
        {report.status === 'PROCESSING' && (
          <li>
            <div className="flex items-center gap-1 text-info py-2">
              <ArrowPathIcon className="h-4 w-4 animate-spin" />
              Processing
            </div>
          </li>
        )}
        {report.status === 'FAILED' && (
          <li>
            <div className="flex items-center gap-1 text-error py-2">
              <ExclamationCircleIcon className="h-4 w-4" />
              Failed
            </div>
          </li>
        )}
        <li>
          <button 
            className="text-error py-2"
            onClick={handleDeleteReport}
          >
            <TrashIcon className="h-4 w-4" />
            Delete
          </button>
        </li>
      </ul>
    </div>
  );
};

export default ActionsMenu; 