import { useState, useEffect } from 'react'
import { navigate, routes } from '@redwoodjs/router'
import { DocumentArrowUpIcon, DocumentChartBarIcon, DocumentTextIcon } from '@heroicons/react/24/outline'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

import ReportsTable from './ReportsTable'
import ToplineReportDesigner from './ToplineReportDesigner'
import { EmptyState } from './EmptyState'

export const QUERY = gql`
  query SurveyReportsQuery($surveyId: String!) {
    surveyReports: surveyReports(surveyId: $surveyId) {
      id
      name
      subtitle
      type
      createdAt
      createdBy
      status
      downloadUrl
    }
  }
`

const CREATE_REPORT_MUTATION = gql`
  mutation CreateReportMutation($input: CreateReportInput!) {
    createReport(input: $input) {
      id
      name
      subtitle
      status
    }
  }
`

const DELETE_REPORT_MUTATION = gql`
  mutation DeleteReportMutation($id: String!) {
    deleteReport(id: $id) {
      id
    }
  }
`

export const Loading = () => (
  <div className="container mx-auto p-4">
    <div className="flex items-center justify-between mb-6">
      <h2 className="text-xl font-semibold">Reports</h2>
      <div className="skeleton w-36 h-10"></div>
    </div>
    <div className="skeleton h-64 w-full"></div>
  </div>
)

export const Empty = ({ surveyId, new_report }) => {
  const [createReport] = useMutation(CREATE_REPORT_MUTATION, {
    onCompleted: () => {
      toast.success('Report created successfully!')
    },
    onError: (error) => {
      toast.error(`Failed to create report: ${error.message}`)
    },
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
  })

  const handleCreateReport = (type) => {
    navigate(routes.surveyDetail({ id: surveyId, tab: 'reports', new_report: type }))
  }

  const handleSaveReport = async (reportData) => {
    try {
      await createReport({
        variables: {
          input: {
            surveyId,
            name: reportData.name,
            subtitle: reportData.subtitle,
            type: new_report,
            config: reportData,
          },
        },
      })
      navigate(routes.surveyDetail({ id: surveyId, tab: 'reports' }))
    } catch (error) {
      console.error('Error saving report:', error)
    }
  }

  const handleCancelReport = () => {
    navigate(routes.surveyDetail({ id: surveyId, tab: 'reports' }))
  }

  if (new_report) {
    return (
      <ToplineReportDesigner
        surveyId={surveyId}
        onSave={handleSaveReport}
        onCancel={handleCancelReport}
      />
    )
  }

  return (
    <div className="flex h-full w-full flex-col">
      {/* Fixed Header */}
      <div className="flex-shrink-0 p-4 border-b border-base-300">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Reports</h2>
          <div className="dropdown dropdown-end">
            <button tabIndex={0} className="btn btn-primary btn-sm">
              Create Report
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 ml-2">
                <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
              </svg>
            </button>
            <ul tabIndex={0} className="dropdown-content menu p-1 shadow-lg bg-base-100 rounded-box w-60 z-[9999]">
              <li>
                <button
                  className="text-base-content hover:text-primary py-2"
                  onClick={() => handleCreateReport('top_line')}
                >
                  <DocumentChartBarIcon className="h-4 w-4" />
                  <div>
                    <div className="font-medium">Topline Report</div>
                    <span className="text-xs opacity-70">Key metrics and insights</span>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <div className="p-4">
          <EmptyState
            message="No reports created yet"
            description="Create your first report to share survey insights with your team or stakeholders"
            icon={DocumentTextIcon}
          />
        </div>
      </div>
    </div>
  )
}

export const Failure = ({ error }) => (
  <div className="text-red-500">
    Error: {error?.message || 'An unexpected error occurred'}
  </div>
)

export const Success = ({ surveyReports, surveyId, new_report }) => {
  const [createReport] = useMutation(CREATE_REPORT_MUTATION, {
    onCompleted: () => {
      toast.success('Report created successfully!')
    },
    onError: (error) => {
      toast.error(`Failed to create report: ${error.message}`)
    },
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
  })

  const [deleteReport] = useMutation(DELETE_REPORT_MUTATION, {
    onCompleted: () => {
      toast.success('Report deleted successfully!')
    },
    onError: (error) => {
      toast.error(`Failed to delete report: ${error.message}`)
    },
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
  })

  const handleCreateReport = (type) => {
    navigate(routes.surveyDetail({ id: surveyId, tab: 'reports', new_report: type }))
  }

  const handleSaveReport = async (reportData) => {
    try {
      await createReport({
        variables: {
          input: {
            surveyId,
            name: reportData.name,
            subtitle: reportData.subtitle,
            type: new_report,
            config: reportData,
          },
        },
      })
      navigate(routes.surveyDetail({ id: surveyId, tab: 'reports' }))
    } catch (error) {
      console.error('Error saving report:', error)
    }
  }

  const handleCancelReport = () => {
    navigate(routes.surveyDetail({ id: surveyId, tab: 'reports' }))
  }

  const handleDeleteReport = async (id) => {
    if (confirm('Are you sure you want to delete this report? This action cannot be undone.')) {
      try {
        await deleteReport({
          variables: { id },
        })
      } catch (error) {
        console.error('Error deleting report:', error)
      }
    }
  }

  if (new_report) {
    return (
      <ToplineReportDesigner
        surveyId={surveyId}
        onSave={handleSaveReport}
        onCancel={handleCancelReport}
      />
    )
  }

  return (
    <div className="flex h-full w-full flex-col">
      {/* Fixed Header */}
      <div className="flex-shrink-0 p-4 border-b border-base-300">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Reports</h2>
          <div className="dropdown dropdown-end">
            <button tabIndex={0} className="btn btn-primary btn-sm">
              Create Report
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 ml-2">
                <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
              </svg>
            </button>
            <ul tabIndex={0} className="dropdown-content menu p-1 shadow-lg bg-base-100 rounded-box w-60 z-[9999]">
              <li>
                <button
                  className="text-base-content hover:text-primary py-2"
                  onClick={() => handleCreateReport('top_line')}
                >
                  <DocumentChartBarIcon className="h-4 w-4" />
                  <div>
                    <div className="font-medium">Topline Report</div>
                    <span className="text-xs opacity-70">Key metrics and insights</span>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <div className="p-4">
          <ReportsTable
            reports={surveyReports}
            onDelete={handleDeleteReport}
          />
        </div>
      </div>
    </div>
  )
}
