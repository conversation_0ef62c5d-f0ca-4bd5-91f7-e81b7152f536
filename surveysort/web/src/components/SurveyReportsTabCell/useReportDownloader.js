// Custom hook for handling report downloads
const useReportDownloader = () => {
  return (url) => {
    if (!url) return;
    
    try {
      // Create an anchor element to handle the download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'report.pdf'; // Suggested filename
      a.rel = 'noopener noreferrer';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
      }, 100);
    } catch (error) {
      console.error('Error downloading file:', error);
      // Fallback to window.open
      window.open(url, '_blank');
    }
  };
};

export default useReportDownloader; 