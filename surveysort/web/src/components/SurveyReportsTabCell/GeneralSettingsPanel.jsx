import React from 'react'

const GeneralSettingsPanel = ({ name, subtitle, onInputChange }) => {
  return (
    <div className="card bg-base-200 shadow-sm mb-4">
      <div className="card-body p-4">
        <h3 className="card-title text-base">Report Information</h3>
        <div className="form-control w-full">
          <label className="label">
            <span className="label-text">Report Name</span>
          </label>
          <input
            type="text"
            className="input input-bordered w-full"
            value={name || ''}
            onChange={(e) => onInputChange('name', e.target.value)}
            placeholder="e.g. Quarterly Customer Feedback"
          />
        </div>
        <div className="form-control w-full">
          <label className="label">
            <span className="label-text">Report Subtitle</span>
          </label>
          <input
            type="text"
            className="input input-bordered w-full"
            value={subtitle || ''}
            onChange={(e) => onInputChange('subtitle', e.target.value)}
            placeholder="e.g. Q3 2023 Results"
          />
        </div>
      </div>
    </div>
  )
}

export default GeneralSettingsPanel 