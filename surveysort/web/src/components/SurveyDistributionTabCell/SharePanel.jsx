import { useContext } from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { toast } from '@redwoodjs/web/toast'
import { routes } from '@redwoodjs/router'
import { DocumentDuplicateIcon, GlobeAltIcon } from '@heroicons/react/24/outline'
import { SurveyContext } from 'src/components/SurveyDetailCell/SurveyDetailCell'

const SharePanel = ({ surveyId }) => {
  const survey = useContext(SurveyContext)
  const isPublished = survey?.status === 'PUBLISHED'

  const socialShareButtons = [
    {
      name: 'Twitter',
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      ),
      label: 'Share on Twitter',
      className: 'btn-info'
    },
    {
      name: 'Facebook',
      icon: (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
        </svg>
      ),
      label: 'Share on Facebook',
      className: 'btn-primary'
    },
  ]

  return (
    <div className="p-4">
      <div className="max-w-4xl mx-auto">
        {!isPublished ? (
          <div className="alert alert-warning">
            <span className="text-xs">Please publish the survey to enable sharing</span>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-8">
            {/* Left Column - Share Options */}
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-4">Share Link</h3>
                <div className="join w-full">
                  <input
                    type="text"
                    value={`${window.location.origin}${routes.endUserSurveySubmit({
                      id: survey.id,
                    })}`}
                    className="input input-bordered join-item input-sm w-full text-xs"
                    readOnly
                  />
                  <button
                    className="btn join-item btn-primary btn-sm tooltip"
                    data-tip="Copy link"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `${window.location.origin}${routes.endUserSurveySubmit({
                          id: survey.id,
                        })}`
                      )
                      toast.success('Survey link copied to clipboard')
                    }}
                  >
                    <DocumentDuplicateIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-4">Share on Social Media</h3>
                <div className="grid grid-cols-2 gap-4">
                  {socialShareButtons.map((button) => (
                    <button
                      key={button.name}
                      onClick={button.onClick}
                      className={`btn btn-sm btn-outline ${button.className}`}
                    >
                      {button.icon}
                      <span className="text-xs ml-2">{button.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <a
                  href={`${window.location.origin}${routes.endUserSurveySubmit({
                    id: survey.id,
                  })}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-outline btn-sm w-full"
                >
                  <GlobeAltIcon className="h-4 w-4 mr-2" />
                  <span className="text-xs">Open Survey in New Tab</span>
                </a>
              </div>
            </div>

            {/* Right Column - QR Code */}
            <div>
              <h3 className="text-sm font-medium mb-4">QR Code</h3>
              <div className="bg-accent/5 p-6 rounded-lg flex flex-col items-center">
                <QRCodeSVG
                  id="survey-qr-code"
                  value={`${window.location.origin}${routes.endUserSurveySubmit({
                    id: survey.id,
                  })}`}
                  size={100}
                  level="H"
                  includeMargin={true}
                  fgColor="#ff7c00"
                />
                <button
                  className="btn btn-outline btn-sm mt-6 w-full max-w-xs"
                  onClick={() => {
                    const canvas = document.getElementById('survey-qr-code')
                    const pngUrl = canvas
                      .toDataURL('image/png')
                      .replace('image/png', 'image/octet-stream')
                    const downloadLink = document.createElement('a')
                    downloadLink.href = pngUrl
                    downloadLink.download = `survey-${survey.id}-qr.png`
                    document.body.appendChild(downloadLink)
                    downloadLink.click()
                    document.body.removeChild(downloadLink)
                  }}
                >
                  Download QR Code
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SharePanel 