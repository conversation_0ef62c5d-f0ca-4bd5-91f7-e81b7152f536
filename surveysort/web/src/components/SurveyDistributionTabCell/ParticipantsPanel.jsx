import { useState } from 'react'
import { EnvelopeIcon } from '@heroicons/react/24/outline'
import ParticipantsCell from 'src/components/ParticipantsCell'
import UploadContactsDialog from 'src/components/UploadContactsDialog'
import EmailComposerForm from 'src/components/EmailComposerForm'

const ParticipantsPanel = ({ surveyId }) => {
  const [showEmailComposer, setShowEmailComposer] = useState(false)

  return (
    <div className="p-4">
      <div className="flex space-x-6">
        <div className="flex-1">
          {!showEmailComposer ? (
            <>
              <div className="flex justify-between mb-6">
                <h3 className="text-md font-medium">Survey Participants</h3>
                <div className="flex gap-2">
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => setShowEmailComposer(true)}
                  >
                    <EnvelopeIcon className="h-4 w-4 mr-1" />
                    Send Emails
                  </button>
                </div>
              </div>
              <ParticipantsCell surveyId={surveyId} />
            </>
          ) : (
            <div>
              <div className="flex justify-between mb-6">
                <h3 className="text-md font-medium">Email Campaign</h3>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => setShowEmailComposer(false)}
                >
                  Cancel
                </button>
              </div>
              <EmailComposerForm
                surveyId={surveyId}
                onClose={() => setShowEmailComposer(false)}
              />
            </div>
          )}
        </div>
      </div>
      <UploadContactsDialog
        surveyId={surveyId}
        onSuccess={() => {
          // Trigger refetch if needed
        }}
      />
    </div>
  )
}

export default ParticipantsPanel 