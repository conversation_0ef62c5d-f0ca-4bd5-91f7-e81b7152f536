import { useState } from 'react'

import { EnvelopeIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline'

import EmailComposerForm from 'src/components/EmailComposerForm'
import ParticipantsCell from 'src/components/ParticipantsCell'
import UploadParticipantsDialog from 'src/components/UploadParticipantsDialog'

const ParticipantsPanel = ({ surveyId }) => {
  const [showEmailComposer, setShowEmailComposer] = useState(false)

  return (
    <div className="p-4">
      <div className="flex space-x-6">
        <div className="flex-1">
          {!showEmailComposer ? (
            <>
              <div className="mb-6 flex justify-between">
                <h3 className="text-md font-medium">Survey Participants</h3>
                <div className="flex gap-2">
                  <button
                    className="btn btn-outline btn-sm"
                    onClick={() =>
                      document
                        .getElementById('upload-participants-modal')
                        .showModal()
                    }
                  >
                    <ArrowUpTrayIcon className="mr-1 h-4 w-4" />
                    Upload CSV
                  </button>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => setShowEmailComposer(true)}
                  >
                    <EnvelopeIcon className="mr-1 h-4 w-4" />
                    Send Emails
                  </button>
                </div>
              </div>
              <ParticipantsCell surveyId={surveyId} />
            </>
          ) : (
            <div>
              <div className="mb-6 flex justify-between">
                <h3 className="text-md font-medium">Email Campaign</h3>
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={() => setShowEmailComposer(false)}
                >
                  Cancel
                </button>
              </div>
              <EmailComposerForm
                surveyId={surveyId}
                onClose={() => setShowEmailComposer(false)}
              />
            </div>
          )}
        </div>
      </div>
      <UploadParticipantsDialog
        surveyId={surveyId}
        onSuccess={() => {
          // Trigger refetch if needed
        }}
      />
    </div>
  )
}

export default ParticipantsPanel
