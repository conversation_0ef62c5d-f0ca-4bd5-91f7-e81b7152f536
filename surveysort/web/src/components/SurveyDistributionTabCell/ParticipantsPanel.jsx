import { useState } from 'react'

import { EnvelopeIcon } from '@heroicons/react/24/outline'

import EmailComposerForm from 'src/components/EmailComposerForm'
import ParticipantsCell from 'src/components/ParticipantsCell'
import UploadParticipantsDialog from 'src/components/UploadParticipantsDialog'

const ParticipantsPanel = ({ surveyId }) => {
  const [showEmailComposer, setShowEmailComposer] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  return (
    <div className="flex h-full w-full flex-col">
      {/* Fixed Header */}
      <div className="flex-shrink-0 border-b border-base-300 p-4">
        {!showEmailComposer ? (
          <div className="flex items-center justify-between">
            <h3 className="text-md font-medium">Survey Participants</h3>
            <div className="flex gap-2">
              <button
                className="btn btn-primary btn-sm"
                onClick={() => setShowEmailComposer(true)}
              >
                <EnvelopeIcon className="mr-1 h-4 w-4" />
                Send Emails
              </button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <h3 className="text-md font-medium">Email Campaign</h3>
            <button
              className="btn btn-ghost btn-sm"
              onClick={() => setShowEmailComposer(false)}
            >
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Scrollable Content */}
      <div className="min-h-0 flex-1 overflow-y-auto">
        {!showEmailComposer ? (
          <div className="flex h-full flex-col">
            <div className="flex-1 p-4">
              <ParticipantsCell
                key={refreshKey}
                surveyId={surveyId}
                onDataChange={() => setRefreshKey((prev) => prev + 1)}
              />
            </div>
          </div>
        ) : (
          <div className="p-4">
            <EmailComposerForm
              surveyId={surveyId}
              onClose={() => setShowEmailComposer(false)}
            />
          </div>
        )}

        <UploadParticipantsDialog
          surveyId={surveyId}
          onSuccess={() => {
            // Trigger refetch by updating the key
            setRefreshKey((prev) => prev + 1)
          }}
        />
      </div>
    </div>
  )
}

export default ParticipantsPanel
