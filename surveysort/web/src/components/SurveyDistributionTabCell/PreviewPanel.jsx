import { useState, useContext } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { PhotoIcon } from '@heroicons/react/24/outline'
import { FileUploader } from 'src/components/catalyst/file-uploader'
import { SurveyContext } from 'src/components/SurveyDetailCell/SurveyDetailCell'

const GET_PRESIGNED_URL = gql`
  mutation getSurveyPreviewImageUploadPresignedUrl($input: PresignedSurveyPreviewImageUrlInput!) {
    getSurveyPreviewImageUploadPresignedUrl(input: $input) {
      signedUrl
      filePath
    }
  }
`

const UPDATE_SURVEY_SETTINGS = gql`
  mutation UpdateSurveySettings($surveyId: String!, $input: UpdateSurveySettingsInput!) {
    updateSurveySettings(surveyId: $surveyId, input: $input) {
      id
      previewImage
      previewTitle
      previewDescription
    }
  }
`

const PreviewPanel = ({ surveyId, surveySettings }) => {
  const survey = useContext(SurveyContext)
  const [getSurveyPreviewImageUploadPresignedUrl] = useMutation(GET_PRESIGNED_URL)
  const [previewData, setPreviewData] = useState({
    title: surveySettings?.previewTitle,
    description: surveySettings?.previewDescription,
    image: surveySettings?.previewImage
  })

  const [updateSettings] = useMutation(UPDATE_SURVEY_SETTINGS, {
    onCompleted: () => {
      toast.success('Preview settings updated')
    },
  })

  const handlePreviewUpdate = async () => {
    await updateSettings({
      variables: {
        surveyId: survey.id,
        input: {
          previewTitle: previewData.title,
          previewDescription: previewData.description,
          previewImage: previewData.image
        }
      }
    })
  }

  const handleImageUpload = async (file) => {
    try {
      if (file === null) {
        // Update preview data and settings
        setPreviewData(prev => ({ ...prev, image: null }))
        await updateSettings({
          variables: {
            surveyId: survey.id,
            input: {
              previewImage: null,
            }
          }
        });
        return;
      }
      
      // Show loading state
      toast.loading('Uploading image...')

      const { data } = await getSurveyPreviewImageUploadPresignedUrl({
        variables: {
          input: {
            fileName: file.name,
            fileType: file.type,
            surveyId,
          },
        },
      })
      const { signedUrl, filePath } = data.getSurveyPreviewImageUploadPresignedUrl
      const result = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (!result.ok) throw new Error('Upload failed')
      const savedResponse = await updateSettings({
        variables: {
          surveyId: survey.id,
          input: {
            previewImage: filePath,
          }
        }
      });
      
      // Update preview data and settings
      setPreviewData(prev => ({ ...prev, image: savedResponse.data.updateSurveySettings.previewImage }))
      toast.dismiss()
      toast.success('Image uploaded successfully')
    } catch (error) {
      console.error('Upload error:', error)
      toast.dismiss()
      toast.error('Failed to upload image')
    }
  }

  return (
    <div className="flex h-full">
      {/* Left Panel - Settings */}
      <div className="flex-1 overflow-auto p-4">
        <div className="space-y-4">
          <div>
            <label className="text-[11px] font-medium">Preview Image</label>
            <FileUploader
              onUpload={handleImageUpload}
              accept="image/*"
              maxSize={5242880}
              currentFile={previewData.image}
              className="w-full"
            />
            <div className="text-[11px] text-base-content/70 mt-1">
              Recommended size: 1200x630px. Max size: 5MB
            </div>
          </div>
          <div>
            <label className="text-[11px] font-medium">Preview Title</label>
            <input
              type="text"
              className="input input-bordered w-full text-xs"
              value={previewData.title}
              onChange={(e) => setPreviewData(prev => ({ ...prev, title: e.target.value }))}
            />
          </div>
          <div>
            <label className="text-[11px] font-medium">Preview Description</label>
            <textarea
              className="textarea textarea-bordered w-full text-xs"
              value={previewData.description}
              onChange={(e) => setPreviewData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>
          <button
            className="btn btn-primary btn-sm"
            onClick={handlePreviewUpdate}
          >
            Update Preview
          </button>
        </div>
      </div>

      {/* Right Panel - Preview */}
      <div className="w-96 border-l border-base-300 p-4 overflow-auto">
        <div className="sticky top-4">
          <h3 className="text-sm font-medium mb-4">Preview</h3>
          <div className="preview-card">
            {previewData.image ? (
              <img
                src={previewData.image}
                alt="Survey Preview"
                className="w-full h-48 object-cover rounded-lg"
              />
            ) : (
              <div className="w-full h-48 bg-base-200 rounded-lg flex items-center justify-center">
                <PhotoIcon className="h-12 w-12 text-base-content/50" />
              </div>
            )}
            <h3 className="text-xl font-bold mt-4">{previewData.title}</h3>
            <p className="text-base-content/70 mt-2">{previewData.description}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PreviewPanel 