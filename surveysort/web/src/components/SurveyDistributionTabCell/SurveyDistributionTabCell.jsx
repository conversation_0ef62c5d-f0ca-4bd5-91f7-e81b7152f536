import { useState } from 'react'
import { Tab } from '@headlessui/react'
import { gql } from '@redwoodjs/web'

// Import separated components
import PreviewPanel from './PreviewPanel'
import SharePanel from './SharePanel'
import ParticipantsPanel from './ParticipantsPanel'
import TabNavigation from './TabNavigation'

export const QUERY = gql`
  query SurveyDistributionQuery($surveyId: String!) {
    surveySettings(surveyId: $surveyId) {
      id
      previewImage
      previewTitle
      previewDescription
    }
  }
`

export const Loading = () => (
  <div className="w-full px-2 py-16 sm:px-0">
    {/* Tab skeleton */}
    <div className="tabs tabs-boxed">
      <div className="tab tab-active skeleton w-24 h-8"></div>
      <div className="tab skeleton w-24 h-8 ml-2"></div>
    </div>

    {/* Content skeleton */}
    <div className="mt-8">
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="skeleton h-8 w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="skeleton h-32 w-full"></div>
            <div className="skeleton h-12 w-full"></div>
            <div className="skeleton h-24 w-full"></div>
            <div className="skeleton h-10 w-32"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export const Empty = () => <div>No settings found</div>

export const Failure = ({ error }) => (
  <div className="text-error">Error: {error?.message}</div>
)

export const Success = ({ surveySettings, surveyId }) => {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <div className="flex flex-col h-full bg-base-100">
      <Tab.Group onChange={setActiveTab}>
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />

        <Tab.Panels className="flex-1 overflow-auto">
          <Tab.Panel className="flex h-full">
            <PreviewPanel surveyId={surveyId} surveySettings={surveySettings} />
          </Tab.Panel>

          <Tab.Panel>
            <SharePanel surveyId={surveyId} />
          </Tab.Panel>

          <Tab.Panel>
            <ParticipantsPanel surveyId={surveyId} />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  )
}
