import { useState } from 'react'

import { Tab } from '@headlessui/react'

import { gql } from '@redwoodjs/web'

// Import separated components
import ParticipantsPanel from './ParticipantsPanel'
import PreviewPanel from './PreviewPanel'
import SharePanel from './SharePanel'
import TabNavigation from './TabNavigation'

export const QUERY = gql`
  query SurveyDistributionQuery($surveyId: String!) {
    surveySettings(surveyId: $surveyId) {
      id
      previewImage
      previewTitle
      previewDescription
    }
  }
`

export const Loading = () => (
  <div className="w-full px-2 py-16 sm:px-0">
    {/* Tab skeleton */}
    <div className="tabs-boxed tabs">
      <div className="tab tab-active skeleton h-8 w-24"></div>
      <div className="tab skeleton ml-2 h-8 w-24"></div>
    </div>

    {/* Content skeleton */}
    <div className="mt-8">
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="skeleton mb-6 h-8 w-1/4"></div>
          <div className="space-y-4">
            <div className="skeleton h-32 w-full"></div>
            <div className="skeleton h-12 w-full"></div>
            <div className="skeleton h-24 w-full"></div>
            <div className="skeleton h-10 w-32"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export const Empty = () => <div>No settings found</div>

export const Failure = ({ error }) => (
  <div className="text-error">Error: {error?.message}</div>
)

export const Success = ({ surveySettings, surveyId }) => {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <div className="flex h-full w-full flex-col bg-base-100">
      <Tab.Group onChange={setActiveTab}>
        <div className="flex-shrink-0">
          <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        </div>

        <Tab.Panels className="w-full flex-1 overflow-hidden">
          <Tab.Panel className="h-full w-full">
            <PreviewPanel surveyId={surveyId} surveySettings={surveySettings} />
          </Tab.Panel>

          <Tab.Panel className="h-full w-full">
            <SharePanel surveyId={surveyId} />
          </Tab.Panel>

          <Tab.Panel className="h-full w-full">
            <ParticipantsPanel surveyId={surveyId} />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  )
}
