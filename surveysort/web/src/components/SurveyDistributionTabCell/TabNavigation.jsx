import { Tab } from '@headlessui/react'
import classNames from 'classnames'
import {
  ShareIcon,
  EyeIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'

const TabNavigation = ({ activeTab, setActiveTab }) => (
  <Tab.List className="hidden sm:block">
    <nav className="-mb-px flex space-x-1 p-2" aria-label="Tabs">
      <Tab
        className={({ selected }) =>
          classNames(
            'rounded-md px-3 py-1.5 text-xs font-medium',
            selected
              ? 'bg-base-200 text-primary'
              : 'text-base-content/70 hover:text-base-content hover:bg-base-200/50'
          )
        }
      >
        <EyeIcon className="mr-1.5 h-4 w-4 inline" />
        Preview
      </Tab>
      <Tab
        className={({ selected }) =>
          classNames(
            'rounded-md px-3 py-1.5 text-xs font-medium',
            selected
              ? 'bg-base-200 text-primary'
              : 'text-base-content/70 hover:text-base-content hover:bg-base-200/50'
          )
        }
      >
        <ShareIcon className="mr-1.5 h-4 w-4 inline" />
        Share
      </Tab>
      <Tab
        className={({ selected }) =>
          classNames(
            'rounded-md px-3 py-1.5 text-xs font-medium',
            selected
              ? 'bg-base-200 text-primary'
              : 'text-base-content/70 hover:text-base-content hover:bg-base-200/50'
          )
        }
      >
        <UserGroupIcon className="mr-1.5 h-4 w-4 inline" />
        Participants
      </Tab>
    </nav>
  </Tab.List>
)

export default TabNavigation 