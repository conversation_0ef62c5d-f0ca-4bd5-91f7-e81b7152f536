import { useState, useRef } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import {
  ArrowUpTrayIcon,
  DocumentTextIcon,
  XMarkIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline'

const UPSERT_PARTICIPANT_LIST = gql`
  mutation UpsertParticipantList($input: UploadCsv!) {
    upsertParticipantList(input: $input) {
      created
      updated
      skipped
    }
  }
`

const UploadContactsDialog = ({ surveyId, onSuccess }) => {
  const [csvFile, setCsvFile] = useState(null)
  const [csvData, setCsvData] = useState(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef(null)
  
  const [upsertParticipantList] = useMutation(UPSERT_PARTICIPANT_LIST, {
    onCompleted: (data) => {
      const { created, updated, skipped } = data.upsertParticipantList
      toast.success(
        `Successfully uploaded: ${created} created, ${updated} updated, ${skipped} skipped`
      )
      handleClose()
      if (onSuccess) onSuccess()
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`)
      setIsUploading(false)
    },
  })
  
  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return
    
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      toast.error('Please upload a CSV file')
      return
    }
    
    setCsvFile(file)
    
    // Read the file
    const reader = new FileReader()
    reader.onload = (event) => {
      try {
        const csvText = event.target.result
        setCsvData(csvText)
        setIsPreviewMode(true)
      } catch (error) {
        toast.error('Failed to read CSV file')
        console.error(error)
      }
    }
    reader.readAsText(file)
  }
  
  const handleDragOver = (e) => {
    e.preventDefault()
    e.stopPropagation()
  }
  
  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0]
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        toast.error('Please upload a CSV file')
        return
      }
      
      setCsvFile(file)
      
      // Read the file
      const reader = new FileReader()
      reader.onload = (event) => {
        try {
          const csvText = event.target.result
          setCsvData(csvText)
          setIsPreviewMode(true)
        } catch (error) {
          toast.error('Failed to read CSV file')
          console.error(error)
        }
      }
      reader.readAsText(file)
    }
  }
  
  const handleUpload = async () => {
    if (!csvData) return
    
    setIsUploading(true)
    await upsertParticipantList({
      variables: {
        input: {
          surveyId,
          data: csvData,
        },
      },
    })
  }
  
  const handleClose = () => {
    setCsvFile(null)
    setCsvData(null)
    setIsPreviewMode(false)
    setIsUploading(false)
    if (fileInputRef.current) fileInputRef.current.value = ''
    const modal = document.getElementById('upload-contacts-modal')
    if (modal) modal.close()
  }
  
  const renderPreview = () => {
    if (!csvData) return null
    
    const lines = csvData.split('\n')
    const headers = lines[0].split(',')
    const previewRows = lines.slice(1, 6) // Show first 5 rows
    
    return (
      <div className="mt-4">
        <h3 className="text-sm font-medium mb-2">Preview</h3>
        <div className="overflow-x-auto">
          <table className="table table-xs">
            <thead>
              <tr>
                {headers.map((header, index) => (
                  <th key={index}>{header.trim()}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {previewRows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.split(',').map((cell, cellIndex) => (
                    <td key={cellIndex}>{cell.trim()}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {lines.length > 6 && (
          <p className="text-xs text-base-content/70 mt-2">
            +{lines.length - 6} more rows not shown in preview
          </p>
        )}
      </div>
    )
  }
  
  return (
    <dialog id="upload-contacts-modal" className="modal">
      <div className="modal-box max-w-2xl">
        <div className="flex justify-between items-center">
          <h3 className="font-bold text-lg">Upload Contacts</h3>
          <button className="btn btn-sm btn-ghost" onClick={handleClose}>
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        <div className="divider"></div>
        
        {!isPreviewMode ? (
          <div
            className="border-2 border-dashed border-base-300 rounded-lg p-8 text-center"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <ArrowUpTrayIcon className="h-12 w-12 mx-auto text-base-content/50" />
            <p className="mt-4 text-sm">
              Drag and drop your CSV file here, or{' '}
              <button
                className="text-primary hover:text-primary-focus"
                onClick={() => fileInputRef.current?.click()}
              >
                browse
              </button>
            </p>
            <p className="text-xs text-base-content/70 mt-2">
              CSV must have at least 'email' column. 'name' column is also recommended.
            </p>
            <input
              type="file"
              accept=".csv"
              className="hidden"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
          </div>
        ) : (
          <div>
            <div className="flex items-center mb-4">
              <DocumentTextIcon className="h-6 w-6 text-primary mr-2" />
              <span className="text-sm font-medium">{csvFile.name}</span>
              <button
                className="btn btn-ghost btn-xs ml-auto"
                onClick={() => {
                  setCsvFile(null)
                  setCsvData(null)
                  setIsPreviewMode(false)
                  if (fileInputRef.current) fileInputRef.current.value = ''
                }}
              >
                Change
              </button>
            </div>
            
            {renderPreview()}
            
            <div className="alert alert-info mt-4">
              <ExclamationCircleIcon className="h-5 w-5" />
              <div className="text-xs">
                <p>
                  <strong>Important notes:</strong>
                </p>
                <ul className="list-disc pl-4 mt-1">
                  <li>Make sure your CSV has an 'email' column (required)</li>
                  <li>Emails already in the suppression list will be skipped</li>
                  <li>Existing contacts with matching emails will be updated</li>
                </ul>
              </div>
            </div>
          </div>
        )}
        
        <div className="modal-action">
          <button className="btn btn-ghost" onClick={handleClose}>
            Cancel
          </button>
          <button
            className="btn btn-primary"
            disabled={!csvData || isUploading}
            onClick={handleUpload}
          >
            {isUploading ? (
              <>
                <span className="loading loading-spinner loading-xs"></span>
                Uploading...
              </>
            ) : (
              'Upload'
            )}
          </button>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button onClick={handleClose}>close</button>
      </form>
    </dialog>
  )
}

export default UploadContactsDialog 