import { motion } from 'framer-motion'

import {
  Form,
  Label,
  TextField,
  TextAreaField,
  Submit,
  SelectField,
  FormError,
  FieldError,
} from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { navigate, routes } from '@redwoodjs/router'

const UPDATE_SURVEY_MUTATION = gql`
  mutation DeleteSurveyMutation($id: String!, $input: UpdateSurveyInput!) {
    updateSurvey(id: $id, input: $input) {
      id
    }
  }
`

const DeleteSurveyForm = ({ survey, onClose }) => {
  const [updateSurvey, { loading, error }] = useMutation(
    UPDATE_SURVEY_MUTATION,
    {
      onCompleted: () => {
        toast.success('Survey deleted successfully')
        onClose()
        navigate(routes.dashboard())
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const onSubmit = () => {
    updateSurvey({ variables: { id: survey.id, input: { isDeleted: true } } })
  }

  return (
    <Form onSubmit={onSubmit} className="flex justify-end gap-3">
      <FormError error={error} />
      
      <button
        type="button"
        onClick={onClose}
        className="btn btn-ghost btn-sm"
      >
        Cancel
      </button>

      <Submit 
        disabled={loading}
        className="btn btn-error btn-sm"
      >
        {loading ? (
          <motion.div
            className="flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4 mr-2"
              animate={{
                rotate: [0, 360],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: 'linear',
              }}
            >
              <path d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </motion.svg>
            Deleting...
          </motion.div>
        ) : (
          'Delete Survey'
        )}
      </Submit>
    </Form>
  )
}

export default DeleteSurveyForm
