import { useState, useEffect } from 'react'
import { useQuery, gql } from '@redwoodjs/web'
import { 
  MagnifyingGlassIcon,
  ChevronDownIcon,
  PlusIcon,
  MinusIcon,
  FolderIcon,
  ChevronRightIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline'

const ALL_QUESTIONS_QUERY = gql`
  query AllQuestionsQuery {
    allQuestions {
        id
        title
        type
        choices
        categoryId
    }
  }
`

const GET_CATEGORIES = gql`
  query GetCategories {
    categories {
      id
      name
    }
  }
`

const QUESTIONS_BY_CATEGORY_QUERY = gql`
  query GetQuestionsByCategory($categoryId: Int!) {
    questionsByCategory(categoryId: $categoryId) {
      id
      title
      type
      choices
    }
  }
`

const QuestionLibraryPopupComponent = ({ onClose, onDone }) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState(null)
  const [selectedQuestions, setSelectedQuestions] = useState([])
  const [filteredQuestions, setFilteredQuestions] = useState(null)
  const [expandedQuestionId, setExpandedQuestionId] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')

  const { data: allQuestionData, loading: allQuestionLoading, error: allQuestionError } = useQuery(ALL_QUESTIONS_QUERY)
  const { data: categoryData, loading: categoryLoading, error: categoryError } = useQuery(GET_CATEGORIES)
  const { data: questionData, loading: questionLoading, error: questionError, refetch: refetchCategoryQuestions } = useQuery(QUESTIONS_BY_CATEGORY_QUERY, {
    variables: { categoryId: selectedCategoryId },
    skip: !selectedCategoryId,
  })

  useEffect(() => {
    if (allQuestionData) {
      setFilteredQuestions(allQuestionData.allQuestions)
    }
  }, [allQuestionData])

  const handleQuestionSelect = (question) => {
    if (!selectedQuestions.includes(question)) {
      setSelectedQuestions([...selectedQuestions, question])
    }
  }

  const handleQuestionRemove = (question) => {
    setSelectedQuestions(selectedQuestions.filter((q) => q.id !== question.id))
  }

  const handleCategorySelection = (selectedCategoryId) => {
    setSelectedCategoryId(selectedCategoryId)
    if (selectedCategoryId) {
      refetchCategoryQuestions({ selectedCategoryId })
    } else {
      setFilteredQuestions(allQuestionData?.allQuestions)
    }
  }

  const toggleExpand = (questionId) => {
    setExpandedQuestionId(expandedQuestionId === questionId ? null : questionId)
  }

  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
    const questionsToFilter = selectedCategoryId ? questionData?.questionsByCategory : allQuestionData?.allQuestions
    if (questionsToFilter) {
      setFilteredQuestions(
        questionsToFilter.filter((question) =>
          question.title.toLowerCase().includes(e.target.value.toLowerCase())
        )
      )
    }
  }

  const questionsToDisplay = selectedCategoryId 
    ? questionData?.questionsByCategory 
    : filteredQuestions

  const renderQuestions = () => {
    if (questionLoading || allQuestionLoading) return <div>Loading questions...</div>
    if (questionError || allQuestionError) return <div>Error loading questions.</div>

    if (questionsToDisplay?.length > 0) {
      return (
        <div className="space-y-4">
          {questionsToDisplay.map((question) => (
            <div
              key={question.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
            >
              <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleExpand(question.id)}>
                <span className="font-semibold">{question.title}</span>
                {question.choices && question.choices.length > 0 && (
                  <ChevronDownIcon className="w-5 h-5 text-gray-500" />
                )}
                <button
                  className="btn btn-sm btn-primary flex items-center"
                  onClick={() => handleQuestionSelect(question)}
                >
                  <PlusIcon className="w-4 h-4 ml-1" />
                </button>
              </div>

              {expandedQuestionId === question.id && (
                <ul className="mt-2 ml-4">
                  {question.choices && question.choices.length > 0 ? (
                    question.choices.map((choice, index) => (
                      <li key={index} className="text-sm">
                        - {choice}
                      </li>
                    ))
                  ) : (
                    <li className="text-sm">No choices available.</li>
                  )}
                </ul>
              )}
            </div>
          ))}
        </div>
      )
    } else {
      return <div>No questions available.</div>
    }
  }

  if (categoryLoading) return <div>Loading categories...</div>
  if (categoryError) return <div>Error loading categories: {categoryError.message}</div>

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="space-y-4">
        {/* Search & Filter Bar */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <MagnifyingGlassIcon 
              className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-base-content/50" 
            />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={handleSearch}
              className="input input-bordered w-full pl-9 h-9 text-sm"
            />
          </div>

          <select
            className="select select-bordered h-9 text-sm min-h-0"
            onChange={(e) => handleCategorySelection(parseInt(e.target.value))}
          >
            <option value="">All Categories</option>
            {categoryData?.categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Questions Grid */}
        <div className="grid grid-cols-2 gap-4">
          {/* Available Questions */}
          <div className="rounded-xl border border-base-300 bg-base-100 overflow-hidden">
            <div className="p-3 border-b border-base-200 bg-base-200/50">
              <div className="flex items-center gap-2">
                <QuestionMarkCircleIcon className="h-4 w-4 text-base-content/70" />
                <h3 className="text-sm font-medium">Available Questions</h3>
              </div>
            </div>

            <div className="divide-y divide-base-200 max-h-[calc(100vh-25rem)] overflow-y-auto p-2">
              {(questionLoading || allQuestionLoading) ? (
                <div className="p-4 text-center text-sm text-base-content/70">
                  Loading questions...
                </div>
              ) : questionsToDisplay?.length > 0 ? (
                questionsToDisplay.map((question) => (
                  <div
                    key={question.id}
                    className="group py-2"
                  >
                    <div className="flex items-start justify-between gap-4">
                      <div 
                        className="flex-1 cursor-pointer"
                        onClick={() => toggleExpand(question.id)}
                      >
                        <h4 className="text-sm font-medium group-hover:text-primary">
                          {question.title}
                        </h4>
                      </div>
                      <button
                        className="btn btn-ghost btn-xs"
                        onClick={() => handleQuestionSelect(question)}
                      >
                        <PlusIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {expandedQuestionId === question.id && question.choices?.length > 0 && (
                      <div className="mt-2 pl-4 text-xs text-base-content/70 space-y-1">
                        {question.choices.map((choice, index) => (
                          <div key={index}>• {choice}</div>
                        ))}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-sm text-base-content/70">
                  No questions available
                </div>
              )}
            </div>
          </div>

          {/* Selected Questions */}
          <div className="rounded-xl border border-base-300 bg-base-100 overflow-hidden">
            <div className="p-3 border-b border-base-200 bg-base-200/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FolderIcon className="h-4 w-4 text-base-content/70" />
                  <h3 className="text-sm font-medium">Selected Questions</h3>
                </div>
                <span className="text-xs text-base-content/70">
                  {selectedQuestions.length} selected
                </span>
              </div>
            </div>

            <div className="divide-y divide-base-200  overflow-y-auto p-2">
              {selectedQuestions.length > 0 ? (
                selectedQuestions.map((question) => (
                  <div
                    key={question.id}
                    className="group flex items-center justify-between gap-4 py-2"
                  >
                    <span className="text-sm">{question.title}</span>
                    <button
                      className="btn btn-ghost btn-xs text-error"
                      onClick={() => handleQuestionRemove(question)}
                    >
                      <MinusIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-sm text-base-content/70">
                  No questions selected
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <button 
            className="btn btn-ghost btn-sm" 
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            className="btn btn-primary btn-sm" 
            onClick={() => onDone(selectedQuestions)}
          >
            Add Selected
          </button>
        </div>
      </div>
    </div>
  )
}

export default QuestionLibraryPopupComponent
