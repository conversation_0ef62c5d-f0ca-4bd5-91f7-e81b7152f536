import SurveyShareDialog from './SurveyShareDialog'

export default {
  component: SurveyShareDialog,
  title: 'Components/SurveyShareDialog',
}

const Template = (args) => <SurveyShareDialog {...args} />

export const Default = Template.bind({})
Default.args = {
  isOpen: true,
  closeModal: () => alert('Modal closed'),
  selectedSurvey: {
    id: 123,
    surveyName: 'Customer Satisfaction Survey',
  },
}

export const Closed = Template.bind({})
Closed.args = {
  isOpen: false,
  closeModal: () => alert('Modal closed'),
  selectedSurvey: {
    id: 123,
    surveyName: 'Customer Satisfaction Survey',
  },
}
