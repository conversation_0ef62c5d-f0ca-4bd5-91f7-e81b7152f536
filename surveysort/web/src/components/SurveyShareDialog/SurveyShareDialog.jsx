import {
  Dialog,
  DialogActions,
  DialogBody,
  DialogDescription,
  DialogTitle,
} from 'src/components/catalyst/dialog'

const SurveyShareDialog = ({
  isOpen = false,
  closeModal = () => {},
  selectedSurvey,
}) => {
  return (
    <Dialog size="xl" open={isOpen} onClose={closeModal}>
      <DialogTitle>Share Survey</DialogTitle>
      <DialogDescription>
        Share the survey with others using the link and social icons below.
      </DialogDescription>
      <DialogBody>
        <div>
          <p>Survey Link:</p>
          <a
            href={`/survey/${selectedSurvey?.id}`}
          >{`/survey/${selectedSurvey?.id}`}</a>
        </div>
        <div className="mt-4">
          <p>Share on:</p>
          <div className="flex space-x-4">
            {/* Social Icons (example using HeroIcons) */}
            <a
              href={`https://twitter.com/share?url=/survey/${selectedSurvey?.id}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg
                className="h-6 w-6 text-blue-500"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M23.954 4.569c-.885.391-1.83.654-2.825.775 1.013-.611 1.794-1.574 2.163-2.724-.95.555-2.005.959-3.127 1.184-.896-.959-2.173-1.555-3.591-1.555-2.72 0-4.924 2.204-4.924 4.923 0 .39.045.765.127 1.124C7.691 8.095 4.066 6.13 1.64 3.161c-.427.721-.666 1.561-.666 2.475 0 1.708.87 3.215 2.188 4.096-.807-.026-1.566-.247-2.23-.616v.061c0 2.388 1.699 4.379 3.946 4.828-.413.112-.849.171-1.295.171-.314 0-.615-.03-.916-.086.631 1.953 2.445 3.377 4.604 3.418-1.685 1.32-3.808 2.105-6.115 2.105-.398 0-.79-.023-1.17-.067 2.189 1.403 4.768 2.215 7.548 2.215 9.142 0 14.307-7.721 14.307-14.417 0-.219-.004-.436-.014-.653.986-.711 1.844-1.602 2.513-2.614z" />
              </svg>
            </a>
            <a
              href={`https://facebook.com/sharer/sharer.php?u=/survey/${selectedSurvey?.id}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg
                className="h-6 w-6 text-blue-700"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M22.676 0H1.324C.592 0 0 .593 0 1.324v21.352C0 23.406.593 24 1.324 24h11.484V14.704h-3.12v-3.62h3.12V8.41c0-3.1 1.894-4.787 4.659-4.787 1.325 0 2.463.1 2.795.144v3.243l-1.92.001c-1.504 0-1.796.714-1.796 1.76v2.309h3.592l-.468 3.62h-3.124V24h6.118c.731 0 1.324-.594 1.324-1.324V1.324C24 .593 23.407 0 22.676 0" />
              </svg>
            </a>
            {/* Add more social icons as needed */}
          </div>
        </div>
      </DialogBody>
      <DialogActions>
        <button className="btn btn-xs" onClick={closeModal}>
          Close
        </button>
      </DialogActions>
    </Dialog>
  )
}

export default SurveyShareDialog
