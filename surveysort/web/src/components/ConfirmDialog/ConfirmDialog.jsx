import { useEffect, useRef } from 'react'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'

const ConfirmDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'danger' // 'danger', 'warning', 'info'
}) => {
  const confirmButtonRef = useRef(null)

  // Handle keyboard events
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        onClose()
      } else if (e.key === 'Enter') {
        onConfirm()
        onClose()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, onClose, onConfirm])

  // Focus management
  useEffect(() => {
    if (isOpen && confirmButtonRef.current) {
      confirmButtonRef.current.focus()
    }
  }, [isOpen])

  if (!isOpen) return null

  const getTypeClasses = () => {
    switch (type) {
      case 'danger':
        return {
          icon: 'text-error',
          confirmBtn: 'btn-error'
        }
      case 'warning':
        return {
          icon: 'text-warning',
          confirmBtn: 'btn-warning'
        }
      case 'info':
        return {
          icon: 'text-info',
          confirmBtn: 'btn-info'
        }
      default:
        return {
          icon: 'text-error',
          confirmBtn: 'btn-error'
        }
    }
  }

  const typeClasses = getTypeClasses()

  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div
      className="modal modal-open"
      onClick={handleBackdropClick}
    >
      <div className="modal-box">
        <div className="flex items-center gap-4 mb-4">
          <div className={`flex-shrink-0 ${typeClasses.icon}`}>
            <ExclamationTriangleIcon className="h-8 w-8" />
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-lg">{title}</h3>
            {message && (
              <p className="text-sm text-base-content/70 mt-1">{message}</p>
            )}
          </div>
        </div>

        <div className="modal-action">
          <button
            className="btn btn-ghost"
            onClick={onClose}
          >
            {cancelText}
          </button>
          <button
            ref={confirmButtonRef}
            className={`btn ${typeClasses.confirmBtn}`}
            onClick={handleConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ConfirmDialog
