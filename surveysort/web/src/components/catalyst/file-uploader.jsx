import { useState, useRef } from 'react';
import { XMarkIcon } from '@heroicons/react/20/solid';

export const FileUploader = ({
  onUpload,
  accept = 'image/*', // Default to images
  maxSize = 5242880, // 5MB default
  currentFile,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(currentFile); // For previewing images
  const fileInputRef = useRef(null);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    const file = e.dataTransfer.files[0];
    validateAndUpload(file);
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    validateAndUpload(file);
  };

  const validateAndUpload = (file) => {
    setError(null);
    setPreviewUrl(null); // Reset preview

    if (!file) return;

    // Check file type
    if (!file.type.match(accept.replace('*', '.*'))) {
      setError(`Invalid file type. Please upload ${accept} files.`);
      return;
    }

    // Check file size
    if (file.size > maxSize) {
      setError(`File too large. Maximum size is ${(maxSize / 1024 / 1024).toFixed(2)}MB.`);
      return;
    }

    // Generate preview for image files
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewUrl(reader.result);
      };
      reader.readAsDataURL(file);
    }

    onUpload(file);
  };

  const handleRemove = () => {
    onUpload(null);
    setPreviewUrl(null); // Remove preview
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div
        className={`
          relative rounded-lg border-2 border-dashed p-4 text-center
          ${isDragging ? 'border-primary bg-primary/5' : 'border-base-300'}
          ${error ? 'border-error' : ''}
        `}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
        />
        {previewUrl ? (
          <div className="flex flex-col items-center">
            <img src={previewUrl} alt="Preview" className="max-w-full max-h-48 mb-2 rounded" />
            <button
              type="button"
              onClick={handleRemove}
              className="btn btn-error btn-sm"
            >
              Remove File
            </button>
          </div>
        ) : (
          <div>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="btn btn-ghost btn-xs"
            >
              Choose file
            </button>
            <p className="mt-1 text-xs text-base-content/70">or drag and drop</p>
          </div>
        )}
      </div>

      {error && (
        <p className="text-xs text-error">{error}</p>
      )}
    </div>
  );
};
