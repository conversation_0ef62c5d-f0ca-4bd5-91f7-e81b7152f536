import { BarChart } from "@tremor/react"

const CustomTooltip = ({ payload, active }) => {
  if (!active || !payload || !payload.length) return null

  const dataPoint = payload[0]
  if (!dataPoint) return null

  return (
    <>
      <div className="w-60 rounded-md border border-base-300/10 bg-primary px-4 py-1.5 text-sm shadow-md">
        <p className="flex items-center justify-between">
          <span className="text-primary-content">Option</span>
          <span className="font-medium text-primary-content">{dataPoint.payload.name}</span>
        </p>
      </div>
      <div className="mt-1 w-60 space-y-1 rounded-md border border-base-300/10 bg-base-100 px-4 py-2 text-sm shadow-md">
        <div className="flex items-center space-x-2.5">
          <span className="bg-primary size-2.5 shrink-0 rounded-sm" aria-hidden={true} />
          <div className="flex w-full justify-between">
            <span className="text-base-content">Responses</span>
            <div className="flex items-center space-x-1">
              <span className="font-medium text-base-content">
                {dataPoint.value}
              </span>
              <span className="text-base-content/70">
                ({dataPoint.payload.percentage}%)
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const MultipleChoiceQuestionAnalysis = ({ data, insights }) => {
  if (!data?.length) return <div className="text-base-content/70">No data available</div>

  const chartData = [...data].map(item => ({
    name: item.category,
    responses: item.value,
    percentage: item.percentage
  }))

  return (
    <div className="backdrop-blur-sm bg-white/30 border border-base-200 rounded-lg p-4 space-y-8">
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Total Responses</div>
          <div className="text-lg font-semibold text-base-content">{insights.totalResponses}</div>
        </div>
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Response Rate</div>
          <div className="text-lg font-semibold text-base-content">{insights.responseRate}%</div>
        </div>
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Most Selected</div>
          <div className="text-lg font-semibold text-base-content truncate">
            {insights.mostSelected?.category}
          </div>
          <div className="text-xs text-base-content/70">
            {insights.mostSelected?.percentage}% of responses
          </div>
        </div>
      </div>

      <BarChart
        data={chartData}
        index="name"
        categories={['responses']}
        colors={["primary"]}
        valueFormatter={(value) => `${value}`}
        customTooltip={CustomTooltip}
        layout="vertical"
        className="h-72 text-xs [&_.tremor-BarChart-bar]:fill-[url(#bar-gradient)]"
        showLegend={false}
        showGridLines={false}
        yAxisWidth={48}
      >
        <defs>
          <linearGradient id="bar-gradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="10%" stopColor="var(--gradient-from)" />
            <stop offset="60%" stopColor="var(--gradient-to)" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  )
}

export default MultipleChoiceQuestionAnalysis 