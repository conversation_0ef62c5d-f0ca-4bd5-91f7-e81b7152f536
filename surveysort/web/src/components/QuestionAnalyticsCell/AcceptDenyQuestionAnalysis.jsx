import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/solid"

const AcceptDenyQuestionAnalysis = ({ data, insights }) => {
  if (!data?.length) return <div className="text-base-content/70">No data available</div>

  const accepts = data.find(d => d.category === 'Yes') || { value: 0, percentage: 0 }
  const denies = data.find(d => d.category === 'No') || { value: 0, percentage: 0 }

  return (
    <div className="backdrop-blur-sm bg-white/30 border border-base-200 rounded-lg p-4 space-y-8">
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Total Responses</div>
          <div className="text-lg font-semibold text-base-content">{insights.totalResponses}</div>
        </div>
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Response Rate</div>
          <div className="text-lg font-semibold text-base-content">{insights.responseRate}%</div>
        </div>
      </div>

      <div className="flex gap-4">
        <div className="flex-1 p-4 bg-success/10 rounded-lg border border-success/20">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircleIcon className="w-6 h-6 text-success" />
            <div className="text-2xl font-bold text-success">{accepts.percentage.toFixed(1)}%</div>
          </div>
          <div className="text-sm text-success">Accepted</div>
          <div className="text-xs text-success/70">{accepts.value} responses</div>
        </div>

        <div className="flex-1 p-4 bg-error/10 rounded-lg border border-error/20">
          <div className="flex items-center gap-2 mb-2">
            <XCircleIcon className="w-6 h-6 text-error" />
            <div className="text-2xl font-bold text-error">{denies.percentage.toFixed(1)}%</div>
          </div>
          <div className="text-sm text-error">Denied</div>
          <div className="text-xs text-error/70">{denies.value} responses</div>
        </div>
      </div>

    </div>
  )
}

export default AcceptDenyQuestionAnalysis 