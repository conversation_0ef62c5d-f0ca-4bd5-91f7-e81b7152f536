import { Text } from '@tremor/react'
import { ResponsiveHeatMap } from '@nivo/heatmap'

const MatrixQuestionAnalysis = ({ data, insights, timeframe }) => {
  // Transform data for heatmap
  const transformDataForHeatmap = () => {
    const rows = new Set()
    const cols = new Set()
    const valueMap = new Map()

    // Extract unique rows and columns
    data.forEach(item => {
      const [row, col] = item.category.split(' - ')
      if (col !== 'Total') {
        rows.add(row)
        cols.add(col)
        valueMap.set(`${row}-${col}`, {
          value: item.value,
          percentage: item.percentage
        })
      }
    })

    // Create heatmap data structure for Nivo
    const heatmapData = Array.from(rows).map(row => ({
      id: row,
      data: Array.from(cols).map(col => ({
        x: col,
        y: valueMap.get(`${row}-${col}`)?.percentage || 0
      }))
    }))

    return heatmapData
  }

  const heatmapData = transformDataForHeatmap()

  // Don't render heatmap if no data
  if (!data.length) {
    return (
      <div>
        <h2 className="text-xl font-semibold mb-2">Response Distribution</h2>
        <p className="text-base-content/60">No responses yet</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <h2 className="text-xl font-semibold mb-2">Response Distribution</h2>
      <p className="text-base-content/60 mb-6">
        Total Responses: {insights.totalResponses}
      </p>

      {/* Full width container with fixed height */}
      <div className="w-full h-[600px] relative">
        {heatmapData.length > 0 ? (
          <ResponsiveHeatMap
            data={heatmapData}
            margin={{ top: 60, right: 80, bottom: 60, left: 160 }}
            valueFormat=">-.2f"
            indexBy="id"
            keys={Array.from(new Set(heatmapData[0]?.data.map(d => d.x) || []))}
            padding={0.3}
            axisTop={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: -30,
              legend: '',
              legendOffset: 46
            }}
            axisRight={null}
            axisBottom={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: -30,
              legend: '',
              legendOffset: 46
            }}
            axisLeft={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: '',
              legendOffset: -40
            }}
            colors={{
              type: 'sequential',
              scheme: 'oranges'
            }}
            cellOpacity={1}
            cellBorderWidth={1}
            cellBorderColor="white"
            labelTextColor={{ from: 'color', modifiers: [['darker', 3]] }}
            legends={[
              {
                anchor: 'bottom',
                translateX: 0,
                translateY: 30,
                length: 240,
                thickness: 10,
                direction: 'row',
                tickPosition: 'after',
                tickSize: 3,
                tickSpacing: 4,
                tickOverlap: false,
                title: 'Response Distribution (%)',
                titleAlign: 'start',
                titleOffset: 4
              }
            ]}
            annotations={[]}
            tooltip={({ xKey, yKey, value, cell }) => (
              <div className="bg-base-100 shadow-lg rounded-lg p-3">
                <div className="font-bold text-base-content mb-1">{cell.id}</div>
                <div className="text-base-content/80">
                  {xKey}: {value?.toFixed(2) || '0.00'}%
                </div>
              </div>
            )}
            theme={{
              tooltip: {
                container: {
                  background: 'var(--fallback-b1,oklch(var(--b1)))',
                  color: 'var(--fallback-bc,oklch(var(--bc)))',
                  fontSize: '14px',
                  borderRadius: '0.5rem',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                }
              }
            }}
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center text-base-content/60">
            No data available for heatmap visualization
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-6 mt-6">
        {data
          .filter(item => item.category.endsWith('Total'))
          .map(item => (
            <div key={item.category} className="bg-base-200/50 rounded-lg p-4">
              <h3 className="text-base-content/60 text-sm">
                {item.category.split(' - ')[0]}
              </h3>
              <p className="text-2xl font-bold text-base-content">
                {item.value} responses
              </p>
              <p className="text-base-content/60">
                {item.percentage.toFixed(2)}% of total
              </p>
            </div>
          ))}
      </div>
    </div>
  )
}

export default MatrixQuestionAnalysis 