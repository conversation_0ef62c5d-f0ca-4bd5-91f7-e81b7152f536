import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Title,
  Text,
  AreaChart,
  Card,
  Grid,
  Col,
  Select,
  SelectItem,
} from '@tremor/react'
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import MultipleChoiceQuestionAnalysis from './MultipleChoiceQuestionAnalysis'
import RatingQuestionAnalysis from './RatingQuestionAnalysis'
import EmoticonQuestionAnalysis from './EmoticonQuestionAnalysis'
import ScoringQuestionAnalysis from './ScoringQuestionAnalysis'
import ScaleQuestionAnalysis from './ScaleQuestionAnalysis'
import LikeDislikeQuestionAnalysis from './LikeDislikeQuestionAnalysis'
import AcceptDenyQuestionAnalysis from './AcceptDenyQuestionAnalysis'
import MatrixQuestionAnalysis from './MatrixQuestionAnalysis'
import TimeSeriesChart from './TimeSeriesChart'
import OpenEndedQuestionAnalysis from './OpenEndedQuestionAnalysis'
import { useQuery } from '@redwoodjs/web'

const customTremorStyles = {
  card: "bg-base-100 border border-base-200 rounded-lg",
  title: "text-base-content font-semibold",
  chart: "mt-4 [&_path]:stroke-primary [&_path]:fill-primary/10"
}

export const QUERY = gql`
  query QuestionAnalyticsQuery($surveyId: String!, $input: QuestionAnalyticsInput) {
    questionAnalytics(surveyId: $surveyId, input: $input) {
      surveyMetrics {
        totalResponses
        completedResponses
        completionRate
        highQualityResponses
        highQualityRate
        averageResponseTime
      }
      timeSeriesData {
        date
        responses
        completedResponses
      }
      questions {
        questionId
        questionType
        title
        responseRate
        chartType
        chartData {
          data {
            category
            value
            percentage
            label
          }
          insights {
            totalResponses
            responseRate
            averageRating
            maxRating
            mostSelected {
              category
              value
              percentage
            }
            label
          }
          openEndedAnalysis {
            sentimentDistribution {
              category
              value
              percentage
              label
            }
            keyInsights {
              insight
              example_responses
            }
            commonThemes {
              theme
              count
            }
          }
        }
      }
    }
  }
`

const timeframeOptions = [
  { value: 'ALL', label: 'All Time' },
  { value: 'LAST_1_DAY', label: 'Last 24h' },
  { value: 'LAST_7_DAYS', label: 'Last Week' },
  { value: 'LAST_30_DAYS', label: 'Last Month' },
  { value: 'LAST_90_DAYS', label: 'Last 90 Days' },
]

export const Loading = () => (
  <div className="flex h-full items-center justify-center">
    <div className="loading loading-spinner loading-lg"></div>
  </div>
)

export const Empty = () => (
  <div className="flex h-full items-center justify-center">
    <Text>No analytics available</Text>
  </div>
)

export const Failure = ({ error }) => (
  <div className="alert alert-error">
    <Text>Error: {error?.message}</Text>
  </div>
)


const QuestionAnalyticsCard = ({ question, timeframe }) => {
  const handleClick = (e) => {
    e.preventDefault()
    const element = document.getElementById(question.questionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      window.history.pushState(null, '', `#${question.questionId}`)
    }
  }



  return (
    <div id={question.questionId} className="card bg-base-100 shadow scroll-mt-24">
      <div className="card-body">
        <div className="flex justify-between items-center mb-6">
          <a 
            href={`#${question.questionId}`} 
            onClick={handleClick}
            className="card-title text-base-content hover:text-primary transition-colors"
          >
            {question.title}
          </a>
        </div>
        {renderVisualization(question, timeframe)}
      </div>
    </div>
  )
}

const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { 
    opacity: 1, 
    y: 0,
    transition: {
      type: "spring",
      stiffness: 50,
      mass: 0.5
    }
  }
}

export const Success = ({ questionAnalytics, surveyId }) => {
  const [timeframe, setTimeframe] = useState('ALL')
  const [searchQuery, setSearchQuery] = useState('')
  
  const { refetch } = useQuery(QUERY, {
    variables: {
      surveyId,
      input: {
        timeframe: 'ALL'
      }
    }
  })

  useEffect(() => {
    refetch({
      surveyId,
      input: {
        timeframe,
        excludeIncomplete: false,
        excludeLowQuality: false
      }
    })
  }, [timeframe, refetch, surveyId])

  const { surveyMetrics, timeSeriesData, questions } = questionAnalytics

  const filteredQuestions = questions.filter(q => 
    q.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  useEffect(() => {
    const hash = window.location.hash
    if (hash) {
      const id = hash.replace('#', '')
      const element = document.getElementById(id)
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth' })
        }, 100)
      }
    }
  }, [])

  return (
    <div className="h-full w-full flex flex-col">
      {/* Floating Header - More minimal timeframe selector */}
      <div className="sticky top-0 z-10 flex justify-end p-2">
        <span className="inline-flex rounded-md shadow-sm scale-90">
          {timeframeOptions.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => setTimeframe(option.value)}
              className={`relative text-xs ${
                option.value === timeframe 
                  ? 'bg-primary text-primary-content' 
                  : 'bg-base-100/50 backdrop-blur-sm text-base-content hover:bg-base-200/50'
              } ${
                option === timeframeOptions[0] ? 'rounded-l-md' : ''
              } ${
                option === timeframeOptions[timeframeOptions.length - 1] ? 'rounded-r-md' : ''
              } -ml-px first:ml-0 px-2 py-1 font-medium focus:z-10`}
            >
              {option.label}
            </button>
          ))}
        </span>
      </div>

      <motion.div 
        className="flex-1 overflow-y-auto"
        variants={containerVariants}
        initial="hidden"
        animate="show"
      >
        <div className="p-4 space-y-6">
          {/* First Row - Time Series and Metrics in 3/4 1/4 layout */}
          <motion.div 
            className="grid grid-cols-4 gap-6"
            variants={itemVariants}
          >
            <div className="col-span-3">
              <TimeSeriesChart data={timeSeriesData} />
            </div>

            {/* Quick Stats - 1/4 width */}
            <div className="col-span-1">
              <div className="backdrop-blur-sm bg-white/30 border border-base-200 rounded-lg overflow-hidden">
                <div className="border-b border-base-300 bg-base-200/50 p-4">
                  <div className="text-sm font-medium text-base-content">Total Responses</div>
                  <div className="text-2xl font-semibold text-base-content">
                    {surveyMetrics.totalResponses}
                  </div>
                </div>
                <dl className="divide-y divide-base-200 px-4 py-2 text-sm">
                  <div className="flex justify-between gap-x-4 py-1.5">
                    <dt className="text-base-content/60">Completed</dt>
                    <dd className="text-base-content">
                      <span className="font-medium">{surveyMetrics.completedResponses}</span>
                      <span className="ml-1 text-success text-xs">
                        ({surveyMetrics.completionRate}%)
                      </span>
                    </dd>
                  </div>
                  <div className="flex justify-between gap-x-4 py-1.5">
                    <dt className="text-base-content/60">High Quality</dt>
                    <dd className="text-base-content">
                      <span className="font-medium">{surveyMetrics.highQualityResponses}</span>
                      <span className="ml-1 text-success text-xs">
                        ({surveyMetrics.highQualityRate}%)
                      </span>
                    </dd>
                  </div>
                  <div className="flex justify-between gap-x-4 py-1.5">
                    <dt className="text-base-content/60">Avg. Time</dt>
                    <dd className="text-base-content">
                      {Math.round(surveyMetrics.averageResponseTime / 60)} min
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </motion.div>

          {/* Search Section */}
          <motion.div 
            className="flex justify-end"
            variants={itemVariants}
          >
            <div className="relative w-64">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-4 w-4 text-base-content/50" aria-hidden="true" />
              </div>
              <input
                type="text"
                placeholder="Search questions..."
                className="input input-bordered input-sm pl-10 pr-4 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </motion.div>

          {/* Questions Section */}
          <motion.div 
            className="space-y-6"
            variants={containerVariants}
          >
            {filteredQuestions
              .filter(question => 
                !['CONTACT_DETAILS', 'DATE_TIME', 'WELCOME_MESSAGE', 'THANK_YOU_MESSAGE', 'INSTRUCTION']
                .includes(question.questionType)
              )
              .map((question) => (
                <motion.div
                  key={question.questionId}
                  variants={itemVariants}
                >
                  <QuestionAnalyticsCard 
                    question={question}
                    timeframe={timeframe}
                  />
                </motion.div>
              ))
            }
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}

export const renderVisualization = (question, timeframe) => {
  switch (question.questionType) {
    case 'SHORT':
    case 'ESSAY':
      return <OpenEndedQuestionAnalysis question={question} />
    case 'MULTIPLE_CHOICE':
      return <MultipleChoiceQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
      />
    case 'RATING':
      return <RatingQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
        labels={{
          title: 'Rating',
          average: 'Average Rating',
          value: 'Responses'
        }}
      />
    case 'EMOTICON':
      return <EmoticonQuestionAnalysis 
        data={question.chartData.data || []}
        insights={{
          ...question.chartData.insights,
          label: question.chartData.insights.label || 'Average Sentiment'
        }}
        timeframe={timeframe}
      />
    case 'SCORING':
      return <ScoringQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
        labels={{
          title: 'Score',
          average: 'Average Score',
          value: 'Responses'
        }}
      />
    case 'SCALE':
      return <ScaleQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
        labels={{
          title: 'Scale',
          average: 'Average Scale',
          value: 'Responses'
        }}
      />
    case 'LIKE_DISLIKE':
      return <LikeDislikeQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
      />
    case 'ACCEPT_DENY':
      return <AcceptDenyQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
      />
    case 'MATRIX':
      return <MatrixQuestionAnalysis 
        data={question.chartData.data}
        insights={question.chartData.insights}
        timeframe={timeframe}
      />
    default:
      return null
  }
} 