import { AreaChart } from "@tremor/react"
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import isToday from 'dayjs/plugin/isToday'

// Extend dayjs with plugins
dayjs.extend(relativeTime)
dayjs.extend(isToday)

const TimeSeriesChart = ({ data }) => {
  // Format dates and ensure valid data
  const formattedData = data?.map(point => {
    const date = dayjs(point.date)
    let formattedDate

    // Choose format based on how old the date is
    if (date.isToday()) {
      formattedDate = date.format('HH:mm')
    } else if (date.isAfter(dayjs().subtract(7, 'day'))) {
      formattedDate = date.format('ddd HH:mm') // e.g. "Mon 15:30"
    } else if (date.isAfter(dayjs().subtract(30, 'day'))) {
      formattedDate = date.format('MMM D') // e.g. "Dec 25"
    } else {
      formattedDate = date.format('MMM YY') // e.g. "Dec 23"
    }

    return {
      date: formattedDate,
      responses: Number(point.responses),
      completedResponses: Number(point.completedResponses),
      originalDate: point.date // Store original date for tooltip
    }
  }) || []

  return (
    <div className="backdrop-blur-sm bg-white/30 border border-base-200 rounded-lg p-4">
      <h3 className="text-base font-semibold text-base-content mb-4">
        Responses Over Time
      </h3>
      <AreaChart
        data={formattedData}
        index="date"
        categories={["responses", "completedResponses"]}
        colors={["primary", "secondary"]}
        valueFormatter={(value) => value.toString()}
        showLegend={false}
        showGridLines={false}
        xAxisLabel="Time"
        yAxisLabel="Number of Responses"
        customTooltip={({ payload }) => {
          if (!payload?.[0]) return null
          const date = dayjs(payload[0].payload.originalDate)
          return (
            <div className="rounded-lg border border-base-200 bg-base-100 p-2 shadow-md">
              <div className="text-sm font-medium text-base-content">
                {date.format('MMM D, YYYY HH:mm')}
                <div className="text-xs text-base-content/70">
                  {date.fromNow()}
                </div>
              </div>
              <div className="mt-1 flex flex-col gap-1">
                <div className="flex items-center justify-between gap-2">
                  <span className="text-xs text-base-content/70">Total:</span>
                  <span className="text-xs font-medium text-base-content">
                    {payload[0].value}
                  </span>
                </div>
                <div className="flex items-center justify-between gap-2">
                  <span className="text-xs text-base-content/70">Completed:</span>
                  <span className="text-xs font-medium text-base-content">
                    {payload[1].value}
                  </span>
                </div>
              </div>
            </div>
          )
        }}
        yAxisWidth={48}
        tickGap={5}
        startEndOnly={false}
        className="h-[240px] [&_.tremor-AxisText]:text-xs [&_.tremor-AxisText]:text-base-content/60 [&_.tremor-Axis]:font-light"
      />
    </div>
  )
}

export default TimeSeriesChart 