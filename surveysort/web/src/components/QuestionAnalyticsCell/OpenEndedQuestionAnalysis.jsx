import { useState } from 'react'
import { <PERSON><PERSON><PERSON> } from '@tremor/react'
import { Text } from '../catalyst/text'

const CustomTooltip = ({ payload, active }) => {
  if (!active || !payload || !payload.length) return null

  const dataPoint = payload[0]
  if (!dataPoint) return null

  return (
    <>
      <div className="w-60 rounded-md border border-base-300/10 bg-primary px-4 py-1.5 text-sm shadow-md">
        <p className="flex items-center justify-between">
          <span className="text-primary-content">Sentiment</span>
          <span className="font-medium text-primary-content">{dataPoint.payload.name}</span>
        </p>
      </div>
      <div className="mt-1 w-60 space-y-1 rounded-md border border-base-300/10 bg-base-100 px-4 py-2 text-sm shadow-md">
        <div className="flex items-center space-x-2.5">
          <span className="bg-primary size-2.5 shrink-0 rounded-sm" aria-hidden={true} />
          <div className="flex w-full justify-between">
            <span className="text-base-content">Responses</span>
            <div className="flex items-center space-x-1">
              <span className="font-medium text-base-content">
                {dataPoint.value}
              </span>
              <span className="text-base-content/70">
                ({dataPoint.payload.percentage}%)
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const OpenEndedQuestionAnalysis = ({ question }) => {
  const { chartData: { insights, openEndedAnalysis } } = question
  const [activeTab, setActiveTab] = useState('sentiment')

  const chartData = openEndedAnalysis?.sentimentDistribution?.map(item => ({
    name: item.category,
    responses: item.value,
    percentage: item.percentage
  })) || []

  return (
    <div className="backdrop-blur-sm bg-white/30 border border-base-200 rounded-lg p-4 space-y-8">
      {/* Metrics Summary */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Total Responses</div>
          <div className="text-lg font-semibold text-base-content">{insights.totalResponses}</div>
        </div>
        <div className="bg-base-200/50 rounded-lg p-3">
          <div className="text-xs text-base-content/70">Response Rate</div>
          <div className="text-lg font-semibold text-base-content">
            {insights.responseRate?.toFixed(1)}%
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="tabs tabs-boxed bg-base-200 p-1 mb-4">
        <button
          className={`tab ${activeTab === 'sentiment' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('sentiment')}
        >
          Sentiment
        </button>
        <button
          className={`tab ${activeTab === 'themes' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('themes')}
        >
          Common Themes
        </button>
        <button
          className={`tab ${activeTab === 'insights' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('insights')}
        >
          Key Insights
        </button>
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {activeTab === 'sentiment' && (
          <div className="space-y-4">
            <BarChart
              data={chartData}
              index="name"
              categories={['responses']}
              colors={["primary"]}
              valueFormatter={(value) => `${value}`}
              customTooltip={CustomTooltip}
              layout="vertical"
              className="h-72 text-xs [&_.tremor-BarChart-bar]:fill-[url(#bar-gradient)]"
              showLegend={false}
              showGridLines={false}
              yAxisWidth={48}
            >
              <defs>
                <linearGradient id="bar-gradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="10%" stopColor="var(--gradient-from)" />
                  <stop offset="60%" stopColor="var(--gradient-to)" />
                </linearGradient>
              </defs>
            </BarChart>
          </div>
        )}

        {activeTab === 'themes' && (
          <div className="space-y-6">
            {openEndedAnalysis?.commonThemes?.map((theme, idx) => (
              <div 
                key={idx} 
                className="relative overflow-hidden rounded-lg border border-base-300/10 bg-base-200/50 p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                      <span className="text-lg font-semibold text-primary">
                        {idx + 1}
                      </span>
                    </div>
                    <h3 className="font-medium text-base-content">
                      {theme.theme}
                    </h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-base-content">
                      {theme.count}
                    </span>
                    <span className="text-xs text-base-content/70">responses</span>
                  </div>
                </div>
                
                {/* Progress bar */}
                <div className="mt-3 h-2 w-full overflow-hidden rounded-full bg-base-300">
                  <div 
                    className="h-full bg-primary transition-all duration-500 ease-in-out"
                    style={{ 
                      width: `${(theme.count / openEndedAnalysis.commonThemes.reduce((acc, t) => acc + t.count, 0) * 100)}%`,
                      backgroundImage: 'linear-gradient(90deg, var(--gradient-from), var(--gradient-to))'
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'insights' && (
          <div className="space-y-6">
            {openEndedAnalysis?.keyInsights?.map((insight, idx) => (
              <div 
                key={idx}
                className="group relative overflow-hidden rounded-lg border border-base-300/10 bg-base-200/50 p-4 transition-all hover:bg-base-200"
              >
                <div className="space-y-3">
                  {/* Main insight */}
                  <div className="flex space-x-3">
                    <div className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <span className="text-sm font-medium text-primary">{idx + 1}</span>
                    </div>
                    <p className="text-sm text-base-content">{insight.insight}</p>
                  </div>

                  {/* Example responses collapsible section */}
                  {insight.example_responses?.length > 0 && (
                    <div className="ml-9">
                      <details className="collapse bg-base-100/50">
                        <summary className="collapse-title text-xs font-medium text-base-content/70 hover:text-base-content">
                          View {insight.example_responses.length} example responses
                        </summary>
                        <div className="collapse-content">
                          <ul className="space-y-2 text-sm">
                            {insight.example_responses.map((example, exIdx) => (
                              <li 
                                key={exIdx}
                                className="rounded-md bg-base-200/50 p-2 text-xs text-base-content/80"
                              >
                                "{example}"
                              </li>
                            ))}
                          </ul>
                        </div>
                      </details>
                    </div>
                  )}
                </div>

                {/* Decorative gradient line */}
                <div 
                  className="absolute inset-x-0 bottom-0 h-0.5"
                  style={{
                    backgroundImage: 'linear-gradient(90deg, var(--gradient-from), var(--gradient-to))'
                  }}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default OpenEndedQuestionAnalysis 