import { PaperAirplaneIcon, SparklesIcon } from '@heroicons/react/24/outline'

export const InputBar = ({ message, onChange, onSend, isLoading }) => (
  <div className="p-4">
    <div className="flex items-center gap-2 max-w-4xl mx-auto relative group">
      {/* AI Magic Gradient Border */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary via-secondary to-primary 
                      rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-1000 
                      group-hover:duration-200 animate-gradient-shift"></div>
      
      <div className="relative flex w-full items-center gap-2 bg-base-100 rounded-lg p-1">
        {/* AI Icon */}
        <SparklesIcon className="h-4 w-4 ml-2 text-primary animate-pulse" />
        
        <input
          type="text"
          value={message}
          onChange={onChange}
          onKeyPress={(e) => e.key === 'Enter' && onSend()}
          placeholder="Ask surveysort research assistant about your survey responses..."
          className="input input-ghost input-sm w-full text-sm bg-transparent focus:outline-none 
                     placeholder:text-primary/60"
          disabled={isLoading}
        />
        
        <button 
          className="btn btn-primary btn-sm rounded-full group-hover:scale-105 transition-transform
                     duration-200 ease-in-out"
          onClick={onSend}
          disabled={isLoading || message.trim() === ''}
        >
          <PaperAirplaneIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
) 