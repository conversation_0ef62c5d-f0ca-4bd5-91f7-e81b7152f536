export const SuggestionBar = ({ suggestions = [], onSuggestionClick, isLoading = false }) => {
  console.log(suggestions)
  if (isLoading) {
    return (
      <div className="flex overflow-x-auto gap-2 px-4 py-2 bg-base-100/30 backdrop-blur-md border-t border-base-300 hide-scrollbar">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="skeleton bg-base-300 h-7 w-32 rounded-full shrink-0"></div>
        ))}
      </div>
    )
  }

  return (
    <div className="flex overflow-x-auto gap-2 px-4 py-2 bg-primary/10 backdrop-blur-md border-t border-primary-300/30 hide-scrollbar">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          onClick={() => onSuggestionClick(suggestion)}
          className="btn btn-xs bg-base-100/70 hover:bg-base-300/70 text-base-content border-base-300/30 
                     whitespace-nowrap shrink-0 rounded-full normal-case font-normal backdrop-blur-sm"
        >
          {suggestion}
        </button>
      ))}
    </div>
  )
} 