import { useState, useEffect, useRef } from 'react'
import { useMutation, useQuery } from '@redwoodjs/web'
import { debounce } from 'lodash'

import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'

import { EmptyState } from './EmptyState'
import { MessageBubble } from './MessageBubble'
import { LoadingBubble } from './LoadingBubble'
import { SuggestionBar } from './SuggestionBar'
import { InputBar } from './InputBar'
import { AnalyticsPanel } from './AnalyticsPanel'
import { formatTimestamp } from './config'
import { QUERY, GET_NEW_MESSAGES, SEND_MESSAGE, GET_SUGGESTIONS } from './SRInsightsConvoCell.queries'

export const Loading = () => <div className="flex items-center justify-center h-64"><div className="loading loading-lg"></div></div>

export const Empty = ({ surveyId, accountId }) => {
  return <Success surveyInsightConversation={{ messages: [] }} surveyId={surveyId} accountId={accountId} />
}

export const Failure = ({ error }) => (
  <div className="flex items-center justify-center h-64">
    <div className="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>Error: {error?.message}</span>
    </div>
  </div>
)

export const Success = ({ surveyInsightConversation, surveyId, accountId }) => {
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState(surveyInsightConversation?.messages || [])
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date().toISOString())
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [isPolling, setIsPolling] = useState(false)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const chatContainerRef = useRef(null)
  const messagesEndRef = useRef(null)
  const [suggestions, setSuggestions] = useState([])
  const [isSuggestionsLoading, setIsSuggestionsLoading] = useState(true)

  const { refetch: getNewMessages } = useQuery(GET_NEW_MESSAGES, {
    variables: { surveyId, since: lastUpdateTime },
    fetchPolicy: 'network-only',
    skip: true,
    onCompleted: (data) => {
      if (data.newMessages.length > 0) {
        setMessages((prevMessages) => [...prevMessages, ...data.newMessages])
        setLastUpdateTime(data.newMessages[data.newMessages.length - 1].timestamp)
      }
    },
    onError: (error) => {
      setError(error.message)
      setIsPolling(false)
    },
  })

  const [sendMessage] = useMutation(SEND_MESSAGE, {
    onCompleted: (data) => {
      setMessages(data.sendMessage.messages)
      setLastUpdateTime(data.sendMessage.messages[data.sendMessage.messages.length - 1].timestamp)
      setIsLoading(false)
      setIsPolling(true)
      setError(null)
    },
    onError: (error) => {
      setError(error.message)
      setIsLoading(false)
    },
  })

  const { loading: suggestionsLoading } = useQuery(GET_SUGGESTIONS, {
    variables: { surveyId },
    onCompleted: (data) => {
      setSuggestions(data.assistantSuggestions.suggestions.map(s => s.suggestion))
      setIsSuggestionsLoading(false)
    },
    onError: (error) => {
      console.warn('Error fetching suggestions:', error)
      setIsSuggestionsLoading(false)
    }
  })

  useEffect(() => {
    let interval
    if (isPolling) {
      interval = setInterval(() => {
        getNewMessages()
      }, 5000)
    }
    return () => clearInterval(interval)
  }, [getNewMessages, isPolling])

  useEffect(() => {
    if (messages.length > 0 && messages[messages.length - 1].sender === 'user') {
      setIsPolling(true)
    } else {
      setIsPolling(false)
    }
  }, [messages])

  useEffect(() => {
    scrollToBottom()
  }, [messages, isLoading])

  useEffect(() => {
    setIsSuggestionsLoading(suggestionsLoading)
  }, [suggestionsLoading])

  const scrollToBottom = () => {
    chatContainerRef.current?.scrollTo({
      top: chatContainerRef.current.scrollHeight,
      behavior: 'smooth',
    })
  }

  const handleScroll = debounce(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current
      setShowScrollButton(scrollHeight - scrollTop - clientHeight > 100)
    }
  }, 200)

  const handleSend = async () => {
    if (message.trim() === '' || isLoading) return
    
    const userMessage = {
      sender: 'user',
      text: message.trim(),
      timestamp: new Date().toISOString(),
      responseType: 'text'
    }
    
    setMessage('')
    setIsLoading(true)
    setMessages(prevMessages => [...prevMessages, userMessage])
    scrollToBottom()

    try {
      await sendMessage({ 
        variables: { 
          surveyId, 
          message: userMessage.text 
        } 
      })
      setIsPolling(true)
    } catch (error) {
      setMessages(prevMessages => 
        prevMessages.filter(msg => msg.timestamp !== userMessage.timestamp)
      )
      setError(error.message)
      toast.error('Failed to send message. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }
  console.log({messages})
  return (
    <div className="h-full flex bottom-0 flex-col">
      <div 
        ref={chatContainerRef}
        className="flex-1 min-h-0 overflow-y-auto px-4 py-4"
        onScroll={handleScroll}
      >
        {messages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="space-y-6 max-w-4xl mx-auto">
            {messages.map((msg, index) => (
              <div key={index}>
                <MessageBubble 
                  message={msg} 
                  timestamp={formatTimestamp(msg.timestamp)} 
                />
                {msg.sender === 'assistant' && (
                  ['question_analytics', 'response_analytics', 'survey_analytics'].includes(msg.responseType) && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      className="mt-3"
                    >
                      <AnalyticsPanel message={msg} />
                    </motion.div>
                  )
                )}
              </div>
            ))}
            {isLoading && <LoadingBubble />}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      <div className="shrink-0 bg-base-100/50">
        <SuggestionBar 
          suggestions={suggestions}
          onSuggestionClick={(suggestion) => setMessage(suggestion)}
          isLoading={isSuggestionsLoading}
        />
        <div className="border-t border-primary/20">
          <InputBar
            message={message}
            onChange={(e) => setMessage(e.target.value)}
            onSend={handleSend}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  )
}

const SRInsightsConvoCell = ({ surveyId, accountId }) => {
  const { loading, error, data } = useQuery(QUERY, {
    variables: { surveyId },
  })

  if (loading) return <Loading />
  if (error) return <Failure error={error} />
  if (!data?.surveyInsightConversation) return <Empty surveyId={surveyId} accountId={accountId} />

  return (
    <Success 
      surveyInsightConversation={data.surveyInsightConversation}
      surveyId={surveyId}
      accountId={accountId}
    />
  )
}

export default SRInsightsConvoCell