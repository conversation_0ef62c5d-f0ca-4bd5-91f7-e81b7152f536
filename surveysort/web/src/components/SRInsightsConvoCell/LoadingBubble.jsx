import { motion } from 'framer-motion'

export const LoadingBubble = () => (
  <motion.div 
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="chat chat-start"
  >
    <div className="chat-bubble bg-primary/90 text-primary-content text-sm">
      <span className="inline-flex items-center gap-2">
        <span className="loading loading-dots loading-xs"></span>
        Analyzing...
      </span>
    </div>
  </motion.div>
) 