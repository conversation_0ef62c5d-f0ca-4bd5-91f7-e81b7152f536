import ReactMarkdown from 'react-markdown'
import { QuestionAnalyticsView } from './QuestionAnalyticsView'
import { ResponseAnalyticsView } from './ResponseAnalyticsView'
import { SurveyAnalyticsView } from './SurveyAnalyticsView'

export const AnalyticsPanel = ({ message }) => {
  const { responseType } = message


  return (
    <div className="space-y-4">
      {/* Analytics Views */}
      {responseType === 'question_analytics' && message?.questionAnalytics && (
        <QuestionAnalyticsView data={message.questionAnalytics} />
      )}

      {responseType === 'response_analytics' && message?.responseList && (
        <ResponseAnalyticsView responseList={message.responseList} />
      )}

      {['survey_overview', 'general_survey_question', 'advanced_survey_question'].includes(responseType) && 
        message.surveyAnalytics && (
          <SurveyAnalyticsView 
            data={message.surveyAnalytics} 
            intentType={intentType}
          />
      )}
    </div>
  )
} 