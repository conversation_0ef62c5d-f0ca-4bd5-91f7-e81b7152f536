import { ResponseTable } from 'src/components/UnifiedResponseAnalyticsCell/ResponseTable'

export const ResponseAnalyticsView = ({ responseList }) => {
  const responses = responseList?.responses || []
  const pageInfo = {
    totalCount: responseList?.total_count || 0,
    currentPage: responseList?.page || 1,
    totalPages: Math.ceil((responseList?.total_count || 0) / (responseList?.page_size || 5))
  }

  return (
    <div className="space-y-4">

      {/* Reuse ResponseTable */}
      <ResponseTable 
        responses={responses}
        pageInfo={pageInfo}
        hidePagination={true}
        onPageChange={() => {}}
        page={1}
      />
    </div>
  )
} 