import { renderVisualization } from 'src/components/QuestionAnalyticsCell/QuestionAnalyticsCell'

export const QuestionAnalyticsView = ({ data }) => (
  <div className="space-y-4">
    {data.questions.map((question) => (
      <div key={question.questionId} className="rounded-lg bg-base-200/50 p-4">
        <h4 className="text-sm font-medium mb-3">{question.title}</h4>
        {renderVisualization(question, 'ALL')}
      </div>
    ))}
  </div>
) 