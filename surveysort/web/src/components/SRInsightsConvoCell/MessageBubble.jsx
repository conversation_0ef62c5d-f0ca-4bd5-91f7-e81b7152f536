import ReactMarkdown from 'react-markdown'
import { SparklesIcon, UserCircleIcon } from '@heroicons/react/24/solid'

export const MessageBubble = ({ message, timestamp }) => (
  <div className={`chat ${message.sender === 'user' ? 'chat-end' : 'chat-start'}`}>
    <div className="chat-header text-xs text-base-content/50">
      <div className="inline-flex items-center gap-2">
        {message.sender === 'assistant' && (
          <SparklesIcon className="h-4 w-4 text-primary" />
        )}
        {message.sender === 'user' ? 'You' : 'Assistant'}
        <time className="ml-1">{timestamp}</time>
      </div>
    </div>
    
    {message.sender === 'user' && (
      <div className="chat-image avatar">
        <div className="w-10 rounded-full">
          <UserCircleIcon className="text-base-content/70" />
        </div>
      </div>
    )}

    <div className={`chat-bubble text-sm ${
      message.sender === 'user' 
        ? 'bg-base-200 text-base-content py-2 px-3' 
        : 'bg-gradient-to-br bg-primary/10 from-primary/10 via-primary/5 to-transparent backdrop-blur-sm'
    }`}>
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown>{message.text}</ReactMarkdown>
      </div>
    </div>
  </div>
) 