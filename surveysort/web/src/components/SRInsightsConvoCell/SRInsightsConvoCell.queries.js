import { gql } from '@redwoodjs/web'

const QUERY = gql`
  query GetSurveyInsightConversation($surveyId: String!) {
    surveyInsightConversation(surveyId: $surveyId) {
      id
      messages {
        sender
        text
        timestamp
        responseType
        response
        responseList {
          responses {
            id
            surveyId
            submissionTime
            participantEmail
            questionResponses {
              questionId
              response
              title
              type
            }
            authenticityScore
            overallResponseQualityScore
            timeSpentOnSurvey
            isDuplicate
            isHighQuality
            isLowEffort
            isIrrelevant
            vpnDetected
            proxyDetected
            copyPastedDetected
            straightLiningDetected
            submittedTooFast
            submittedCountry
            submittedCity
            deviceInfo {
              browser
              os
              device
              screenResolution
            }
            questionStatuses
            isGradingComplete
          }
          total_count
          page
          page_size
        }
        metadata {
          surveyId
          intentType
          timestamp
          questionIds
          timeframe
          analysisType
          metrics
          appliedFilters
        }
        questionAnalytics {
          questions {
            questionId
            questionType
            title
            responseRate
            chartType
            chartData {
              data {
                category
                value
                percentage
                label
              }
              insights {
                totalResponses
                responseRate
                averageRating
                maxRating
                mostSelected {
                  category
                  value
                  percentage
                }
                label
              }
              openEndedAnalysis {
                sentimentDistribution {
                  category
                  value
                  percentage
                  label
                }
                keyInsights {
                  insight
                  example_responses
                }
                commonThemes {
                  theme
                  count
                }
              }
            }
          }
        }
        surveyAnalytics {
          metrics
          timeSeries
        }
     
      }
    }
  }
`

const GET_NEW_MESSAGES = gql`
  query GetNewMessages($surveyId: String!, $since: DateTime!) {
    newMessages(surveyId: $surveyId, since: $since) {
      sender
      text
      timestamp
      responseType
      response
      responseList {
        responses {
          id
          surveyId
          submissionTime
          participantEmail
          questionResponses {
            questionId
            response
            title
            type
          }
          authenticityScore
          overallResponseQualityScore
          timeSpentOnSurvey
          isDuplicate
          isHighQuality
          isLowEffort
          isIrrelevant
          vpnDetected
          proxyDetected
          copyPastedDetected
          straightLiningDetected
          submittedTooFast
          submittedCountry
          submittedCity
          deviceInfo {
            browser
            os
            device
            screenResolution
          }
          questionStatuses
          isGradingComplete
        }
        total_count
        page
        page_size
      }
      metadata {
        surveyId
        intentType
        timestamp
        questionIds
        timeframe
        analysisType
        metrics
        appliedFilters
      }
      questionAnalytics {
        questions {
          questionId
          questionType
          title
          responseRate
          chartType
          chartData {
            data {
              category
              value
              percentage
              label
            }
            insights {
              totalResponses
              responseRate
              averageRating
              maxRating
              mostSelected {
                category
                value
                percentage
              }
              label
            }
            openEndedAnalysis {
              sentimentDistribution {
                category
                value
                percentage
                label
              }
              keyInsights {
                insight
                example_responses
              }
              commonThemes {
                theme
                count
              }
            }
          }
        }
      }
      surveyAnalytics {
        metrics
        timeSeries
      }
    }
  }
`

const SEND_MESSAGE = gql`
  mutation SendMessage($surveyId: String!, $message: String!) {
    sendMessage(surveyId: $surveyId, message: $message) {
      id
      messages {
        sender
        text
        timestamp
        responseType
        response
        responseList {
          responses {
            id
            surveyId
            submissionTime
            participantEmail
            questionResponses {
              questionId
              response
              title
              type
            }
            authenticityScore
            overallResponseQualityScore
            timeSpentOnSurvey
            isDuplicate
            isHighQuality
            isLowEffort
            isIrrelevant
            vpnDetected
            proxyDetected
            copyPastedDetected
            straightLiningDetected
            submittedTooFast
            submittedCountry
            submittedCity
            deviceInfo {
              browser
              os
              device
              screenResolution
            }
            questionStatuses
            isGradingComplete
          }
          total_count
          page
          page_size
        }
        metadata {
          surveyId
          intentType
          timestamp
          questionIds
          timeframe
          analysisType
          metrics
          appliedFilters
        }
        questionAnalytics {
          questions {
            questionId
            questionType
            title
            responseRate
            chartType
            chartData {
              data {
                category
                value
                percentage
                label
              }
              insights {
                totalResponses
                responseRate
                averageRating
                maxRating
                mostSelected {
                  category
                  value
                  percentage
                }
                label
              }
              openEndedAnalysis {
                sentimentDistribution {
                  category
                  value
                  percentage
                  label
                }
              keyInsights {
                insight
                example_responses
              }
              commonThemes {
                theme
                count
              }
              }
            }
          }
        }
        surveyAnalytics {
          metrics
          timeSeries
        }
      }
    }
  }
`

const GET_SUGGESTIONS = gql`
  query GetSuggestions($surveyId: String!) {
    assistantSuggestions(surveyId: $surveyId) {
      suggestions {
        suggestion
      }
    }
  }
`

export { QUERY, GET_NEW_MESSAGES, SEND_MESSAGE, GET_SUGGESTIONS } 