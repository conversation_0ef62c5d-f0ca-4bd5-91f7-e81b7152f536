import { useAuth } from 'src/auth'
import { ClockIcon } from '@heroicons/react/24/outline'

export const TrialBanner = () => {
  const { currentUser } = useAuth()
  
  if (!currentUser?.subscription?.onTrial) return null;

  const daysLeft = currentUser.subscription.trialDaysLeft;
  const isEnding = daysLeft <= 3;

  return (
    <div className={`${isEnding ? 'bg-warning' : 'bg-primary'} relative z-40`}>
      <div className="mx-auto max-w-7xl py-2 px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ClockIcon className="h-5 w-5 text-white" />
            <p className="ml-3 text-sm text-white">
              {isEnding ? 
                `Your trial ends in ${daysLeft} days! Choose a plan to continue.` :
                `Try all features free for ${daysLeft} more days`
              }
            </p>
          </div>
          <button
            onClick={() => setIsPricingOpen(true)}
            className="btn btn-sm btn-ghost text-white"
          >
            Choose Plan
          </button>
        </div>
      </div>
    </div>
  )
}

export default TrialBanner 