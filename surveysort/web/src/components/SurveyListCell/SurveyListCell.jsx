import { useState } from 'react'
import { navigate, routes } from '@redwoodjs/router'

import { EllipsisVerticalIcon, MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/20/solid'
import { motion, AnimatePresence } from 'framer-motion'
import classNames from 'classnames'

export const QUERY = gql`
  query SurveyListQuery($search: String) {
    surveys(search: $search) {
      id
      title
      audience
      surveyObjective
      status
      source
      isDeleted
      isSuspended
      createdAt
      totalResponses
      completionRate
      createdBy
      account {
        name
      }
    }
  }
`

export const Loading = () => (
  <div className="p-4 space-y-6">
    <div className="flex justify-between items-center">
      <div className="space-y-2">
        <div className="skeleton h-6 w-32"></div>
        <div className="skeleton h-4 w-48"></div>
      </div>
      <div className="flex items-center gap-2">
        <div className="skeleton w-72 h-8"></div>
        <div className="skeleton w-32 h-8"></div>
      </div>
    </div>

    <div className="mt-3 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <div key={i} className="border border-base-300 rounded-lg overflow-hidden">
          <div className="p-4 border-b border-base-300 bg-base-200/50">
            <div className="flex items-center gap-x-4">
              <div className="skeleton w-10 h-10 rounded-lg"></div>
              <div className="flex-1">
                <div className="skeleton h-4 w-3/4 mb-2"></div>
                <div className="skeleton h-3 w-1/2"></div>
              </div>
            </div>
          </div>
          <div className="p-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <div className="skeleton h-3 w-24"></div>
                <div className="skeleton h-3 w-12"></div>
              </div>
              <div className="flex justify-between">
                <div className="skeleton h-3 w-24"></div>
                <div className="skeleton h-3 w-12"></div>
              </div>
              <div className="flex justify-between">
                <div className="skeleton h-3 w-24"></div>
                <div className="skeleton h-3 w-16"></div>
              </div>
            </div>
          </div>
          <div className="border-t border-base-200 p-4">
            <div className="grid grid-cols-2 gap-2">
              <div className="skeleton h-6"></div>
              <div className="skeleton h-6"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
)

export const Empty = ({ onCreateSurvey }) => (
  <div className="flex flex-col items-center justify-center py-12">
    <div className="rounded-full bg-base-200 p-3">
      <svg
        className="h-8 w-8 text-base-content/50"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
        />
      </svg>
    </div>
    <h3 className="mt-4 text-sm font-medium text-base-content">No surveys found</h3>
    <p className="mt-1 text-sm text-base-content/70">
      Get started by creating your first survey
    </p>
    <div className="mt-6">
      <button
        onClick={onCreateSurvey}
        type="button"
        className="btn btn-primary btn-sm gap-2"
      >
        <PlusIcon className="h-4 w-4" />
        Create Survey
      </button>
    </div>
  </div>
)

export const Success = ({ surveys, onCreateSurvey }) => {

  const [searchTerm, setSearchTerm] = useState('')



  const displaySurveys = surveys?.map(survey => ({
    ...survey,
    totalResponses: survey.totalResponses || 0,
    completionRate: survey.completionRate || '0%',
    status: survey.status || 'DRAFT'
  }))

  const filteredSurveys = displaySurveys?.filter(survey => 
    survey.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    survey.audience.toLowerCase().includes(searchTerm.toLowerCase()) ||
    survey.surveyObjective.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const getStatusColor = (status) => {
    const colors = {
      DRAFT: 'bg-warning',
      PUBLISHED: 'bg-success',
      COMPLETED: 'bg-neutral',
    }
    return colors[status] || 'bg-base-300'
  }

  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold tracking-tight">Your Surveys</h2>
          <p className="mt-1 text-sm text-base-content/70">
            Create and manage your survey projects
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-4 w-4 text-base-content/50" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search surveys..."
              className="input input-bordered input-sm pl-10 pr-4 w-72"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div
        
          >
            <button
              onClick={onCreateSurvey}
              className={`btn btn-primary btn-sm gap-2 `}
            >
              <PlusIcon className="h-4 w-4" />
              Create Survey
            </button>
          </div>
        </div>
      </div>

      <AnimatePresence>
        <motion.ul
          role="list"
          className="mt-3 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3"
          initial="hidden"
          animate="show"
          variants={{
            hidden: { opacity: 0 },
            show: {
              opacity: 1,
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {filteredSurveys.map((survey) => (
            <motion.li
              key={survey.id}
              variants={{
                hidden: { opacity: 0, y: 20 },
                show: { opacity: 1, y: 0 }
              }}
              className="overflow-hidden rounded-lg border border-base-300 bg-base-100"
            >
              <div className="flex items-center gap-x-4 border-b border-base-300 bg-base-200/50 p-4">
                <div 
                  className={classNames(
                    getStatusColor(survey.status),
                    'flex w-10 h-10 shrink-0 items-center justify-center rounded-lg text-base-100'
                  )}
                >
                  {survey.title.substring(0, 2).toUpperCase()}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="text-sm font-medium text-base-content truncate">{survey.title}</div>
                  <div className="flex items-center gap-2 text-xs text-base-content/70">
                    <span className="truncate">{survey.audience}</span>
                    <span className="text-base-content/50">•</span>
                    <span>{survey.createdBy}</span>
                  </div>
                </div>
              </div>

              <dl className="divide-y divide-base-200 px-4 py-2 text-sm">
                <div className="flex justify-between gap-x-4 py-1.5">
                  <dt className="text-base-content/60">Total Responses</dt>
                  <dd className="text-base-content/70">{survey.totalResponses}</dd>
                </div>
                <div className="flex justify-between gap-x-4 py-1.5">
                  <dt className="text-base-content/60">Completion Rate</dt>
                  <dd className="text-base-content/70">{survey.completionRate}</dd>
                </div>
                <div className="flex justify-between gap-x-4 py-1.5">
                  <dt className="text-base-content/60">Status</dt>
                  <dd>
                    <span className={classNames(
                      'inline-flex items-center rounded-md px-1.5 py-0.5 text-[10px] font-medium ring-1 ring-inset',
                      survey.status === 'PUBLISHED' ? 'bg-success/10 text-success ring-success/20' :
                      survey.status === 'DRAFT' ? 'bg-warning/10 text-warning ring-warning/20' :
                      'bg-neutral/10 text-neutral-content ring-neutral/20'
                    )}>
                      {survey.status.charAt(0).toUpperCase() + survey.status.slice(1).toLowerCase()}
                    </span>
                  </dd>
                </div>
              </dl>

              <div className="border-t border-base-200 bg-base-100 p-4">
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => navigate(routes.surveyDetail({ id: survey.id, tab: 'details' }))}
                    className="btn btn-outline btn-xs"
                  >
                    Details
                  </button>
                  <button
                    onClick={() => navigate(routes.surveyDetail({ id: survey.id, tab: 'insights' }))}
                    className="btn btn-outline btn-primary btn-xs"
                  >
                    Insights
                  </button>
                </div>
              </div>
            </motion.li>
          ))}
        </motion.ul>
      </AnimatePresence>
    </div>
  )
}
