// Define your own mock data here:
export const standard = (/* vars, { ctx, req } */) => {
  const surveys = [
    {
      id: 1,
      surveyName: 'Customer Satisfaction Survey',
      createdBy: '<PERSON>',
      createdOn: '2024-01-15',
      subheading: 'Feedback on customer service',
      numberOfResponses: 120,
      isActive: true,
    },
    {
      id: 2,
      surveyName: 'Product Feedback Survey',
      createdBy: '<PERSON>',
      createdOn: '2024-02-20',
      subheading: 'Opinions on new product features',
      numberOfResponses: 75,
      isActive: false,
    },
    {
      id: 3,
      surveyName: 'Employee Engagement Survey',
      createdBy: '<PERSON>',
      createdOn: '2024-03-10',
      subheading: 'Workplace satisfaction and engagement',
      numberOfResponses: 45,
      isActive: true,
    },
    {
      id: 4,
      surveyName: 'Market Research Survey',
      createdBy: '<PERSON>',
      createdOn: '2024-04-05',
      subheading: 'Insights on market trends',
      numberOfResponses: 200,
      isActive: false,
    },
    {
      id: 5,
      surveyName: 'Event Feedback Survey',
      createdBy: '<PERSON>',
      createdOn: '2024-05-22',
      subheading: 'Attendee feedback on recent event',
      numberOfResponses: 60,
      isActive: true,
    },
    // Add more entries to simulate multiple pages
  ]

  // Paginate the data
  const page = 1 // This should be dynamically set based on query parameters
  const surveysPerPage = 2
  const offset = (page - 1) * surveysPerPage
  const paginatedSurveys = surveys.slice(offset, offset + surveysPerPage)

  return {
    surveyList: {
      surveys: paginatedSurveys,
      count: surveys.length,
    },
  }
}
