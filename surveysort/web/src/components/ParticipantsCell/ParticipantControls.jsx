import { MagnifyingGlassIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'

const ParticipantControls = ({
  searchQuery,
  setSearchQuery,
  handleSearch,
  statusFilter,
  handleStatusChange,
  handleClearFilters,
  handleGenerateLinks,
  generatingLinks,
}) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
      <div className="flex-1">
        <form onSubmit={handleSearch} className="relative flex">
          <input
            type="text"
            placeholder="Search by email or name..."
            className="input input-bordered w-full pr-10 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button
            type="submit"
            className="absolute right-0 top-0 btn btn-ghost h-full"
          >
            <MagnifyingGlassIcon className="h-5 w-5" />
          </button>
        </form>
      </div>
      
      <div className="flex gap-2">
        <select
          className="select select-bordered text-sm"
          value={statusFilter}
          onChange={handleStatusChange}
        >
          <option value="">All statuses</option>
          <option value="PENDING">Pending</option>
          <option value="SENT">Sent</option>
          <option value="CLICKED">Clicked</option>
          <option value="COMPLETED">Completed</option>
          <option value="BOUNCED">Bounced</option>
          <option value="UNSUBSCRIBED">Unsubscribed</option>
        </select>
        
        {(searchQuery || statusFilter) && (
          <button
            className="btn btn-ghost btn-sm"
            onClick={handleClearFilters}
          >
            Clear Filters
          </button>
        )}
        
        <div className="dropdown dropdown-end">
          <label tabIndex={0} className="btn">
            <AdjustmentsHorizontalIcon className="h-5 w-5 mr-1" />
            Actions
          </label>
          <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
            <li>
              <button 
                onClick={() => handleGenerateLinks()}
                disabled={generatingLinks}
              >
                Generate All Links
              </button>
            </li>
            <li>
              <button 
                onClick={() => handleGenerateLinks('PENDING')}
                disabled={generatingLinks}
              >
                Generate Links for Pending
              </button>
            </li>
            <li>
              <button onClick={() => document.getElementById('upload-contacts-modal').showModal()}>
                Upload Contacts
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default ParticipantControls 