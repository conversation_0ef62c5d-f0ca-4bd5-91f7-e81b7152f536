import { useState } from 'react'

import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

// Import separated components
import Pagination from './Pagination'
import ParticipantControls from './ParticipantControls'
import ParticipantsTable from './ParticipantsTable'
import ParticipantStats from './ParticipantStats'

export const QUERY = gql`
  query ParticipantsQuery(
    $surveyId: String!
    $page: Int
    $perPage: Int
    $filter: ParticipantFilters
  ) {
    participants: listParticipants(
      surveyId: $surveyId
      page: $page
      perPage: $perPage
      filter: $filter
    ) {
      participants {
        id
        email
        name
        status
        firstSentAt
        lastSentAt
        respondedAt
        links {
          id
          token
          expiresAt
          clickedAt
          createdAt
        }
      }
      count
      hasMore
    }
    stats: participantStats(surveyId: $surveyId) {
      total
      pending
      sent
      clicked
      completed
      bounced
      unsubscribed
    }
  }
`

const GENERATE_LINKS = gql`
  mutation GenerateLinks($surveyId: String!, $segment: SegmentInput) {
    generateLinks(surveyId: $surveyId, segment: $segment)
  }
`

const DELETE_PARTICIPANT = gql`
  mutation DeleteParticipant($id: String!) {
    deleteParticipant(id: $id) {
      id
      email
    }
  }
`

export const Loading = () => (
  <div className="p-4">
    <div className="mb-6 flex justify-between">
      <div className="skeleton h-10 w-1/3"></div>
      <div className="skeleton h-10 w-32"></div>
    </div>

    <div className="mb-4 flex w-full space-x-2">
      <div className="skeleton h-12 flex-1"></div>
    </div>

    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="skeleton h-16 w-full"></div>
      ))}
    </div>
  </div>
)

export const Empty = ({ surveyId }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <h3 className="mb-2 text-lg font-medium">No participants yet</h3>
      <p className="mb-6 text-sm text-base-content/70">
        Upload a CSV of participants to get started
      </p>
      <button
        className="btn btn-primary btn-sm"
        onClick={() =>
          document.getElementById('upload-participants-modal').showModal()
        }
      >
        Upload Participants
      </button>
    </div>
  )
}

export const Failure = ({ error }) => (
  <div className="alert alert-error">
    <div className="flex-1">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        className="mx-2 h-6 w-6 stroke-current"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></path>
      </svg>
      <label>Error: {error?.message}</label>
    </div>
  </div>
)

export const Success = ({ participants, stats, surveyId, onDataChange }) => {
  const [page, setPage] = useState(1)
  const [perPage] = useState(20)
  const [filter, setFilter] = useState({})
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [deletingParticipantId, setDeletingParticipantId] = useState(null)

  const [generateLinks, { loading: generatingLinks }] = useMutation(
    GENERATE_LINKS,
    {
      onCompleted: () => {
        toast.success('Links generated successfully')
        // Trigger refresh if callback is provided, otherwise reload page
        if (onDataChange) {
          onDataChange()
        } else {
          window.location.reload()
        }
      },
      onError: (error) => {
        toast.error(`Failed to generate links: ${error.message}`)
      },
    }
  )

  const [deleteParticipant] = useMutation(DELETE_PARTICIPANT, {
    onCompleted: (data) => {
      toast.success(
        `Participant ${data.deleteParticipant.email} deleted successfully`
      )
      setDeletingParticipantId(null)
      // Trigger refresh if callback is provided, otherwise reload page
      if (onDataChange) {
        onDataChange()
      } else {
        window.location.reload()
      }
    },
    onError: (error) => {
      console.error('Delete participant error:', error)
      toast.error(`Failed to delete participant: ${error.message}`)
      setDeletingParticipantId(null)
    },
    errorPolicy: 'all',
  })

  const handleGenerateLinks = (status) => {
    generateLinks({
      variables: {
        surveyId,
        segment: status ? { status } : undefined,
      },
    })
  }

  const handleDelete = (id, email) => {
    if (
      confirm(
        `Are you sure you want to delete participant "${email}"? This action cannot be undone.`
      )
    ) {
      setDeletingParticipantId(id)
      deleteParticipant({
        variables: { id },
      })
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    setFilter({ ...filter, search: searchQuery })
    setPage(1)
  }

  const handleStatusChange = (e) => {
    const newStatus = e.target.value || undefined
    setStatusFilter(newStatus || '')
    setFilter({ ...filter, status: newStatus })
    setPage(1)
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setStatusFilter('')
    setFilter({})
    setPage(1)
  }

  const handlePagination = (newPage) => {
    setPage(newPage)
  }

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'PENDING':
        return 'bg-secondary/10 text-secondary/90'
      case 'SENT':
        return 'bg-primary/10 text-primary/90'
      case 'CLICKED':
        return 'bg-info/10 text-info/90'
      case 'COMPLETED':
        return 'bg-success/10 text-success/90'
      case 'BOUNCED':
        return 'bg-error/10 text-error/90'
      case 'UNSUBSCRIBED':
        return 'bg-warning/10 text-warning/90'
      default:
        return 'bg-neutral/10 text-neutral/90'
    }
  }

  const totalPages = Math.ceil(participants.count / perPage)

  return (
    <div className="p-4">
      {/* Stats */}
      <ParticipantStats stats={stats} />

      {/* Controls */}
      <ParticipantControls
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
        statusFilter={statusFilter}
        handleStatusChange={handleStatusChange}
        handleClearFilters={handleClearFilters}
        handleGenerateLinks={handleGenerateLinks}
        generatingLinks={generatingLinks}
      />

      {/* Participants Table */}
      <ParticipantsTable
        participants={participants.participants}
        surveyId={surveyId}
        onGenerateLink={handleGenerateLinks}
        onDelete={handleDelete}
        getStatusBadgeClass={getStatusBadgeClass}
        deletingParticipantId={deletingParticipantId}
      />

      {/* Pagination */}
      <Pagination
        currentPage={page}
        totalPages={totalPages}
        onPageChange={handlePagination}
      />
    </div>
  )
}
