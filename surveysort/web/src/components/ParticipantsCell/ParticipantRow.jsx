import { useState } from 'react'

import {
  ChevronRightIcon,
  ArrowPathIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'

import { toast } from '@redwoodjs/web/toast'

const ParticipantRow = ({
  participant,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  isDeleting,
}) => {
  const [expanded, setExpanded] = useState(false)
  const p = participant

  return (
    <>
      <tr className={`hover ${expanded ? 'bg-base-200/30' : ''}`}>
        <td>
          <button
            className="btn btn-ghost btn-xs"
            onClick={() => setExpanded(!expanded)}
          >
            <ChevronRightIcon
              className={`h-4 w-4 transition-transform ${expanded ? 'rotate-90' : ''}`}
            />
          </button>
        </td>
        <td>{p.email}</td>
        <td>{p.name || '—'}</td>
        <td>
          <span
            className={`rounded-full px-2 py-1 text-xs ${getStatusBadgeClass(p.status)}`}
          >
            {p.status}
          </span>
        </td>
        <td>
          {p.respondedAt ? (
            <span
              className="text-success"
              title={new Date(p.respondedAt).toLocaleString()}
            >
              Responded{' '}
              {formatDistanceToNow(new Date(p.respondedAt), {
                addSuffix: true,
              })}
            </span>
          ) : p.lastSentAt ? (
            <span title={new Date(p.lastSentAt).toLocaleString()}>
              Email sent{' '}
              {formatDistanceToNow(new Date(p.lastSentAt), { addSuffix: true })}
            </span>
          ) : (
            <span className="text-secondary">Never contacted</span>
          )}
        </td>
        <td>
          <div className="flex space-x-2">
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => onGenerateLink(p.status)}
              title="Generate new link"
            >
              <ArrowPathIcon className="h-4 w-4" />
            </button>
            <button
              className="btn btn-ghost btn-xs text-error"
              onClick={() => onDelete(p.id, p.email)}
              title="Delete participant"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <span className="loading loading-spinner loading-xs"></span>
              ) : (
                <TrashIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        </td>
      </tr>

      {/* Modal Overlay */}
      {expanded && (
        <>
          {/* Background Overlay */}
          <div
            className="fixed inset-0 z-40 bg-black/50"
            onClick={() => setExpanded(false)}
          />

          {/* Modal Content */}
          <div className="fixed inset-4 z-50 overflow-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <div className="relative max-h-[90vh] w-full max-w-6xl overflow-auto rounded-xl border border-base-300 bg-base-100 shadow-2xl">
                {/* Close Button */}
                <button
                  className="btn btn-ghost btn-sm absolute right-4 top-4 z-10"
                  onClick={() => setExpanded(false)}
                >
                  ✕
                </button>

                <div className="p-8">
                  <div className="space-y-8">
                    {/* Header */}
                    <div className="border-b border-base-300 pb-4">
                      <h3 className="flex items-center text-2xl font-bold text-base-content">
                        <span className="mr-3">👤</span>
                        Participant Details: {p.email}
                      </h3>
                    </div>

                    {/* Participant Details */}
                    <div>
                      <h4 className="mb-6 text-lg font-semibold text-base-content">
                        Contact Information
                      </h4>
                      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        <div className="space-y-3">
                          <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                            Email Address
                          </p>
                          <div className="rounded-lg bg-base-200 p-4">
                            <p className="break-all font-mono text-sm text-base-content">
                              {p.email}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                            Full Name
                          </p>
                          <div className="rounded-lg bg-base-200 p-4">
                            <p className="text-sm text-base-content">
                              {p.name || 'Not provided'}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                            First Contacted
                          </p>
                          <div className="rounded-lg bg-base-200 p-4">
                            <p className="text-sm text-base-content">
                              {p.firstSentAt
                                ? new Date(p.firstSentAt).toLocaleString()
                                : 'Never contacted'}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                            Last Contacted
                          </p>
                          <div className="rounded-lg bg-base-200 p-4">
                            <p className="text-sm text-base-content">
                              {p.lastSentAt
                                ? new Date(p.lastSentAt).toLocaleString()
                                : 'Never contacted'}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                            Survey Completed
                          </p>
                          <div className="rounded-lg bg-base-200 p-4">
                            <p className="text-sm text-base-content">
                              {p.respondedAt
                                ? new Date(p.respondedAt).toLocaleString()
                                : 'Not completed'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Survey Link Details */}
                    <div>
                      <h4 className="mb-6 flex items-center text-lg font-semibold text-base-content">
                        <span className="mr-3">🔗</span>
                        Survey Link Details
                      </h4>
                      {p.links && p.links.length > 0 ? (
                        <div className="rounded-lg border border-primary/20 bg-gradient-to-r from-primary/5 to-secondary/5 p-6">
                          <div className="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                            <div className="space-y-3">
                              <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                                Link Created
                              </p>
                              <div className="rounded-lg bg-base-100 p-4 shadow-sm">
                                <p className="text-sm text-base-content">
                                  {new Date(
                                    p.links[0].createdAt
                                  ).toLocaleString()}
                                </p>
                              </div>
                            </div>
                            {p.links[0].expiresAt && (
                              <div className="space-y-3">
                                <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                                  Link Expires
                                </p>
                                <div className="rounded-lg bg-base-100 p-4 shadow-sm">
                                  <p className="text-sm text-base-content">
                                    {new Date(
                                      p.links[0].expiresAt
                                    ).toLocaleString()}
                                  </p>
                                </div>
                              </div>
                            )}
                            {p.links[0].clickedAt && (
                              <div className="space-y-3">
                                <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                                  Link Clicked
                                </p>
                                <div className="rounded-lg bg-base-100 p-4 shadow-sm">
                                  <p className="text-sm text-base-content">
                                    {new Date(
                                      p.links[0].clickedAt
                                    ).toLocaleString()}
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="space-y-4">
                            <p className="text-xs font-semibold uppercase tracking-wider text-base-content/60">
                              Survey URL
                            </p>
                            <div className="flex flex-col gap-4 lg:flex-row lg:items-center">
                              <div className="flex-1">
                                <input
                                  type="text"
                                  value={`${window.location.origin}/s/${surveyId}?token=${p.links[0].token}`}
                                  className="input input-bordered w-full bg-base-100 font-mono text-sm shadow-sm"
                                  readOnly
                                />
                              </div>
                              <button
                                className="btn btn-primary shrink-0 lg:btn-lg"
                                onClick={() => {
                                  navigator.clipboard.writeText(
                                    `${window.location.origin}/s/${surveyId}?token=${p.links[0].token}`
                                  )
                                  toast.success(
                                    'Survey link copied to clipboard!'
                                  )
                                }}
                              >
                                <span className="mr-2">📋</span>
                                Copy Survey Link
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="to-orange/5 rounded-lg border border-warning/20 bg-gradient-to-r from-warning/5 p-6">
                          <div className="text-center">
                            <div className="mb-4">
                              <span className="text-4xl">⚠️</span>
                            </div>
                            <h5 className="mb-2 text-lg font-medium text-base-content">
                              No Survey Link Generated
                            </h5>
                            <p className="mb-4 text-sm text-base-content/70">
                              This participant doesn&apos;t have a survey link
                              yet. Generate one to send them the survey.
                            </p>
                            <button
                              className="btn btn-primary"
                              onClick={() => onGenerateLink(p.status)}
                            >
                              <span className="mr-2">🔗</span>
                              Generate Survey Link
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default ParticipantRow
