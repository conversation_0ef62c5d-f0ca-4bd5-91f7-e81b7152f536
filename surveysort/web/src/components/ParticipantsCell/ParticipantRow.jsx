import { useState } from 'react'

import {
  ChevronRightIcon,
  ArrowPathIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'

import { toast } from '@redwoodjs/web/toast'

const ParticipantRow = ({
  participant,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  isDeleting,
}) => {
  const [expanded, setExpanded] = useState(false)
  const p = participant

  return (
    <>
      <tr className="hover">
        <td>
          <button
            className="btn btn-ghost btn-xs"
            onClick={() => setExpanded(!expanded)}
          >
            <ChevronRightIcon
              className={`h-4 w-4 transition-transform ${expanded ? 'rotate-90' : ''}`}
            />
          </button>
        </td>
        <td>{p.email}</td>
        <td>{p.name || '—'}</td>
        <td>
          <span
            className={`rounded-full px-2 py-1 text-xs ${getStatusBadgeClass(p.status)}`}
          >
            {p.status}
          </span>
        </td>
        <td>
          {p.respondedAt ? (
            <span
              className="text-success"
              title={new Date(p.respondedAt).toLocaleString()}
            >
              Responded{' '}
              {formatDistanceToNow(new Date(p.respondedAt), {
                addSuffix: true,
              })}
            </span>
          ) : p.lastSentAt ? (
            <span title={new Date(p.lastSentAt).toLocaleString()}>
              Email sent{' '}
              {formatDistanceToNow(new Date(p.lastSentAt), { addSuffix: true })}
            </span>
          ) : (
            <span className="text-secondary">Never contacted</span>
          )}
        </td>
        <td>
          <div className="flex space-x-2">
            <button
              className="btn btn-ghost btn-xs"
              onClick={() => onGenerateLink(p.status)}
              title="Generate new link"
            >
              <ArrowPathIcon className="h-4 w-4" />
            </button>
            <button
              className="btn btn-ghost btn-xs text-error"
              onClick={() => onDelete(p.id, p.email)}
              title="Delete participant"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <span className="loading loading-spinner loading-xs"></span>
              ) : (
                <TrashIcon className="h-4 w-4" />
              )}
            </button>
          </div>
        </td>
      </tr>

      {expanded && (
        <tr className="bg-base-200/50">
          <td
            colSpan={6}
            className="relative !m-0 !p-0"
            style={{ padding: '0 !important', margin: '0 !important' }}
          >
            <div className="relative w-full max-w-none overflow-visible">
              <div className="mx-4 w-full min-w-0 space-y-6 p-8">
                <div className="w-full">
                  <h4 className="mb-4 border-b border-base-300 pb-2 text-sm font-semibold text-base-content">
                    Participant Details
                  </h4>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
                    <div className="space-y-2">
                      <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                        Email
                      </p>
                      <p className="break-all rounded bg-base-100 p-2 font-mono text-sm text-base-content">
                        {p.email}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                        Name
                      </p>
                      <p className="break-words rounded bg-base-100 p-2 text-sm text-base-content">
                        {p.name || '—'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                        First Sent
                      </p>
                      <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                        {p.firstSentAt
                          ? new Date(p.firstSentAt).toLocaleString()
                          : '—'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                        Last Sent
                      </p>
                      <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                        {p.lastSentAt
                          ? new Date(p.lastSentAt).toLocaleString()
                          : '—'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                        Responded
                      </p>
                      <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                        {p.respondedAt
                          ? new Date(p.respondedAt).toLocaleString()
                          : '—'}
                      </p>
                    </div>
                  </div>
                </div>

                {p.links && p.links.length > 0 && (
                  <div className="w-full">
                    <h4 className="mb-4 border-b border-base-300 pb-2 text-sm font-semibold text-base-content">
                      Latest Survey Link
                    </h4>
                    <div className="rounded-lg bg-base-300 p-6">
                      <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                        <div className="space-y-1">
                          <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                            Created
                          </p>
                          <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                            {new Date(p.links[0].createdAt).toLocaleString()}
                          </p>
                        </div>
                        {p.links[0].expiresAt && (
                          <div className="space-y-1">
                            <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                              Expires
                            </p>
                            <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                              {new Date(p.links[0].expiresAt).toLocaleString()}
                            </p>
                          </div>
                        )}
                        {p.links[0].clickedAt && (
                          <div className="space-y-1">
                            <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                              Clicked
                            </p>
                            <p className="rounded bg-base-100 p-2 text-sm text-base-content">
                              {new Date(p.links[0].clickedAt).toLocaleString()}
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <p className="text-xs font-medium uppercase tracking-wide text-base-content/60">
                          Survey URL
                        </p>
                        <div className="flex flex-col gap-3 lg:flex-row">
                          <div className="min-w-0 flex-1">
                            <input
                              type="text"
                              value={`${window.location.origin}/s/${surveyId}?token=${p.links[0].token}`}
                              className="input input-sm input-bordered w-full bg-base-100 font-mono text-xs"
                              readOnly
                            />
                          </div>
                          <button
                            className="btn btn-primary btn-sm w-full shrink-0 lg:w-auto"
                            onClick={() => {
                              navigator.clipboard.writeText(
                                `${window.location.origin}/s/${surveyId}?token=${p.links[0].token}`
                              )
                              toast.success('Link copied to clipboard')
                            }}
                          >
                            📋 Copy Link
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </td>
        </tr>
      )}
    </>
  )
}

export default ParticipantRow
