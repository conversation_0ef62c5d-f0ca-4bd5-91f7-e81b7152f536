import { useState } from 'react'
import { toast } from '@redwoodjs/web/toast'
import { formatDistanceToNow } from 'date-fns'
import { ChevronRightIcon, ArrowPathIcon, TrashIcon } from '@heroicons/react/24/outline'

const ParticipantRow = ({ participant, surveyId, onGenerateLink, onDelete, getStatusBadgeClass }) => {
  const [expanded, setExpanded] = useState(false)
  const p = participant

  return (
    <>
      <tr className="hover">
        <td>
          <button
            className="btn btn-xs btn-ghost"
            onClick={() => setExpanded(!expanded)}
          >
            <ChevronRightIcon
              className={`h-4 w-4 transition-transform ${expanded ? 'rotate-90' : ''}`}
            />
          </button>
        </td>
        <td>{p.email}</td>
        <td>{p.name || '—'}</td>
        <td>
          <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(p.status)}`}>
            {p.status}
          </span>
        </td>
        <td>
          {p.respondedAt ? (
            <span className="text-success" title={new Date(p.respondedAt).toLocaleString()}>
              Responded {formatDistanceToNow(new Date(p.respondedAt), { addSuffix: true })}
            </span>
          ) : p.lastSentAt ? (
            <span title={new Date(p.lastSentAt).toLocaleString()}>
              Email sent {formatDistanceToNow(new Date(p.lastSentAt), { addSuffix: true })}
            </span>
          ) : (
            <span className="text-secondary">Never contacted</span>
          )}
        </td>
        <td>
          <div className="flex space-x-2">
            <button
              className="btn btn-xs btn-ghost"
              onClick={() => onGenerateLink(p.id)}
              title="Generate new link"
            >
              <ArrowPathIcon className="h-4 w-4" />
            </button>
            <button
              className="btn btn-xs btn-ghost text-error"
              onClick={() => onDelete(p.id)}
              title="Delete"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
      
      {expanded && (
        <tr className="bg-base-200/50">
          <td colSpan={6} className="px-4 py-2">
            <div className="p-4 space-y-4">
              <div>
                <h4 className="font-medium text-sm mb-2">Participant Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-base-content/70">Email</p>
                    <p className="text-sm">{p.email}</p>
                  </div>
                  <div>
                    <p className="text-xs text-base-content/70">Name</p>
                    <p className="text-sm">{p.name || '—'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-base-content/70">First Sent</p>
                    <p className="text-sm">
                      {p.firstSentAt
                        ? new Date(p.firstSentAt).toLocaleString()
                        : '—'}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-base-content/70">Last Sent</p>
                    <p className="text-sm">
                      {p.lastSentAt
                        ? new Date(p.lastSentAt).toLocaleString()
                        : '—'}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-base-content/70">Responded</p>
                    <p className="text-sm">
                      {p.respondedAt
                        ? new Date(p.respondedAt).toLocaleString()
                        : '—'}
                    </p>
                  </div>
                </div>
              </div>
              
              {p.links && p.links.length > 0 && (
                <div>
                  <h4 className="font-medium text-sm mb-2">Latest Link</h4>
                  <div className="bg-base-300 p-2 rounded text-xs overflow-x-auto">
                    <p className="mb-1">
                      <span className="font-medium">Created:</span>{' '}
                      {new Date(p.links[0].createdAt).toLocaleString()}
                    </p>
                    {p.links[0].expiresAt && (
                      <p className="mb-1">
                        <span className="font-medium">Expires:</span>{' '}
                        {new Date(p.links[0].expiresAt).toLocaleString()}
                      </p>
                    )}
                    {p.links[0].clickedAt && (
                      <p className="mb-1">
                        <span className="font-medium">Clicked:</span>{' '}
                        {new Date(p.links[0].clickedAt).toLocaleString()}
                      </p>
                    )}
                    <div className="join w-full mt-2">
                      <input
                        type="text"
                        value={`${window.location.origin}/survey/${surveyId}?token=${p.links[0].token}`}
                        className="input input-bordered join-item input-xs w-full"
                        readOnly
                      />
                      <button
                        className="btn btn-xs join-item"
                        onClick={() => {
                          navigator.clipboard.writeText(
                            `${window.location.origin}/survey/${surveyId}?token=${p.links[0].token}`
                          )
                          toast.success('Link copied to clipboard')
                        }}
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </td>
        </tr>
      )}
    </>
  )
}

export default ParticipantRow 