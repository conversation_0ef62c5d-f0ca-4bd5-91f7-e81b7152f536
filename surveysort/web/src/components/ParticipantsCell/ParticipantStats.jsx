const ParticipantStats = ({ stats }) => (
  <div className="stats stats-vertical lg:stats-horizontal shadow mb-6 text-xs w-full">
    <div className="stat">
      <div className="stat-title">Total</div>
      <div className="stat-value text-lg">{stats.total}</div>
    </div>
    
    <div className="stat">
      <div className="stat-title">Pending</div>
      <div className="stat-value text-lg">{stats.pending}</div>
    </div>
    
    <div className="stat">
      <div className="stat-title">Sent</div>
      <div className="stat-value text-lg">{stats.sent}</div>
    </div>
    
    <div className="stat">
      <div className="stat-title">Clicked</div>
      <div className="stat-value text-lg">{stats.clicked}</div>
    </div>
    
    <div className="stat">
      <div className="stat-title">Completed</div>
      <div className="stat-value text-lg">{stats.completed}</div>
    </div>
  </div>
)

export default ParticipantStats 