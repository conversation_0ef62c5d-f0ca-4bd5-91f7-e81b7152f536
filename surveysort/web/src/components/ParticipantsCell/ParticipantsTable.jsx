import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full">
      <div className="max-h-[70vh] overflow-x-auto overflow-y-auto rounded-lg border border-base-300">
        <table
          className="table table-zebra w-full"
          style={{ tableLayout: 'auto' }}
        >
          <thead className="sticky top-0 z-10 bg-base-200">
            <tr>
              <th className="w-12 shrink-0 bg-base-200">
                <div className="flex items-center justify-center">
                  <span className="text-xs text-base-content/60">#</span>
                </div>
              </th>
              <th className="min-w-[200px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Email
                  </span>
                </div>
              </th>
              <th className="min-w-[120px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Name
                  </span>
                </div>
              </th>
              <th className="min-w-[100px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Status
                  </span>
                </div>
              </th>
              <th className="min-w-[160px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Last Activity
                  </span>
                </div>
              </th>
              <th className="min-w-[80px] bg-base-200">
                <div className="flex items-center justify-center">
                  <span className="text-xs font-semibold text-base-content">
                    Actions
                  </span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Scroll Indicator */}
      <div className="mt-2 text-center">
        <p className="text-xs text-base-content/60">
          {participants.length > 10 && (
            <>
              <span className="inline-flex items-center">
                <svg
                  className="mr-1 h-3 w-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
                Scroll to see more participants
              </span>
            </>
          )}
        </p>
      </div>
    </div>
  )
}

export default ParticipantsTable
