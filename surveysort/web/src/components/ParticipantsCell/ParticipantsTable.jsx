import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full">
      {/* Scrollable Table Container */}
      <div
        className="h-[400px] overflow-x-auto overflow-y-scroll rounded-lg border-2 border-primary/20 bg-base-100 shadow-sm"
        style={{
          scrollbarWidth: 'auto',
          scrollbarColor: 'rgb(59 130 246) rgb(243 244 246)',
        }}
      >
        <table
          className="table table-zebra w-full"
          style={{ tableLayout: 'auto' }}
        >
          <thead className="sticky top-0 z-20 bg-base-200 shadow-sm">
            <tr>
              <th className="w-12 shrink-0 border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">#</span>
              </th>
              <th className="min-w-[200px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Email</span>
              </th>
              <th className="min-w-[120px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Name</span>
              </th>
              <th className="min-w-[100px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Status</span>
              </th>
              <th className="min-w-[160px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Last Activity</span>
              </th>
              <th className="min-w-[80px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Scroll Info Banner */}
      {participants.length > 8 && (
        <div className="mt-3 rounded-lg border border-primary/20 bg-primary/10 p-3">
          <div className="flex items-center justify-center text-sm font-medium text-primary">
            <svg
              className="mr-2 h-4 w-4 animate-bounce"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
            Scroll in the table above to see all {participants.length}{' '}
            participants
            <svg
              className="ml-2 h-4 w-4 animate-bounce"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </div>
        </div>
      )}

      {/* Participant Count */}
      <div className="mt-4 text-center">
        <p className="text-xs text-base-content/60">
          Showing {participants.length} participants on this page
        </p>
      </div>
    </div>
  )
}

export default ParticipantsTable
