import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId
}) => {
  return (
    <div className="overflow-x-auto">
      <table className="table table-zebra w-full min-w-0">
        <thead>
          <tr>
            <th className="w-12"></th>
            <th className="min-w-[200px]">Email</th>
            <th className="min-w-[150px]">Name</th>
            <th className="min-w-[100px]">Status</th>
            <th className="min-w-[180px]">Last Activity</th>
            <th className="min-w-[100px]">Actions</th>
          </tr>
        </thead>
        <tbody>
          {participants.map((participant) => (
            <ParticipantRow
              key={participant.id}
              participant={participant}
              surveyId={surveyId}
              onGenerateLink={onGenerateLink}
              onDelete={onDelete}
              getStatusBadgeClass={getStatusBadgeClass}
              isDeleting={deletingParticipantId === participant.id}
            />
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default ParticipantsTable
