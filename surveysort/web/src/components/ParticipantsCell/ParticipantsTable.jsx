import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full">
      {/* Scrollable Table Container */}
      <div
        className="h-[500px] overflow-x-auto overflow-y-scroll rounded-lg border-2 border-primary/20 bg-base-100 shadow-sm"
        style={{
          scrollbarWidth: 'auto',
          scrollbarColor: 'rgb(59 130 246) rgb(243 244 246)',
        }}
      >
        <table
          className="table table-zebra w-full"
          style={{ tableLayout: 'auto' }}
        >
          <thead className="sticky top-0 z-20 bg-base-200 shadow-sm">
            <tr>
              <th className="w-12 shrink-0 border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">#</span>
              </th>
              <th className="min-w-[200px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Email</span>
              </th>
              <th className="min-w-[120px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Name</span>
              </th>
              <th className="min-w-[100px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Status</span>
              </th>
              <th className="min-w-[160px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Last Activity</span>
              </th>
              <th className="min-w-[80px] border-b-2 border-base-300 bg-base-200">
                <span className="text-xs font-semibold">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Participant Count and Scroll Info */}
      <div className="mt-4 flex items-center justify-between text-xs text-base-content/60">
        <div className="flex items-center">
          <span className="inline-flex items-center">
            <svg
              className="mr-1 h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Showing {participants.length} participants
          </span>
        </div>
        {participants.length > 10 && (
          <div className="flex items-center">
            <span className="inline-flex items-center font-medium text-primary">
              <svg
                className="mr-1 h-3 w-3 animate-pulse"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
              Scroll in table above to see all participants
            </span>
          </div>
        )}
      </div>
    </div>
  )
}

export default ParticipantsTable
