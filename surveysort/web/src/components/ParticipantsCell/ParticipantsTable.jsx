import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full rounded-lg border border-base-300 bg-base-100">
      {/* Fixed Header */}
      <div className="rounded-t-lg border-b border-base-300 bg-base-200">
        <table className="table w-full">
          <thead>
            <tr>
              <th className="w-12 shrink-0 bg-base-200">
                <div className="flex items-center justify-center">
                  <span className="text-xs text-base-content/60">#</span>
                </div>
              </th>
              <th className="min-w-[200px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Email
                  </span>
                </div>
              </th>
              <th className="min-w-[120px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Name
                  </span>
                </div>
              </th>
              <th className="min-w-[100px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Status
                  </span>
                </div>
              </th>
              <th className="min-w-[160px] bg-base-200">
                <div className="flex items-center">
                  <span className="text-xs font-semibold text-base-content">
                    Last Activity
                  </span>
                </div>
              </th>
              <th className="min-w-[80px] bg-base-200">
                <div className="flex items-center justify-center">
                  <span className="text-xs font-semibold text-base-content">
                    Actions
                  </span>
                </div>
              </th>
            </tr>
          </thead>
        </table>
      </div>

      {/* Scrollable Body */}
      <div
        className="overflow-x-auto overflow-y-auto"
        style={{
          height: '400px',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgb(156 163 175) transparent',
        }}
      >
        <table className="table table-zebra w-full">
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Scroll Indicator and Info */}
      <div className="rounded-b-lg border-t border-base-300 bg-base-200/50 p-3">
        <div className="flex items-center justify-between text-xs text-base-content/60">
          <div className="flex items-center">
            <span className="inline-flex items-center">
              <svg
                className="mr-1 h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              Showing {participants.length} participants
            </span>
          </div>
          {participants.length > 5 && (
            <div className="flex items-center">
              <span className="inline-flex items-center font-medium text-primary">
                <svg
                  className="mr-1 h-3 w-3 animate-bounce"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
                Scroll inside table to see more
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ParticipantsTable
