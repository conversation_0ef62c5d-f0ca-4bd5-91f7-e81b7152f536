import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full">
      {/* Scrollable Table Container */}
      <div className="max-h-[60vh] overflow-x-auto overflow-y-auto rounded-lg border border-base-300 bg-base-100">
        <table
          className="table table-zebra w-full"
          style={{ tableLayout: 'auto' }}
        >
          <thead className="sticky top-0 z-10 bg-base-200">
            <tr>
              <th className="w-12 shrink-0 bg-base-200"></th>
              <th className="min-w-[200px] bg-base-200">Email</th>
              <th className="min-w-[120px] bg-base-200">Name</th>
              <th className="min-w-[100px] bg-base-200">Status</th>
              <th className="min-w-[160px] bg-base-200">Last Activity</th>
              <th className="min-w-[80px] bg-base-200">Actions</th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Participant Count */}
      <div className="mt-4 text-center">
        <p className="text-xs text-base-content/60">
          Showing {participants.length} participants on this page
        </p>
      </div>
    </div>
  )
}

export default ParticipantsTable
