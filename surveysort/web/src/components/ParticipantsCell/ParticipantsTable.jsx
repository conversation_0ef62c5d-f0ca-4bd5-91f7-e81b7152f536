import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId,
}) => {
  return (
    <div className="w-full">
      <div className="overflow-x-auto">
        <table
          className="table table-zebra w-full"
          style={{ tableLayout: 'auto' }}
        >
          <thead>
            <tr>
              <th className="w-12 shrink-0"></th>
              <th className="min-w-[200px]">Email</th>
              <th className="min-w-[120px]">Name</th>
              <th className="min-w-[100px]">Status</th>
              <th className="min-w-[160px]">Last Activity</th>
              <th className="min-w-[80px]">Actions</th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant) => (
              <ParticipantRow
                key={participant.id}
                participant={participant}
                surveyId={surveyId}
                onGenerateLink={onGenerateLink}
                onDelete={onDelete}
                getStatusBadgeClass={getStatusBadgeClass}
                isDeleting={deletingParticipantId === participant.id}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default ParticipantsTable
