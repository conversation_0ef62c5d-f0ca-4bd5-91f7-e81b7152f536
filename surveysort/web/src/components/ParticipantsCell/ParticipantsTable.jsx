import ParticipantRow from './ParticipantRow'

const ParticipantsTable = ({
  participants,
  surveyId,
  onGenerateLink,
  onDelete,
  getStatusBadgeClass,
  deletingParticipantId
}) => {
  return (
    <div className="overflow-x-auto">
      <table className="table table-zebra w-full">
        <thead>
          <tr>
            <th></th>
            <th>Email</th>
            <th>Name</th>
            <th>Status</th>
            <th>Last Activity</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {participants.map((participant) => (
            <ParticipantRow
              key={participant.id}
              participant={participant}
              surveyId={surveyId}
              onGenerateLink={onGenerateLink}
              onDelete={onDelete}
              getStatusBadgeClass={getStatusBadgeClass}
              isDeleting={deletingParticipantId === participant.id}
            />
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default ParticipantsTable
