import { useState } from 'react'
import { useForm } from '@redwoodjs/forms'
import { Form, TextField, Submit, Label } from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { navigate, routes } from '@redwoodjs/router'
import { motion } from 'framer-motion'
import LoadingIndicator from 'src/components/LoadingIndicator/LoadingIndicator'
import {
  UserIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
  CircleStackIcon,
  ArrowUpIcon,
} from '@heroicons/react/24/outline'
import { useAuth } from 'src/auth'

const UPDATE_USER_PROPS = gql`
  mutation UpdateUserPropsMutation($input: UpdateUserPropsInput!) {
    updateUserProps(input: $input) {
      preferredName
      title
      organization
      industry
    }
  }
`

const ArrowIcon = ({ delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ 
      opacity: [0.1, 0.3, 0.1],
      y: [0, -40, 0],
    }}
    transition={{
      duration: 3,
      delay: delay,
      repeat: Infinity,
      ease: "easeInOut"
    }}
    className="text-primary/10"
  >
    <svg 
      viewBox="0 0 24 24" 
      fill="none" 
      className="h-8 w-8"
    >
      <path 
        d="M12 3L4 11H9V21H15V11H20L12 3Z" 
        fill="currentColor" 
        stroke="currentColor" 
        strokeWidth="1"
      />
    </svg>
  </motion.div>
)

const AnimatedBackground = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      <div className="absolute inset-0 grid grid-cols-8 gap-x-8 gap-y-12 p-12">
        {Array.from({ length: 32 }).map((_, i) => (
          <ArrowIcon key={i} delay={i * 0.15 % 2.5} />
        ))}
      </div>
    </div>
  )
}

const OnboardingForm = ({ onComplete }) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formError, setFormError] = useState(null)
  const formMethods = useForm({ mode: 'onChange' })
  const { reauthenticate } = useAuth()

  const [updateUserProps] = useMutation(UPDATE_USER_PROPS, {
    onCompleted: () => {
      setIsSubmitting(false)
      reauthenticate()
      onComplete?.()
      toast.success('Welcome to SurveySort!')
      navigate(routes.dashboard())
    },
    onError: (error) => {
      setIsSubmitting(false)
      setFormError(error.message)
      toast.error(error.message)
    },
  })

  const onSubmit = (data) => {
    setFormError(null)
    setIsSubmitting(true)
    updateUserProps({
      variables: {
        input: {
          ...data,
          onboardingDone: true,
        },
      },
    })
  }

  if (isSubmitting) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-3">
          <LoadingIndicator />
          <span className="text-sm text-base-content/70">
            Setting up your personalized experience...
          </span>
        </div>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-base-100 p-4">
      {/* Gradient Background */}
      <div className="fixed inset-0 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100" />
      
      {/* Animated Background */}
      <AnimatedBackground />

      {/* Frosted Card */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="relative card w-full max-w-md bg-white/70 backdrop-blur-xl shadow-xl border border-white/20"
      >
        <div className="card-body space-y-4">
          <div className="text-center space-y-1">
            <h2 className="text-lg font-semibold text-base-content">Welcome to SurveySort</h2>
            <p className="text-xs text-base-content/70">Tell us about yourself to get started</p>
          </div>

          <Form onSubmit={onSubmit} error={formError} formMethods={formMethods} className="mt-4">
            <div className="space-y-4">
              <div className="form-control">
                <Label name="preferredName" className="label text-xs text-base-content/70 py-1">
                  <span className="label-text">Preferred Name</span>
                </Label>
                <div className="relative">
                  <UserIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-base-content/50 z-10" />
                  <TextField
                    name="preferredName"
                    validation={{ required: true }}
                    className="input input-bordered bg-white/70 w-full pl-10 text-sm"
                    errorClassName="input input-bordered input-error pl-10 text-sm"
                    placeholder="How should we call you?"
                  />
                </div>
              </div>

              <div className="form-control">
                <Label name="title" className="label text-xs text-base-content/70 py-1">
                  <span className="label-text">Title</span>
                </Label>
                <div className="relative">
                  <BriefcaseIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-base-content/50 z-10" />
                  <TextField
                    name="title"
                    validation={{ required: true }}
                    className="input input-bordered bg-white/70 w-full pl-10 text-sm"
                    errorClassName="input input-bordered input-error pl-10 text-sm"
                    placeholder="Your role"
                  />
                </div>
              </div>

              <div className="form-control">
                <Label name="organization" className="label text-xs text-base-content/70 py-1">
                  <span className="label-text">Organization</span>
                </Label>
                <div className="relative">
                  <BuildingOfficeIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-base-content/50 z-10" />
                  <TextField
                    name="organization"
                    validation={{ required: true }}
                    className="input input-bordered bg-white/70 w-full pl-10 text-sm"
                    errorClassName="input input-bordered input-error pl-10 text-sm"
                    placeholder="Company name"
                  />
                </div>
              </div>

              <div className="form-control">
                <Label name="industry" className="label text-xs text-base-content/70 py-1">
                  <span className="label-text">Industry</span>
                </Label>
                <div className="relative">
                  <CircleStackIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-base-content/50 z-10" />
                  <TextField
                    name="industry"
                    validation={{ required: true }}
                    className="input input-bordered bg-white/70 w-full pl-10 text-sm"
                    errorClassName="input input-bordered input-error pl-10 text-sm"
                    placeholder="Your industry"
                  />
                </div>
              </div>

              <div className="pt-2">
                <Submit 
                  className="btn btn-primary w-full text-sm h-10 min-h-0"
                  disabled={!formMethods.formState.isValid || isSubmitting}
                >
                  {isSubmitting ? (
                    <span className="loading loading-spinner loading-sm"></span>
                  ) : (
                    'Complete Setup'
                  )}
                </Submit>
              </div>
            </div>
          </Form>
        </div>
      </motion.div>
    </div>
  )
}

export default OnboardingForm
