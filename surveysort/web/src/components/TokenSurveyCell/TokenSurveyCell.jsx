import { useState, useEffect } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import EndUserSurveyCell from 'src/components/EndUserSurveyCell'

export const QUERY = gql`
  query VerifyTokenAndGetSurvey($token: String!) {
    verifyTokenAndGetSurvey(token: $token) {
      participant {
        id
        email
        name
        status
      }
      survey {
        id
        title
        description
        status
        questions {
          id
          type
          title
          required
          pageNumber
          order
          questionConfig {
            choices
            rating
            scale
            minLength
            maxLength
            rules {
              operation
              value
            }
            condition
            actions {
              type
              questionNumber
            }
            allowMultiple
            allowWriteIn
            rows
            columns
            cellType
            includeTime
            requireName
            requireEmail
            requirePhone
            requireCompany
            requireAddress
            requireWebsite
            randomize
            randomizeChoices
            randomizeRows
            welcomeMessage
            welcomeDisclaimer
            requireDisclaimer
            thankYouMessage
          }
        }
        settings {
          randomizationGroups {
            name
            questionIds
          }
          themeConfig {
            questionTextColor
            answerTextColor
            buttonColor
            backgroundColor
            progressBarColor
          }
          previewTitle
          previewDescription
          previewImage
          companyLogo
          removeBranding
          showNavigation
          showProgressBar
          requireEmail
          disclosure
        }
      }
    }
  }
`

const COMPLETE_SURVEY_MUTATION = gql`
  mutation CompleteSurveyParticipant($token: String!) {
    completeSurveyParticipant(token: $token) {
      id
      status
    }
  }
`

export const Loading = () => (
  <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
    <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
      <div className="skeleton mb-4 h-8 w-3/4"></div>
      <div className="skeleton mb-6 h-4 w-full"></div>
      <div className="skeleton h-10 w-full"></div>
    </div>
  </div>
)

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }) => {
  if (error) {
    const errorMsg = error?.graphQLErrors?.[0]?.extensions?.errorMsg || error?.message || 'An unexpected error occurred.';

    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="error-container bg-red-50 border-l-4 border-red-500 p-4 rounded-md shadow-sm">
            <div className="flex items-center">
              <svg
                className="h-6 w-6 text-red-500 mr-2"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12h6m-3-3v6m2-8.938c3.22 0 5.832 2.387 5.832 5.334 0 2.947-2.612 5.333-5.832 5.333-3.22 0-5.832-2.386-5.832-5.333C3.168 7.448 5.78 5.062 9 5.062z"
                />
              </svg>
              <h2 className="text-lg font-medium text-red-700">Survey Link Error</h2>
            </div>
            <p className="text-red-700 mt-2">{errorMsg}</p>
            <a
              href="https://surveysort.com"
              className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white font-medium text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Go Home
            </a>
          </div>
        </div>
      </div>
    );
  }
}

export const Success = ({ verifyTokenAndGetSurvey, token }) => {
  const { participant, survey } = verifyTokenAndGetSurvey

  const [completeSurveyParticipant] = useMutation(COMPLETE_SURVEY_MUTATION, {
    onCompleted: () => {
      console.log('Survey completion status updated successfully')
    },
    onError: (error) => {
      console.error('Failed to update survey completion status:', error)
    },
  })

  // Simply render the existing EndUserSurveyCell with the survey data
  // We'll modify EndUserSurveyResponseSubmit to handle token completion
  return (
    <EndUserSurveyCell.Success
      endUserSurvey={survey}
      token={token}
      completeSurveyParticipant={completeSurveyParticipant}
      prefilledEmail={participant?.email}
    />
  )
}
