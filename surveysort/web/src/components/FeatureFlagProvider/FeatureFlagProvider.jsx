import { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from 'src/auth'
import { useQuery } from '@redwoodjs/web'

const FeatureFlagContext = createContext({
  isReleased: () => false,
  loading: true,
  error: null,
})

export const FLAGS_QUERY = gql`
  query GetFeatureFlags($flagKeys: [String!]!) {
    getFlags(flagKeys: $flagKeys) {
      key
      enabled
    }
  }
`

const DEFAULT_FLAGS = ['isReleased']

export const FeatureFlagProvider = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuth()
  const [flags, setFlags] = useState({})
  const [initialLoadDone, setInitialLoadDone] = useState(false)

  const { loading, error, refetch } = useQuery(FLAGS_QUERY, {
    variables: { flagKeys: DEFAULT_FLAGS },
    skip: true, // Skip initial auto-fetch
    onCompleted: (data) => {
      if (data?.getFlags) {
        const newFlags = {}
        data.getFlags.forEach(flag => {
          newFlags[flag.key] = flag.enabled
        })
        setFlags(newFlags)
      }
      setInitialLoadDone(true)
    }
  })

  // Load flags on mount and when auth state changes
  useEffect(() => {
    refetch()
  }, [refetch, isAuthenticated, currentUser])

  const value = {
    isReleased: () => flags['isReleased'] ?? false,
    loading: loading || !initialLoadDone,
    error,
  }

  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  )
}

export const useFeatureFlags = () => {
  const context = useContext(FeatureFlagContext)
  if (context === undefined) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider')
  }
  return context
} 