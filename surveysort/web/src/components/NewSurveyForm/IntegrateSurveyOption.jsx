import { Label, SelectField } from '@redwoodjs/forms'

const IntegrateSurveyOption = () => {
  return (
    <div className="form-control w-full">
      <Label name="integration" className="label">
        <span className="label-text">Select Survey Tool</span>
      </Label>
      <SelectField
        name="surveyTool"
        className="select select-bordered w-full"
        validation={{ required: true }}
      >
        <option value="">Select a survey tool</option>
        <option value="surveyMonkey">Survey Monkey</option>
        <option value="surveyPlanet">Survey Planet</option>
        <option value="qualtrics">Qualtrics</option>
        <option value="custom">Custom</option>
      </SelectField>
      <div className="mt-4">
        <h3 className="text-lg font-semibold">Integration Instructions</h3>
        <p className="text-sm text-gray-600">
          Select a survey tool to see specific integration instructions.
        </p>
        {/* Add conditional rendering for specific integration instructions */}
      </div>
    </div>
  )
}

export default IntegrateSurveyOption
