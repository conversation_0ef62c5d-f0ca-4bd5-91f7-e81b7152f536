import { useState } from 'react'
import { Form, Label, TextField, Submit, FieldError, TextAreaField } from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { Dialog } from '@headlessui/react'
import { navigate, routes } from '@redwoodjs/router'
import TemplatesView from 'src/components/TemplatesView/TemplatesView'
import { SparklesIcon, InformationCircleIcon, BeakerIcon } from '@heroicons/react/24/outline'
import TemplateCustomizeDialog from 'src/components/TemplateCustomizeDialog/TemplateCustomizeDialog'

const TemplateCreateSurveyOption = ({ onClose, initialTemplate = null }) => {
  const [selectedTemplate, setSelectedTemplate] = useState(initialTemplate)
  const [showDialog, setShowDialog] = useState(!!initialTemplate)

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
    setShowDialog(true)
  }

  return (
    <>
      <TemplatesView onTemplateSelect={handleTemplateSelect} />
      
      <TemplateCustomizeDialog 
        isOpen={showDialog}
        onClose={() => {
          setShowDialog(false)
          setSelectedTemplate(null)
        }}
        template={selectedTemplate}
      />
    </>
  )
}

export default TemplateCreateSurveyOption 