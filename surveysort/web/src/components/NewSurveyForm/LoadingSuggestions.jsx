import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

import {
    LightBulbIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon,
    SparklesIcon,
  } from '@heroicons/react/24/outline'
  
const loadingMessages = [
  'Checking survey topic feasibility...',
  'Identifying potential biases and vagueness...',
  'Fetching relevant research links...',
  'Generating tailored survey suggestions...',
  'Finalizing your survey recommendations...'
];

const finalMessage = 'Your customized survey recommendations are ready!';

const LoadingSuggestions = ({ loadingRecommendations }) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  useEffect(() => {
    if (!loadingRecommendations) return;

    const messageDuration = 2000; // 2 seconds per message
    const totalMessages = loadingMessages.length;
    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) => {
        if (prevIndex < totalMessages - 1) {
          return prevIndex + 1;
        } else {
          clearInterval(interval);
          return prevIndex;
        }
      });
    }, messageDuration);

    return () => clearInterval(interval);
  }, [loadingRecommendations]);

  return (
    <>
      {loadingRecommendations && (
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <motion.div
              className="mx-auto h-12 w-12"
              animate={{
                rotate: 360,
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: 'linear',
              }}
            >
              <ArrowPathIcon className="h-12 w-12 text-primary" />
            </motion.div>
            <p className="mt-4 text-sm text-gray-700">
              {currentMessageIndex < loadingMessages.length
                ? loadingMessages[currentMessageIndex]
                : finalMessage}
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default LoadingSuggestions;