import { useState } from 'react'
import {
  Form,
  Label,
  TextField,
  TextAreaField,
  Submit,
  FieldError,
} from '@redwoodjs/forms'
import { toast } from '@redwoodjs/web/toast'
import { motion } from 'framer-motion'
import { ArrowPathIcon, SparklesIcon, BeakerIcon, InformationCircleIcon } from '@heroicons/react/24/outline'
import { useMutation } from '@redwoodjs/web'
import { navigate, routes } from '@redwoodjs/router'

const CREATE_SURVEY = gql`
  mutation CreateSurvey($input: CreateSurveyInput!) {
    createSurvey(input: $input) {
      id
      title
    }
  }
`

const CreateSurveyOption = ({ onClose }) => {
  const [useAI, setUseAI] = useState(false)
  const [creating, setCreating] = useState(false)
  
  const [createSurvey] = useMutation(CREATE_SURVEY, {
    onCompleted: (data) => {
      toast.success('Survey created successfully!')
      navigate(routes.surveyDetail({ id: data.createSurvey.id }))
    },
  })

  const onSubmit = async (data) => {
    setCreating(true)
    try {
      await createSurvey({
        variables: {
          input: {
            ...data,
            isGeneratedByAi: useAI,
            productName: data.productName,
          },
        },
      })
    } catch (error) {
      toast.error(error.message)
      setCreating(false)
    }
  }

  return (
    <div className="rounded-lg border border-base-300 bg-base-100">
      <Form onSubmit={onSubmit} className="p-6 space-y-6">
        <div>
          <div className="flex items-center gap-2">
            <Label name="title" className="font-medium">Survey Title</Label>
            <div className="tooltip tooltip-right" data-tip="A clear, descriptive title helps respondents understand the survey's purpose at a glance">
              <InformationCircleIcon className="h-4 w-4 text-base-content/70" />
            </div>
          </div>
          <TextField
            name="title"
            validation={{ required: true }}
            className="input input-bordered w-full mt-1"
            placeholder="e.g., Customer Satisfaction Survey Q4 2024"
          />
          <FieldError name="title" className="mt-1 text-error text-sm" />
        </div>

        <div>
          <Label name="audience" className="font-medium">Target Audience</Label>
          <TextField
            name="audience"
            validation={{ required: true }}
            className="input input-bordered w-full mt-1"
            placeholder="e.g., Current customers who have used our product for at least 3 months"
          />
          <p className="mt-1 text-xs text-base-content/70">
            Define who should take this survey. Be specific to ensure relevant responses.
          </p>
          <FieldError name="audience" className="mt-1 text-error text-sm" />
        </div>

        <div>
          <Label name="productName" className="font-medium">Product/Company Name</Label>
          <TextField
            name="productName"
            validation={{ required: true }}
            className="input input-bordered w-full mt-1"
            placeholder="e.g., SurveySort"
          />
          <FieldError name="productName" className="mt-1 text-error text-sm" />
        </div>

        <div>
          <Label name="surveyObjective" className="font-medium">Survey Objective</Label>
          <TextAreaField
            name="surveyObjective"
            validation={{ required: true }}
            className="textarea textarea-bordered w-full mt-1"
            placeholder="e.g., Understand customer satisfaction with recent app update, identify pain points, and gather feature requests"
            rows={3}
          />
          <p className="mt-1 text-xs text-base-content/70">
            Clearly state what you want to learn. This guides question design and helps ensure you gather actionable insights.
          </p>
          <FieldError name="surveyObjective" className="mt-1 text-error text-sm" />
        </div>

        <div>
          <Label name="additionalContext" className="font-medium">Additional Context (Optional)</Label>
          <TextAreaField
            name="additionalContext"
            className="textarea textarea-bordered w-full mt-1"
            placeholder="e.g., Recent app update included new features X and Y. We've received some support tickets about feature Z."
            rows={3}
          />
          <p className="mt-1 text-xs text-base-content/70">
            Add any background information, recent changes, or specific areas of interest that might be relevant.
          </p>
        </div>

        <div className="rounded-lg bg-primary/5 p-4 border border-primary/10">
          <label className="flex items-start gap-4 cursor-pointer hover:bg-primary/5 rounded-md transition-colors p-2 -m-2">
            <input 
              type="checkbox" 
              className="checkbox checkbox-primary mt-1" 
              checked={useAI}
              onChange={(e) => setUseAI(e.target.checked)}
            />
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium">SurveySort Questions Agent</span>
                <SparklesIcon className="h-4 w-4 text-primary" />
              </div>
              <p className="mt-1 text-sm text-base-content/70">
                Let our AI design expert craft intelligent questions based on research insights. 
                It analyzes your objective, considers best practices, and generates 5-10 
                perfectly structured questions with optimal response formats.
              </p>
              <div className="mt-2 flex items-center gap-2 text-xs text-primary/80">
                <BeakerIcon className="h-3 w-3" />
                <span>Powered by advanced survey design AI</span>
              </div>
            </div>
          </label>
        </div>

        <Submit 
          className="btn btn-primary w-full"
          disabled={creating}
        >
          {creating ? (
            <div className="flex items-center justify-center">
              <motion.span
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              >
                <ArrowPathIcon className="h-5 w-5 mr-2" />
              </motion.span>
              Creating Survey...
            </div>
          ) : (
            'Create Survey'
          )}
        </Submit>
      </Form>
    </div>
  )
}

export default CreateSurveyOption
