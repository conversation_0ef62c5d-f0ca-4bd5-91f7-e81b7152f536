import { useState } from 'react'
import { Tab } from '@headlessui/react'
import { 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  BookOpenIcon, 
  LinkIcon,
  LightBulbIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline'

const SurveyRecommendations = ({ recommendations, onCreateSurvey, creatingStage }) => {
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0)

  const nextSuggestion = () => {
    setCurrentSuggestionIndex((prevIndex) =>
      Math.min(prevIndex + 1, recommendations.suggestions.length - 1)
    )
  }

  const prevSuggestion = () => {
    setCurrentSuggestionIndex((prevIndex) => Math.max(prevIndex - 1, 0))
  }

  return (
    <Tab.Group>
      <Tab.List className="flex space-x-1 rounded-xl bg-primary/20 p-1">
        <Tab className={({ selected }) =>
          `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-primary flex items-center justify-center
           ${selected ? 'bg-white shadow' : 'text-primary-content hover:bg-white/[0.12] hover:text-primary-content'}`
        }>
          <LightBulbIcon className="h-5 w-5 mr-2" />
          Recommendations
        </Tab>
        <Tab className={({ selected }) =>
          `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-primary flex items-center justify-center
           ${selected ? 'bg-white shadow' : 'text-primary-content hover:bg-white/[0.12] hover:text-primary-content'}`
        }>
          <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
          Research
        </Tab>
      </Tab.List>
      <Tab.Panels className="mt-4">
        <Tab.Panel>
          <div className="space-y-4 h-[calc(100vh-200px)] overflow-y-auto pr-2">
            <h4 className="text-lg font-semibold">Considerations</h4>
            <p className="text-sm text-gray-600">{recommendations.considerations}</p>
            <div className="flex justify-between items-center">
              <div className="text-lg font-semibold">Survey Suggestions</div>
              <div className="flex space-x-2">
                <button
                  className="btn btn-square btn-outline btn-primary btn-sm"
                  onClick={prevSuggestion}
                  disabled={currentSuggestionIndex === 0}
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <button
                  className="btn btn-square btn-outline btn-primary btn-sm"
                  onClick={nextSuggestion}
                  disabled={currentSuggestionIndex === recommendations.suggestions.length - 1}
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            <div className="carousel w-full">
              {recommendations.suggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id}
                  className={`carousel-item w-full ${
                    index === currentSuggestionIndex ? 'block' : 'hidden'
                  }`}
                >
                  <div className="alert alert-info">
                    <div>
                      <h2 className="font-bold text-primary">{suggestion.surveyType}</h2>
                      <p>
                        <strong className="text-primary">Objective:</strong> {suggestion.surveyObjective}
                      </p>
                      <p>
                        <strong className="text-primary">Audience:</strong> {suggestion.audience}
                      </p>
                      <p>
                        <strong className="text-primary">Survey Summary:</strong> {suggestion.surveySummary}
                      </p>
                      <div className="mt-2">
                        {creatingStage ? (
                          <div className="flex items-center">
                            <ArrowPathIcon className="animate-spin h-5 w-5 mr-2" />
                            <span>{creatingStage}</span>
                          </div>
                        ) : (
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => onCreateSurvey(suggestion)}
                          >
                            Use This Suggestion
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Tab.Panel>
        <Tab.Panel>
          <div className="space-y-4 h-[calc(100vh-200px)] overflow-y-auto pr-2">
            <h3 className="text-2xl font-bold">Research Links</h3>
            <ul className="space-y-4">
              {recommendations.researchLinks.map((link, index) => (
                <li key={index} className="flex items-start">
                  <BookOpenIcon className="h-5 w-5 text-primary mr-2 mt-1 flex-shrink-0" />
                  <a 
                    href={link.url} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="text-primary hover:underline flex items-start"
                  >
                    <span className="mr-1">{link.title}</span>
                    <LinkIcon className="h-4 w-4 mt-1 flex-shrink-0" />
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </Tab.Panel>
      </Tab.Panels>
    </Tab.Group>
  )
}

export default SurveyRecommendations