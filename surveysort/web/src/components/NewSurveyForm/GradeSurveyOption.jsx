import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useMutation } from '@redwoodjs/web'
import { Form, SelectField } from '@redwoodjs/forms' // Removed Submit
import Papa from 'papaparse'
import toast from 'react-hot-toast'

const GET_PRESIGNED_URL = gql`
  mutation GetPresignedUrl($input: PresignedUrlInput!) {
    getPresignedUrl(input: $input) {
      signedUrl
      filePath
    }
  }
`

const CREATE_UPLOADED_SURVEY = gql`
  mutation CreateExternalUploadSurvey($input: CreateExternalUploadSurveyInput!) {
    createExternalUploadSurvey(input: $input) {
      id
    }
  }
`

const questionTypes = [
  { label: 'Multiple Choice', value: 'MULTIPLE_CHOICE' },
  { label: 'Essay', value: 'ESSAY' },
  { label: 'Rating', value: 'RATING' },
  { label: 'Scale', value: 'SCALE' },
  { label: 'Short Answer', value: 'SHORT' },
  { label: 'Scoring', value: 'SCORING' },
  { label: 'Range', value: 'RANGE' },
  { label: 'Date/Time', value: 'DATE_TIME' },
  { label: 'Image', value: 'IMAGE' },
  { label: 'Matrix', value: 'MATRIX' },
]

const GradeSurveyOption = ({ onClose }) => {
  const [file, setFile] = useState(null)
  const [headers, setHeaders] = useState([])
  const [mappings, setMappings] = useState({})
  const [ignoredFields, setIgnoredFields] = useState({})
  const [getPresignedUrl] = useMutation(GET_PRESIGNED_URL)
  const [createSurvey] = useMutation(CREATE_UPLOADED_SURVEY)
  const [filePath, setFilePath] = useState('')

  const onDrop = useCallback((acceptedFiles) => {
    const uploadedFile = acceptedFiles[0]
    setFile(uploadedFile)
    Papa.parse(uploadedFile, {
      header: true,
      complete: (results) => {
        setHeaders(results.meta.fields)
      },
    })
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
    },
    multiple: false,
  })

  const handleMappingChange = (column, field, value, headerIndex) => {
    setMappings((prev) => ({
      ...prev,
      [column]: {
        ...prev[column],
        [field]: value,
        headerIndex: headerIndex,
      },
    }))
  }

  const handleIgnoreField = (column) => {
    setIgnoredFields((prev) => ({
      ...prev,
      [column]: !prev[column],
    }))
    if (!ignoredFields[column]) {
      setMappings((prev) => {
        const newMappings = { ...prev }
        delete newMappings[column]
        return newMappings
      })
    }
  }

  const handleUpload = async () => {
    if (!file) return

    // Set default ignore on unchecked fields
    const newIgnoredFields = { ...ignoredFields }
    headers.forEach((column) => {
      if (!mappings[column]) {
        newIgnoredFields[column] = true
      }
    })
    setIgnoredFields(newIgnoredFields)

    try {
      const { data } = await getPresignedUrl({
        variables: {
          input: {
            fileName: file.name,
            fileType: file.type,
          },
        },
      })
      const { signedUrl, filePath } = data.getPresignedUrl
      setFilePath(filePath)

      const result = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (result.ok) {
        const { data } = await createSurvey({
          variables: {
            input: {
              filePath,
              mappings,
            },
          },
        })
        const { id } = data.createExternalUploadSurvey
        if (id) {
          toast.success('File uploaded successfully and survey created')
          onClose() // Close popup after successful upload
        }
      } else {
        toast.error('Error uploading the file. Please try again.')
      }
    } catch (error) {
      console.log('Error uploading file:', error)
      toast.error('Error uploading the file. Please try again.')
    }
  }

  return (
    <Form className="space-y-4">
      <div className="w-full">
        <div
          {...getRootProps()}
          className={`flex justify-center rounded-lg border-2 border-dashed border-secondary px-6 py-10 transition-colors ${isDragActive ? 'border-primary bg-primary bg-opacity-10' : 'hover:border-primary'
            }`}
        >
          <input {...getInputProps()} />
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-secondary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              {/* SVG Path */}
            </svg>
            <div className="mt-4 flex text-sm leading-6 text-base-content">
              <label className="relative cursor-pointer rounded-md font-semibold text-primary">
                <span>Upload a file</span>
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs leading-5 text-base-content">
              CSV up to 10MB
            </p>
          </div>
        </div>
      </div>

      {file && (
        <div className="text-sm text-base-content">
          Selected file: {file.name}
        </div>
      )}

      {/* Scrollable Column Mapping Interface */}
      {headers.length > 0 && (
        <div
          className="space-y-4 max-h-[58vh] overflow-y-auto"
          style={{
            scrollbarColor: '#A0AEC0 #EDF2F7',
            scrollbarWidth: 'thin',
          }}
        >
          <h3 className="text-lg font-semibold">Map Columns to Questions</h3>
          {headers.map((column, index) => (
            <div key={column} className="flex items-center space-x-4">
              <div className="w-1/3">
                <label className="block text-sm font-medium text-gray-700">
                  {column}
                </label>
              </div>
              <div className="w-1/3">
                <SelectField
                  name={`questionType-${column}`}
                  className="select select-bordered w-full"
                  value={mappings[column]?.questionType || ''}
                  onChange={(e) =>
                    handleMappingChange(column, 'questionType', e.target.value, index)
                  }
                  validation={{ required: true }}
                  disabled={ignoredFields[column]}
                >
                  <option value="">Select Type</option>
                  {questionTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </SelectField>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={ignoredFields[column] || false}
                  onChange={() => handleIgnoreField(column)}
                  className="checkbox"
                />
                <label className="text-sm text-gray-700">Ignore</label>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Conditionally Render Cancel and Upload Buttons */}
      {file && (
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={() => {
              setFile(null)
              onClose() // Close popup when canceling
            }}
            className="btn btn-sm btn-primary"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleUpload} // Directly call handleUpload
            className="btn btn-sm btn-primary"
          >
            Upload
          </button>
        </div>
      )}
    </Form>
  )
}

export default GradeSurveyOption
