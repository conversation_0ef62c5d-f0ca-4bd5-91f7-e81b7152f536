import { useState } from 'react'
import { 
  PencilSquareIcon, 
  ClipboardDocumentIcon, 
  ArrowLeftIcon 
} from '@heroicons/react/24/outline'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import CreateSurveyOption from './CreateSurveyOption'
import TemplateCreateSurveyOption from './TemplateCreateSurveyOption'
import { navigate, routes } from '@redwoodjs/router'

const CREATE_SURVEY = gql`
  mutation CreateSurveyMutation($input: CreateSurveyInput!) {
    createSurvey(input: $input) {
      id
    }
  }
`

const NewSurveyForm = ({ onClose, onOptionSelect, selectedOption }) => {
  const [createSurvey] = useMutation(CREATE_SURVEY, {
    onCompleted: () => {
      toast.success('Survey created successfully')
      onClose()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const options = [
    {
      id: 'scratch',
      title: 'Create Survey from Scratch',
      description: 'Start with a blank survey and customize everything to your needs.',
      icon: PencilSquareIcon,
      component: CreateSurveyOption,
    },
    {
      id: 'template',
      title: 'Create with a Template',
      description: 'Choose from our pre-built templates and customize for your specific use case.',
      icon: ClipboardDocumentIcon,
      component: TemplateCreateSurveyOption,
    },
  ]

  const renderOptions = () => (
    <div className="w-full max-w-2xl mx-auto">
      <div className="space-y-4">
        {options.map((option) => (
          <div
            key={option.id}
            onClick={() => onOptionSelect(option.id)}
            className="group card bg-white/70 backdrop-blur-sm hover:shadow-lg transition-all border border-base-300 hover:border-primary/20 hover:bg-primary/5 cursor-pointer"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === 'Enter' && onOptionSelect(option.id)}
          >
            <div className="card-body flex flex-row items-center p-6">
              <option.icon className="h-8 w-8 text-primary mr-4 group-hover:text-primary/80" />
              <div>
                <h3 className="text-lg font-medium">{option.title}</h3>
                <p className="text-sm text-base-content/70">{option.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderSelectedOption = () => {
    const option = options.find((o) => o.id === selectedOption)
    const Component = option.component

    return (
      <div className="w-full max-w-4xl mx-auto">
        <Component 
          onClose={onClose} 
          createSurvey={selectedOption === 'scratch' ? createSurvey : undefined} 
        />
      </div>
    )
  }

  return selectedOption ? renderSelectedOption() : renderOptions()
}

export default NewSurveyForm
