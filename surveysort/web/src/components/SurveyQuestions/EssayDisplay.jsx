import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const EssayDisplay = ({ question, value, onChange, onKeystroke, onPaste, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const minLength = parseInt(question.questionConfig?.minLength) || 0
  const maxLength = parseInt(question.questionConfig?.maxLength) || 1000

  const handleChange = (e) => {
    onChange?.(e.target.value)
  }

  return (
    <div>
      <textarea
        className={`
          w-full h-32 rounded-lg px-4 py-2
          border border-gray-300
          focus:ring-2 focus:ring-opacity-50 focus:border-transparent
          transition duration-150 ease-in-out
          placeholder:text-gray-400
          disabled:opacity-50 disabled:cursor-not-allowed
          ${themeConfig?.buttonColor ? 'focus:ring-primary/30' : 'focus:ring-blue-500/30'}
        `}
        placeholder="Type your answer here..."
        minLength={minLength}
        maxLength={maxLength}
        value={value || ''}
        onChange={handleChange}
        onKeyDown={onKeystroke}
        onPaste={onPaste}
        style={{ 
          '--primary': themeConfig?.buttonColor,
          color: themeConfig?.answerTextColor,
          borderColor: `${themeConfig?.buttonColor}40`,
        }}
      />
      <div 
        className="text-sm mt-1 space-x-2"
        style={{ color: themeConfig?.answerTextColor }}
      >
        {minLength > 0 && <span>Minimum length: {minLength} characters</span>}
        {maxLength < 1000 && <span>Maximum length: {maxLength} characters</span>}
      </div>
    </div>
  )
}

export default EssayDisplay
