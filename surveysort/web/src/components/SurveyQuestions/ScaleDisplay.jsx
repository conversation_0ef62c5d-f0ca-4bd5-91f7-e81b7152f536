import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const ScaleDisplay = ({ question, value, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const scale = question.questionConfig?.scale || 5

  const handleScaleClick = (scaleValue) => {
    onChange?.(scaleValue)
  }

  return (
    <div className="flex flex-wrap justify-center gap-2">
      {[...Array(scale)].map((_, index) => (
        <button
          key={index}
          type="button"
          onClick={() => handleScaleClick(index + 1)}
          style={{
            borderColor: value === index + 1 
              ? 'var(--primary)' 
              : 'rgba(var(--answer-text-rgb), 0.4)',
            backgroundColor: value === index + 1 
              ? 'var(--primary)'
              : 'rgba(var(--answer-text-rgb), 0.05)',
            color: value === index + 1 
              ? 'text-white' 
              : 'text-[--answer-text]',
          }}
          className={`
            w-12 h-12 rounded-full flex items-center justify-center
            text-base font-medium border-2
            transition-all duration-200 ease-in-out
            ${value === index + 1 
              ? 'bg-[--background] scale-110' 
              : 'text-[--answer-text] hover:scale-105'
            }
            hover:bg-[--primary]/10 hover:border-[--background]
            active:scale-95
          `}
        >
          {index + 1}
        </button>
      ))}
    </div>
  )
}

export default ScaleDisplay
