import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const ShortDisplay = ({ question, value, onChange, onKeystroke, onPaste, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const maxLength = parseInt(question.questionConfig?.maxLength) || 100

  const handleChange = (e) => {
    onChange?.(e.target.value)
  }

  return (
    <div>
      <input
        type="text"
        className={`
          input input-bordered w-full
          focus:outline-none focus:ring-2 focus:ring-[--primary]/30
          text-[--answer-text] border-[--primary]/40
          placeholder:text-[--answer-text]/60
        `}
        placeholder="Type your answer here..."
        maxLength={maxLength}
        value={value || ''}
        onChange={handleChange}
        onKeyDown={onKeystroke}
        onPaste={onPaste}
        style={{ 
          '--primary': themeConfig?.buttonColor,
          '--answer-text': themeConfig?.answerTextColor
        }}
      />
      {maxLength < 100 && (
        <div className="text-sm mt-1 text-base-content" style={{ '--base-content': themeConfig?.answerTextColor }}>
          Maximum length: {maxLength} characters
        </div>
      )}
    </div>
  )
}

export default ShortDisplay
