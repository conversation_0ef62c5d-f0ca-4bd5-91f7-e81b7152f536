import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'
import ThankYouMessageDisplay from './ThankYouMessageDisplay'
import WelcomeMessageDisplay from './WelcomeMessageDisplay'
import MultipleChoiceInput from './MultipleChoiceDisplay'
import EssayInput from './EssayDisplay'
import RatingInput from './RatingDisplay'
import ScaleInput from './ScaleDisplay'
import ShortInput from './ShortDisplay'
import MatrixInput from './MatrixDisplay'
import EmoticonInput from './EmoticonDisplay'
import AcceptDenyDisplay from './AcceptDenyDisplay'
import LikeDislikeDisplay from './LikeDislikeDisplay'
import DateTimeDisplay from './DateTimeDisplay'
import ContactDetailsDisplay from './ContactDetailsDisplay'
import ScoringInput from './ScoringDisplay'

const QuestionDisplay = ({
  question,
  value,
  onChange,
  onValidation,
  onKeystroke,
  onPaste,
  showValidation = false,
  isPreview = false,
  error,
  themeConfig
}) => {

  const { device, themeConfig: contextThemeConfig } = useContext(SurveyContext)

  // Default theme values
  const defaultTheme = {
    questionTextColor: 'hsl(var(--bc))', // base content color
    answerTextColor: 'hsl(var(--bc) / 0.8)', // base content with opacity
    buttonColor: 'hsl(var(--p))', // primary color
    backgroundColor: 'hsl(var(--b1))', // base-100 color
    progressBarColor: 'hsl(var(--p))', // primary color
    buttonTextColor: 'hsl(var(--bc))', // base content color
  }

  // Merge themes with proper fallbacks
  const mergedThemeConfig = {
    ...defaultTheme,
    ...contextThemeConfig,
    ...(themeConfig || {})
  }

  // Comprehensive validation logic
  const validateResponse = (value) => {
    const errors = []

    if (question.required && !value) {
      errors.push('This field is required')
    }

    switch (question.type) {
      case 'SHORT':
      case 'ESSAY':
        if (question.questionConfig?.maxLength &&
          value?.length > question.questionConfig.maxLength) {
          errors.push(`Answer exceeds maximum length of ${question.questionConfig.maxLength} characters`)
        }
        if (question.questionConfig?.minLength &&
          value?.length < question.questionConfig.minLength) {
          errors.push(`Answer must be at least ${question.questionConfig.minLength} characters`)
        }
        break;

      case 'MATRIX':
        if (question.required) {
          const matrixValue = value || {}
          question.questionConfig?.rows?.forEach((row) => {
            if (!matrixValue[row.id]) {
              errors.push(`Row "${row}" is required`)
            }
          })
        }
        break;
 
    }

    onValidation?.(errors)
    return errors
  }

  // Shared handlers
  const handleChange = (newValue) => {
    const errors = validateResponse(newValue)
    onChange?.(question.id, newValue, errors)
  }

  const handleKeystroke = (event) => {
    if (!isPreview) {
      onKeystroke?.(question.id, event)
    }
  }

  const handlePaste = (event) => {
    if (!isPreview) {
      onPaste?.(question.id)
    }
  }

  // Component mapping
  const components = {
    MULTIPLE_CHOICE: MultipleChoiceInput,
    ESSAY: EssayInput,
    RATING: RatingInput,
    SCALE: ScaleInput,
    RANGE: ScaleInput,
    SCORING: ScoringInput,
    SHORT: ShortInput,
    MATRIX: MatrixInput,
    EMOTICON: EmoticonInput,
    ACCEPT_DENY: AcceptDenyDisplay,
    LIKE_DISLIKE: LikeDislikeDisplay,
    DATE_TIME: DateTimeDisplay,
    CONTACT_DETAILS: ContactDetailsDisplay,
    THANK_YOU_MESSAGE: ThankYouMessageDisplay,
    WELCOME_MESSAGE: WelcomeMessageDisplay,
  }

  const QuestionComponent = components[question.type]

  if (!QuestionComponent) {
    return <div>Unsupported question type: {question.type}</div>
  }

  return (
    <div className="question-display">
      {question.explainer && (
          <p className="text-sm mb-1 text-[--question-text]/60">
            {question.explainer}
          </p>
        )}

      <div className="question-title">
        <h3 className="text-lg font-semibold py-4  text-[--question-text]">
          {question.title}
          </h3>
        </div>
        {question.required && (
          <span className="text-sm text-error">* Required</span>
        )}

      <QuestionComponent
        question={question}
        value={value}
        onChange={handleChange}
        onKeystroke={handleKeystroke}
        onPaste={handlePaste}
        isPreview={isPreview}
        error={error}
        themeConfig={mergedThemeConfig}
      />
    </div>
  )
}

export default QuestionDisplay
