import { useContext } from "react";
import { SurveyContext } from "./SurveyContext";

const ScoringDisplay = ({ question, value = 0, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext);
  const scale = question.questionConfig?.scale || 100; // Default scale to 100

  const handleSeekChange = (event) => {
    onChange?.(Number(event.target.value));
  };

  const progressPercentage = (value / scale) * 100;

  return (
    <div className="flex flex-col items-center gap-4">
      {/* Display the bar with the current score */}
      <div className="text-lg font-medium">{value} / {scale}</div>

      {/* Seek bar */}
      <input
        type="range"
        min="0"
        max={scale}
        value={value}
        onChange={handleSeekChange}
        className="w-full h-3 bg-gray-200 rounded-full appearance-none cursor-pointer"
        style={{
          background: `linear-gradient(to right, var(--primary) ${progressPercentage}%, #e5e7eb ${progressPercentage}%)`,
        }}
      />

      {/* Optional: Add current score indicator */}
      <div className="text-sm text-gray-600">
        Adjust the slider to set your score.
      </div>
    </div>
  );
};

export default ScoringDisplay;
