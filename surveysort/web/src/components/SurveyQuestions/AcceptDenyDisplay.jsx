import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

// Helper to parse the stored string format back into an object
const parseChoice = (choiceString) => {
  const [value, label, text] = choiceString.split('|')
  return { value, label, text }
}

const AcceptDenyDisplay = ({ question, value, onChange, showValidation }) => {
  const { device } = useContext(SurveyContext)
  const choices = (question.questionConfig?.choices || []).map(parseChoice)

  return (
    <div className="w-full">
      <div className={`flex ${device === 'mobile' ? 'flex-col gap-2' : 'justify-center gap-8'}`}>
        {choices.map((choice) => (
          <button
            key={choice.value}
            type="button"
            onClick={() => onChange(choice.value)}
            className={`
              flex items-center justify-center p-6 rounded-lg border-2
              transition-all duration-200 ease-in-out w-full
              ${value === choice.value 
                ? choice.value === 'accept'
                  ? 'border-success bg-success/10 text-success scale-105'
                  : 'border-error bg-error/10 text-error scale-105'
                : 'border-[--answer-text]/40 bg-[--answer-text]/5 text-[--answer-text] hover:scale-102'
              }
              hover:bg-[--primary]/10 hover:border-[--primary]
              active:scale-95
            `}
          >
            <span className="text-xl font-bold">
              {choice.text}
            </span>
          </button>
        ))}
      </div>
      {showValidation && !value && (
        <p className="text-error text-xs mt-2">Please make a selection</p>
      )}
    </div>
  )
}

export default AcceptDenyDisplay 