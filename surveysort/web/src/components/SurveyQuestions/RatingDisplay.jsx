import { useContext } from 'react'
import { StarIcon as StarIconOutline } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import { SurveyContext } from './SurveyContext'

const RatingDisplay = ({ question, value, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const maxRating = question.questionConfig?.rating || 5

  return (
    <div className="flex items-center space-x-1">
      {[...Array(maxRating)].map((_, index) => (
        <button
          key={index}
          type="button"
          onClick={() => onChange?.(index + 1)}
          className={`
            p-2 rounded-lg
            focus:outline-none transition-all duration-200 ease-in-out
            hover:scale-110
            ${index < (value || 0) 
              ? 'text-[--primary] bg-[--primary]/10' 
              : 'text-[--answer-text] bg-[--answer-text]/5 hover:bg-[--primary]/10'
            }
          `}
        >
          {index < (value || 0) ? (
            <StarIconSolid className="h-8 w-8" />
          ) : (
            <StarIconOutline className="h-8 w-8" />
          )}
        </button>
      ))}
    </div>
  )
}

export default RatingDisplay
