import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const parseChoice = (choiceString) => {
  const [value, label, text] = choiceString.split('|')
  return { value: parseInt(value), label, text }
}

const EmoticonDisplay = ({ question, value, onChange, showValidation }) => {
  const { device, themeConfig } = useContext(SurveyContext)
  const choices = (question.questionConfig?.choices || []).map(parseChoice)
  console.log(choices)
  return (
    <div className="w-full">
      <div className={`grid ${device === 'mobile' ? 'grid-cols-1 gap-2' : 'grid-cols-5 gap-4'}`}>
        {choices.map((choice) => (
          <button
            key={choice.value}
            type="button"
            onClick={() => onChange?.(choice.value)}
            style={{
              borderColor: value === choice.value 
                ? 'var(--primary)' 
                : 'rgba(var(--answer-text-rgb), 0.4)',
              backgroundColor: value === choice.value 
                ? 'rgba(var(--primary-rgb), 0.1)'
                : 'rgba(var(--answer-text-rgb), 0.05)',
            }}
            className={`
              flex flex-col items-center p-3 rounded-lg
              transition-all duration-200 ease-in-out
              border-2
              ${value === choice.value 
                ? 'text-[--primary] scale-105' 
                : 'text-[--answer-text] hover:scale-102'
              }
              hover:bg-[--primary]/10 hover:border-[--primary]
              active:scale-95
            `}
          >
            <span 
              className={`
                text-3xl mb-2 transition-colors duration-200
                ${value === choice.value 
                  ? 'text-[--primary] scale-110' 
                  : 'text-[--answer-text]'
                }
              `}
            >
              {choice.label}
            </span>
            <span className="text-xs text-center text-[--answer-text]">
              {choice.text}
            </span>
          </button>
        ))}
      </div>
      {showValidation && !value && (
        <p className="text-error text-xs mt-2">Please select an option</p>
      )}
    </div>
  )
}

export default EmoticonDisplay