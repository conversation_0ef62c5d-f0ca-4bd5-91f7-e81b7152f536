import { useContext } from 'react'
import { HandThumbUpIcon, HandThumbDownIcon } from '@heroicons/react/24/outline'
import { HandThumbUpIcon as HandThumbUpSolid, HandThumbDownIcon as HandThumbDownSolid } from '@heroicons/react/24/solid'
import { SurveyContext } from './SurveyContext'

// Helper to parse the stored string format back into an object
const parseChoice = (choiceString) => {
  const [value, label, text] = choiceString.split('|')
  return { value, label, text }
}

const LikeDislikeDisplay = ({ question, value, onChange, showValidation }) => {
  const { device, themeConfig } = useContext(SurveyContext)
  const choices = (question.questionConfig?.choices || []).map(parseChoice)

  const getIcon = (choiceValue) => {
    if (value === choiceValue) {
      return choiceValue === 'like' ? HandThumbUpSolid : HandThumbDownSolid
    }
    return choiceValue === 'like' ? HandThumbUpIcon : HandThumbDownIcon
  }

  return (
    <div className="w-full">
      <div className={`flex ${device === 'mobile' ? 'flex-col gap-2' : 'justify-center gap-8'}`}>
        {choices.map((choice) => {
          const Icon = getIcon(choice.value)
          return (
            <button
              key={choice.value}
              type="button"
              onClick={() => onChange(choice.value)}
              className={`
                flex flex-col items-center p-6 rounded-lg border-2
                transition-all duration-200 ease-in-out
                ${value === choice.value 
                  ? 'border-[--primary] bg-[--primary]/10 scale-105' 
                  : 'border-[--answer-text]/40 bg-[--answer-text]/5 hover:scale-102'
                }
                hover:bg-[--primary]/10 hover:border-[--primary]
                active:scale-95
              `}
            >
              <Icon 
                className={`h-12 w-12 mb-2 transition-transform duration-200 ${
                  value === choice.value 
                    ? 'text-[--primary] scale-110' 
                    : 'text-[--answer-text]'
                }`}
              />
              <span className="text-lg font-medium text-[--answer-text]">
                {choice.text}
              </span>
            </button>
          )
        })}
      </div>
      {showValidation && !value && (
        <p className="text-error text-xs mt-2">Please make a selection</p>
      )}
    </div>
  )
}

export default LikeDislikeDisplay 