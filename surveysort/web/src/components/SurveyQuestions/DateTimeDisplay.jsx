import { useContext } from 'react'
import dayjs from 'dayjs'
import { SurveyContext } from './SurveyContext'

const DateTimeDisplay = ({ question, value, onChange, showValidation }) => {
  const { device } = useContext(SurveyContext)
  const { includeTime = false } = question.questionConfig || {}

  const handleDateChange = (e) => {
    const date = e.target.value
    if (!date) {
      onChange(null)
      return
    }

    if (includeTime && value) {
      // Keep existing time if any
      const time = dayjs(value).format('HH:mm')
      onChange(dayjs(`${date} ${time}`).format())
    } else {
      onChange(dayjs(date).format())
    }
  }

  const handleTimeChange = (e) => {
    const time = e.target.value
    if (!time) return

    const date = value ? dayjs(value).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')
    onChange(dayjs(`${date} ${time}`).format())
  }

  return (
    <div className={`w-full ${device === 'mobile' ? 'space-y-2' : 'flex gap-4'}`}>
      <div className="flex-1">
        <input
          type="date"
          value={value ? dayjs(value).format('YYYY-MM-DD') : ''}
          onChange={handleDateChange}
          className="input input-bordered w-full"
        />
      </div>
      
      {includeTime && (
        <div className="flex-1">
          <input
            type="time"
            value={value ? dayjs(value).format('HH:mm') : ''}
            onChange={handleTimeChange}
            className="input input-bordered w-full"
          />
        </div>
      )}

      {showValidation && !value && (
        <p className="text-error text-xs mt-2">Please select a date{includeTime ? ' and time' : ''}</p>
      )}
    </div>
  )
}

export default DateTimeDisplay 