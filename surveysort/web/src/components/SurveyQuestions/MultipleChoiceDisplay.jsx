import { useContext, useState } from 'react'
import { SurveyContext } from './SurveyContext'

const MultipleChoiceDisplay = ({ question, value, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const choices = question.questionConfig?.choices || []
  const cellType = question.questionConfig?.cellType || 'radio'
  const allowWriteIn = question.questionConfig?.allowWriteIn || false
  
  const [writeInValue, setWriteInValue] = useState('')
  const [selectedChoices, setSelectedChoices] = useState(
    Array.isArray(value) ? value : [value].filter(Boolean)
  )

  const handleChange = (choice, isChecked = true) => {
    if (!onChange) return

    if (cellType === 'checkbox') {
      const newChoices = isChecked 
        ? [...selectedChoices, choice]
        : selectedChoices.filter(c => c !== choice)
      
      setSelectedChoices(newChoices)
      onChange(newChoices)
    } else {
      setSelectedChoices([choice])
      onChange(choice)
    }
  }

  return (
    <div className="space-y-2">
      {choices.map((choice, index) => (
        <label 
          key={index} 
          className={`
            flex items-center gap-3 p-2 rounded-lg cursor-pointer
            transition-all duration-200 ease-in-out
            hover:bg-[--primary]/10 active:bg-[--primary]/20
          `}
        >
          <input
            type={cellType}
            checked={cellType === 'checkbox' 
              ? selectedChoices.includes(choice)
              : value === choice
            }
            onChange={(e) => handleChange(choice, e.target.checked)}
            className={`
              ${cellType === 'checkbox' ? 'checkbox' : 'radio'}
              border-2 border-[--answer-text]/40
              checked:!bg-[--answer-text]
              checked:!border-[--answer-text]
              hover:!border-[--answer-text]
              focus:!ring-2 focus:!ring-[--answer-text]/30
            `}
          />
          <span className="text-[--answer-text]">{choice}</span>
        </label>
      ))}

      {allowWriteIn && (
        <label className="flex items-center gap-3 p-2">
          <input
            type={cellType}
            checked={cellType === 'checkbox' 
              ? selectedChoices.includes(writeInValue)
              : value === writeInValue
            }
            onChange={(e) => handleChange(writeInValue, e.target.checked)}
            className={`
              ${cellType === 'checkbox' ? 'checkbox' : 'radio'}
              border-2 border-[--answer-text]/40
              checked:!bg-[--answer-text]
              checked:!border-[--answer-text]
              hover:!border-[--answer-text]
              focus:!ring-2 focus:!ring-[--answer-text]/30
            `}
          />
          <input
            type="text"
      
            value={writeInValue}
            onChange={(e) => setWriteInValue(e.target.value)}
            placeholder="Other..."
            className={`
              input input-bordered w-full input-sm
              focus:outline-none focus:ring-2 focus:ring-[--primary]/30
              text-[--answer-text] border-[--primary]/40
              placeholder:text-[--answer-text]/60
            `}
          />
        </label>
      )}
    </div>
  )
}

export default MultipleChoiceDisplay
