import { createContext, useState } from 'react'

export const SurveyContext = createContext({
  device: 'desktop',
  isPreview: false,
  showValidation: false,
  responses: {},
  setResponses: () => {},
  currentPage: 1,
  setCurrentPage: () => {},
  validateBeforeNext: () => true,
  onSubmit: () => {},
  themeConfig: {
    questionTextColor: '#000000',
    answerTextColor: '#4B5563',
    buttonColor: '#3B82F6',
    backgroundColor: '#FFFFFF',
    progressBarColor: '#3B82F6'
  }
})

export const SurveyProvider = ({ 
  children,
  device = 'desktop',
  isPreview = false,
  initialResponses = {},
  onSubmit,
  themeConfig = {}
}) => {
  const [responses, setResponses] = useState(initialResponses)
  const [currentPage, setCurrentPage] = useState(1)
  const [showValidation, setShowValidation] = useState(false)

  const defaultTheme = {
    questionTextColor: '#000000',
    answerTextColor: '#4B5563',
    buttonColor: '#3B82F6',
    backgroundColor: '#FFFFFF',
    progressBarColor: '#3B82F6'
  }

  const mergedTheme = { ...defaultTheme, ...themeConfig }

  const validateBeforeNext = () => {
    // Shared validation logic
    return true
  }

  return (
    <SurveyContext.Provider value={{
      device,
      isPreview,
      showValidation,
      responses,
      setResponses,
      currentPage,
      setCurrentPage,
      validateBeforeNext,
      onSubmit: isPreview ? () => {} : onSubmit,
      themeConfig: mergedTheme
    }}>
      {children}
    </SurveyContext.Provider>
  )
}
