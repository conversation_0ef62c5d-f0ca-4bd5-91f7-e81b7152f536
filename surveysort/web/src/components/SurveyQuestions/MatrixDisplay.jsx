import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const MatrixDisplay = ({ question, value, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)
  const rows = question.questionConfig?.rows || []
  const columns = question.questionConfig?.columns || []
  const cellType = question.questionConfig?.cellType || 'radio'

  const handleChange = (rowIndex, colIndex, checked) => {
    if (cellType === 'radio') {
      onChange?.({
        ...value,
        [rowIndex]: colIndex
      })
    } else {
      onChange?.({
        ...value,
        [rowIndex]: {
          ...value?.[rowIndex],
          [colIndex]: checked
        }
      })
    }
  }

  const isSelected = (rowIndex, colIndex) => {
    if (cellType === 'radio') {
      return value?.[rowIndex] === colIndex
    }
    return value?.[rowIndex]?.[colIndex]
  }

  return (
    <div className="overflow-x-auto">
      <table className="table w-full">
        <thead>
          <tr>
            <th className="bg-transparent text-[--answer-text]"></th>
            {columns.map((col, colIndex) => (
              <th 
                key={colIndex} 
                className="text-center bg-transparent text-[--answer-text]"
              >
                {col}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr 
              key={rowIndex}
              className="transition-all duration-200 ease-in-out hover:bg-[--primary]/5"
            >
              <td className="text-[--answer-text]">
                {row}
              </td>
              {columns.map((_, colIndex) => (
                <td key={colIndex} className="text-center">
                  <input
                    type={cellType}
                    name={cellType === 'radio' ? `matrix_${question.id}_${rowIndex}` : undefined}
                    checked={isSelected(rowIndex, colIndex)}
                    onChange={(e) => handleChange(rowIndex, colIndex, e.target.checked)}
                    className={`
                      ${cellType === 'radio' ? 'radio' : 'checkbox'}
                      border-2
                      checked:!bg-[--answer-text]
                      checked:!border-[--answer-text]
                      hover:!border-[--answer-text]
                      focus:!ring-2 focus:!ring-[--answer-text]/30
                      border-[--answer-text]/40
                    `}
                  />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default MatrixDisplay
