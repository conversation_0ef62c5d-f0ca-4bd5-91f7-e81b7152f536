const MultipleChoiceQuestion = ({ 
  choices, 
  value, 
  onChange, 
  allowMultiple,
  themeConfig 
}) => {
  return (
    <div className="space-y-2">
      {choices.map((choice, index) => (
        <label 
          key={index} 
          className="flex items-center space-x-2 cursor-pointer"
        >
          <input
            type={allowMultiple ? "checkbox" : "radio"}
            name="choice"
            value={choice}
            checked={allowMultiple ? value?.includes(choice) : value === choice}
            onChange={(e) => onChange(
              allowMultiple 
                ? (value || []).includes(choice)
                  ? (value || []).filter(v => v !== choice)
                  : [...(value || []), choice]
                : choice
            )}
            className="radio radio-primary" // DaisyUI will use our theme
            style={{
              '--tw-radio-checked-bg': themeConfig?.buttonColor
            }}
          />
          <span style={{ color: themeConfig?.answerTextColor }}>
            {choice}
          </span>
        </label>
      ))}
    </div>
  )
} 