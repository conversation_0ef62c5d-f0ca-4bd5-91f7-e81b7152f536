import { useContext } from 'react'
import { SurveyContext } from './SurveyContext'

const ContactDetailsDisplay = ({ question, value = {}, onChange, themeConfig }) => {
  const { isPreview } = useContext(SurveyContext)

  const handleChange = (field, newValue) => {
    onChange?.({
      ...value,
      [field]: newValue
    })
  }

  return (
    <div className="w-full space-y-4">
      <div>
        <label className="block mb-2 text-sm text-[--answer-text]">
          First Name
        </label>
        <input
          type="text"
          value={value.firstName || ''}
          onChange={(e) => handleChange('firstName', e.target.value)}
          className="input input-bordered w-full"
          placeholder="First name"
        />
      </div>
      
      <div>
        <label className="block mb-2 text-sm text-[--answer-text]">
          Last Name
        </label>
        <input
          type="text"
          value={value.lastName || ''}
          onChange={(e) => handleChange('lastName', e.target.value)}
          className="input input-bordered w-full"
          placeholder="Last name"
        />
      </div>

      <div>
        <label className="block mb-2 text-sm text-[--answer-text]">
          Email
        </label>
        <input
          type="email"
          value={value.email || ''}
          onChange={(e) => handleChange('email', e.target.value)}
          className="input input-bordered w-full"
          placeholder="Email address"
        />
      </div>

      <div>
        <label className="block mb-2 text-sm text-[--answer-text]">
          Phone
        </label>
        <input
          type="tel"
          value={value.phone || ''}
          onChange={(e) => handleChange('phone', e.target.value)}
          className="input input-bordered w-full"
          placeholder="Phone number"
        />
      </div>
    </div>
  )
}

export default ContactDetailsDisplay 