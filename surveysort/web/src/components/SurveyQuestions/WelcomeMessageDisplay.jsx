import { useState } from 'react'

const WelcomeMessageDisplay = ({ question, onContinue }) => {
  const [disclaimerAccepted, setDisclaimerAccepted] = useState(false)
  const config = question.questionConfig || {}

  return (
    <div className="flex items-center justify-center p-4">
    
          {config?.requireDisclaimer && config?.welcomeDisclaimer && (
            <div className="space-y-2 mt-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={disclaimerAccepted}
                  onChange={(e) => setDisclaimerAccepted(e.target.checked)}
                  className="checkbox checkbox-primary"
                />
                <span className="text-sm">{config.welcomeDisclaimer}</span>
              </label>
            </div>
          )}
   
    </div>
  )
}

export default WelcomeMessageDisplay 