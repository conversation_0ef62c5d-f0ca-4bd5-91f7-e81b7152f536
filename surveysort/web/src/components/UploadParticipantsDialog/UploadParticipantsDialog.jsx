import { useState, useRef } from 'react'

import {
  ArrowUpTrayIcon,
  DocumentTextIcon,
  XMarkIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline'

import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

const UPSERT_PARTICIPANT_LIST = gql`
  mutation UpsertParticipantList($input: UploadCsv!) {
    upsertParticipantList(input: $input) {
      created
      updated
      skipped
    }
  }
`

const GET_PRESIGNED_URL = gql`
  mutation GetPresignedUrl($input: PresignedUrlInput!) {
    getPresignedUrl(input: $input) {
      signedUrl
      filePath
    }
  }
`

const UploadParticipantsDialog = ({ surveyId, onSuccess }) => {
  const [csvFile, setCsvFile] = useState(null)
  const [csvData, setCsvData] = useState(null)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadMethod, setUploadMethod] = useState('gcp') // 'gcp' or 'direct'
  const fileInputRef = useRef(null)

  const [getPresignedUrl] = useMutation(GET_PRESIGNED_URL)

  const [upsertParticipantList] = useMutation(UPSERT_PARTICIPANT_LIST, {
    onCompleted: (data) => {
      const { created, updated, skipped } = data.upsertParticipantList
      toast.success(
        `Successfully uploaded: ${created} created, ${updated} updated, ${skipped} skipped`
      )
      handleClose()
      if (onSuccess) onSuccess()
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`)
      setIsUploading(false)
    },
  })

  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      toast.error('Please upload a CSV file')
      return
    }

    setCsvFile(file)

    // Read the file
    const reader = new FileReader()
    reader.onload = (event) => {
      try {
        const csvText = event.target.result
        setCsvData(csvText)
        setIsPreviewMode(true)
      } catch (error) {
        toast.error('Failed to read CSV file')
        console.error(error)
      }
    }
    reader.readAsText(file)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0]
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        toast.error('Please upload a CSV file')
        return
      }

      setCsvFile(file)

      // Read the file
      const reader = new FileReader()
      reader.onload = (event) => {
        try {
          const csvText = event.target.result
          setCsvData(csvText)
          setIsPreviewMode(true)
        } catch (error) {
          toast.error('Failed to read CSV file')
          console.error(error)
        }
      }
      reader.readAsText(file)
    }
  }

  const handleUpload = async () => {
    if (!csvFile && !csvData) return

    setIsUploading(true)

    try {
      if (uploadMethod === 'gcp' && csvFile) {
        // Upload to GCP first
        const { data } = await getPresignedUrl({
          variables: {
            input: {
              fileName: csvFile.name,
              fileType: csvFile.type || 'text/csv',
            },
          },
        })

        const { signedUrl, filePath } = data.getPresignedUrl

        // Upload file to GCP
        const uploadResult = await fetch(signedUrl, {
          method: 'PUT',
          body: csvFile,
          headers: {
            'Content-Type': csvFile.type || 'text/csv',
          },
        })

        if (!uploadResult.ok) {
          throw new Error('Failed to upload file to GCP')
        }

        // Call upsertParticipantList with GCP file path
        await upsertParticipantList({
          variables: {
            input: {
              surveyId,
              gcpFilePath: filePath,
            },
          },
        })
      } else {
        // Fallback to direct CSV data upload
        await upsertParticipantList({
          variables: {
            input: {
              surveyId,
              data: csvData,
            },
          },
        })
      }
    } catch (error) {
      toast.error(`Upload failed: ${error.message}`)
      setIsUploading(false)
    }
  }

  const handleClose = () => {
    setCsvFile(null)
    setCsvData(null)
    setIsPreviewMode(false)
    setIsUploading(false)
    setUploadMethod('gcp')
    if (fileInputRef.current) fileInputRef.current.value = ''
    const modal = document.getElementById('upload-participants-modal')
    if (modal) modal.close()
  }

  const renderPreview = () => {
    if (!csvData) return null

    const lines = csvData.split('\n')
    const headers = lines[0].split(',')
    const previewRows = lines.slice(1, 6) // Show first 5 rows

    return (
      <div className="mt-4">
        <h3 className="mb-2 text-sm font-medium">Preview</h3>
        <div className="overflow-x-auto">
          <table className="table table-xs">
            <thead>
              <tr>
                {headers.map((header, index) => (
                  <th key={index}>{header.trim()}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {previewRows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.split(',').map((cell, cellIndex) => (
                    <td key={cellIndex}>{cell.trim()}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {lines.length > 6 && (
          <p className="mt-2 text-xs text-base-content/70">
            +{lines.length - 6} more rows not shown in preview
          </p>
        )}
      </div>
    )
  }

  return (
    <dialog id="upload-participants-modal" className="modal">
      <div className="modal-box max-w-2xl">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-bold">Upload Participants</h3>
          <button className="btn btn-ghost btn-sm" onClick={handleClose}>
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="divider"></div>

        {!isPreviewMode ? (
          <div
            className="rounded-lg border-2 border-dashed border-base-300 p-8 text-center"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-base-content/50" />
            <p className="mt-4 text-sm">
              Drag and drop your CSV file here, or{' '}
              <button
                className="hover:text-primary-focus text-primary"
                onClick={() => fileInputRef.current?.click()}
              >
                browse
              </button>
            </p>
            <p className="mt-2 text-xs text-base-content/70">
              CSV must have at least 'email' column. 'name' column is also
              recommended.
            </p>
            <input
              type="file"
              accept=".csv"
              className="hidden"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
          </div>
        ) : (
          <div>
            <div className="mb-4 flex items-center">
              <DocumentTextIcon className="mr-2 h-6 w-6 text-primary" />
              <span className="text-sm font-medium">{csvFile.name}</span>
              <button
                className="btn btn-ghost btn-xs ml-auto"
                onClick={() => {
                  setCsvFile(null)
                  setCsvData(null)
                  setIsPreviewMode(false)
                  if (fileInputRef.current) fileInputRef.current.value = ''
                }}
              >
                Change
              </button>
            </div>

            {renderPreview()}

            <div className="alert alert-info mt-4">
              <ExclamationCircleIcon className="h-5 w-5" />
              <div className="text-xs">
                <p>
                  <strong>Important notes:</strong>
                </p>
                <ul className="mt-1 list-disc pl-4">
                  <li>Make sure your CSV has an 'email' column (required)</li>
                  <li>
                    Emails already in the suppression list will be skipped
                  </li>
                  <li>
                    Existing participants with matching emails will be updated
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="modal-action">
          <button className="btn btn-ghost" onClick={handleClose}>
            Cancel
          </button>
          <button
            className="btn btn-primary"
            disabled={!csvData || isUploading}
            onClick={handleUpload}
          >
            {isUploading ? (
              <>
                <span className="loading loading-spinner loading-xs"></span>
                Uploading...
              </>
            ) : (
              'Upload'
            )}
          </button>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button onClick={handleClose}>close</button>
      </form>
    </dialog>
  )
}

export default UploadParticipantsDialog
