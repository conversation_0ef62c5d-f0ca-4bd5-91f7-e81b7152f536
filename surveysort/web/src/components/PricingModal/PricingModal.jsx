import { useState, useEffect } from 'react'
import { useLocation, navigate, routes } from '@redwoodjs/router'
import { useQuery, useMutation } from '@redwoodjs/web'
import { RadioGroup, Radio } from '@headlessui/react'
import { CheckIcon } from '@heroicons/react/20/solid'
import { EnvelopeIcon } from '@heroicons/react/24/outline'
import { Dialog } from '@headlessui/react'
import { Badge } from 'src/components/catalyst/badge'
import { toast } from '@redwoodjs/web/toast'

import { useAuth } from 'src/auth'

const FETCH_PLANS_QUERY = gql`
  query FetchPlans {
    getPlans {
      priceId
      productId
      name
      description
      monthlyPrice
      yearlyPrice
      yearlyPriceTotal
      interval
      currency
      features
      isCurrent
      meteredComponents {
        name
        includedQuantity
        unitAmount
      }
      discount
      isEnterprise
      hideFromPricing
      contactSales
    }
  }
`

const CREATE_CHECKOUT_MUTATION = gql`
  mutation CreateCheckoutSession($priceId: String!) {
    createCheckoutSession(priceId: $priceId) {
      sessionId
      url
    }
  }
`

const frequencies = [
  { value: 'month', label: 'Monthly', priceSuffix: '/month' },
  { value: 'year', label: 'Annually', priceSuffix: '/year' },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

const LoadingState = () => (
  <div className="flex items-center justify-center p-8">
    <span className="loading loading-spinner loading-lg text-primary"></span>
  </div>
)

const ErrorState = ({ error }) => (
  <div className="p-8 text-center">
    <p className="text-error">{error.message}</p>
    <button 
      className="btn btn-error btn-sm mt-4"
      onClick={() => window.location.reload()}
    >
      Retry
    </button>
  </div>
)

const PricingModal = ({ open, onClose }) => {
  const location = useLocation()
  const { currentUser } = useAuth()
  const [selectedPriceId, setSelectedPriceId] = useState(null)
  const [frequency, setFrequency] = useState('month')
  const [showEnterpriseForm, setShowEnterpriseForm] = useState(false)
  const [enterpriseForm, setEnterpriseForm] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  })

  const { loading, error, data } = useQuery(FETCH_PLANS_QUERY, {
    fetchPolicy: 'network-only'
  })
  
  console.log(data)

  const [createCheckoutSession] = useMutation(CREATE_CHECKOUT_MUTATION)

  useEffect(() => {
    if (location.search) {
      const params = new URLSearchParams(location.search)
      const showPlans = params.get('showPlans')
      if (showPlans === 'true') {
        setIsOpen(true)
      }
    }
  }, [location.search])

  const handleCloseDialog = () => {
    setIsOpen(false)
    const params = new URLSearchParams(location.search)
    params.delete('showPlans')
    
    if (params.toString()) {
      navigate(`${location.pathname}?${params.toString()}`)
    } else {
      navigate(location.pathname)
    }
  }

  const handleCheckout = async () => {
    if (!selectedPriceId) return
    try {
      const { data } = await createCheckoutSession({
        variables: { priceId: selectedPriceId }
      })
      if (data?.createCheckoutSession?.url) {
        window.location.href = data.createCheckoutSession.url
      }
    } catch (error) {
      console.error('Checkout error:', error)
    }
  }

  const renderFrequencySelector = () => (
    <div className="mt-4 flex justify-center">
      <div className="join">
        {frequencies.map((option) => (
          <input 
            key={option.value}
            type="radio"
            name="frequency"
            className="join-item btn btn-sm"
            aria-label={option.label}
            checked={frequency === option.value}
            onChange={() => setFrequency(option.value)}
            data-checked={frequency === option.value}
          />
        ))}
      </div>
    </div>
  )

  const renderPlanCard = (plan) => {
    if (plan.hideFromPricing) return null

    const price = frequency === 'month' ? plan.monthlyPrice : plan.yearlyPrice
    const isEnterprise = plan.isEnterprise

    return (
      <div
        key={plan.priceId}
        className={`card backdrop-blur-sm shadow-sm border border-base-100/20
          ${isEnterprise 
            ? 'bg-neutral/90 text-neutral-content' 
            : 'bg-base-100/70'}`}
      >
        <div className="py-2 px-4">
          <div className={`text-sm py-1 ${isEnterprise ? 'text-neutral-content' : 'text-neutral'}`}>
            {plan.name}
          </div>
          
          <p className={`text-xs ${isEnterprise ? 'text-neutral-content/70' : 'text-base-content/70'}`}>
            {plan.description}
          </p>

          <div className={`flex py-2 items-baseline gap-x-1 ${isEnterprise ? 'text-neutral-content' : 'text-base-content'}`}>
            {price ? (
              <>
                <span className="text-xl font-bold">
                  ${price}
                </span>
                <span className="text-sm opacity-75">
                  /month
                </span>
                {frequency === 'year' && (
                  <span className="text-xs opacity-75">
                    (billed monthly)
                  </span>
                )}
              </>
            ) : (
              <span className="text-xl font-bold">
                Custom
              </span>
            )}
          </div>

          <button
            onClick={() => isEnterprise ? setShowEnterpriseForm(true) : setSelectedPriceId(plan.priceId)}
            className={`btn btn-sm w-full mb-2 
              ${isEnterprise 
                ? 'btn-outline text-neutral-content hover:text-neutral' 
                : 'btn-outline btn-primary'}
              ${plan.isCurrent ? 'btn-disabled' : ''}`}
            disabled={plan.isCurrent}
          >
            {plan.isCurrent ? 'Current Plan' : 
             isEnterprise ? 'Contact Sales' : 'Select Plan'}
          </button>

          <ul className={`space-y-1 py-2 ${isEnterprise ? 'text-neutral-content/70' : 'text-neutral/70'}`}>
            {plan.features.map((feature) => (
              <li key={feature} className="flex gap-x-2 items-center">
                <CheckIcon
                  className={`h-4 w-4 flex-none ${isEnterprise ? 'text-neutral-content' : 'text-primary'}`}
                  aria-hidden="true"
                />
                <span className="text-xs">{feature}</span>
              </li>
            ))}
            {plan.meteredComponents.map((component) => (
              <li key={component.name} className="flex gap-x-2 items-center">
                <CheckIcon
                  className={`h-4 w-4 flex-none ${isEnterprise ? 'text-neutral-content' : 'text-primary'}`}
                  aria-hidden="true"
                />
                <span className="text-xs">{`${component.includedQuantity.toLocaleString()} ${component.name} included`}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  }

  const handleEnterpriseSubmit = async (e) => {
    e.preventDefault()
    try {
      await requestEnterprisePlan({
        variables: enterpriseForm
      })
      setShowEnterpriseForm(false)
      toast.success('Request sent successfully!')
    } catch (error) {
      toast.error('Failed to send request')
    }
  }

  if (loading) return <Dialog open={open} onClose={onClose}><LoadingState /></Dialog>
  if (error) return <Dialog open={open} onClose={onClose}><ErrorState error={error} /></Dialog>

  const plans = data?.getPlans || []
  
  console.log('All plans:', plans)
  console.log('Plans with enterprise:', plans.filter(p => p.isEnterprise))

  const sortedPlans = [...plans].sort((a, b) => {
    if (a.isEnterprise && !b.isEnterprise) return 1
    if (!a.isEnterprise && b.isEnterprise) return -1
    return (a.monthlyPrice || 0) - (b.monthlyPrice || 0)
  })

  console.log('Sorted plans:', sortedPlans)

  return (
    <>
      <Dialog 
        open={open} 
        onClose={onClose}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/50" aria-hidden="true" />
        
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-2">
            <Dialog.Panel className="modal-box w-full max-w-7xl p-4 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100">
              <Dialog.Title className="text-xl font-bold text-center mb-1">
                Choose Your Plan
              </Dialog.Title>
              
              <Dialog.Description className="text-center text-sm opacity-75 mb-2">
                All plans include a 7-day free trial. You can upgrade, downgrade, or cancel anytime.
              </Dialog.Description>

              {renderFrequencySelector()}

              <div className="mt-2 w-full">
                <div className="grid gap-8 sm:grid-cols-1 lg:grid-cols-3">
                  {sortedPlans.map(renderPlanCard)}
                </div>
              </div>

              <div className="modal-action mt-4">
                <button
                  className={`btn btn-sm ${selectedPriceId ? 'btn-primary' : 'btn-disabled'}`}
                  onClick={handleCheckout}
                  disabled={!selectedPriceId}
                >
                  {selectedPriceId ? 'Upgrade to This Plan' : 'Select a Plan'}
                </button>
                <button className="btn btn-sm btn-outline" onClick={onClose}>
                  Close
                </button>
              </div>
            </Dialog.Panel>
          </div>
        </div>
      </Dialog>

      <Dialog 
        open={showEnterpriseForm} 
        onClose={() => setShowEnterpriseForm(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/50" aria-hidden="true" />
        
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Dialog.Panel className="modal-box max-w-md">
              <Dialog.Title className="font-bold text-lg mb-4">
                Contact Sales
              </Dialog.Title>

              <form onSubmit={handleEnterpriseSubmit} className="space-y-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Name</span>
                  </label>
                  <input
                    type="text"
                    required
                    className="input input-bordered w-full"
                    value={enterpriseForm.name}
                    onChange={(e) => setEnterpriseForm(prev => ({
                      ...prev,
                      name: e.target.value
                    }))}
                  />
                </div>
                {/* Add similar fields for email, company, message */}
                <button
                  type="submit"
                  className="btn btn-primary w-full"
                >
                  Submit Request
                </button>
              </form>
            </Dialog.Panel>
          </div>
        </div>
      </Dialog>
    </>
  )
}

export default PricingModal
