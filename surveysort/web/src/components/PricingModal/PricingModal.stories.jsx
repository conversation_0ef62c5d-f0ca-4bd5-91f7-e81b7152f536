import { mockGraphQLQuery, mockGraphQLMutation } from '@redwoodjs/testing/web'

import PricingModal from './PricingModal'

export default {
  component: PricingModal,
  title: 'Components/PricingModal',
}

const mockPlans = [
  {
    id: '1',
    name: 'Individual Researcher',
    description: 'Individual Researcher tier for survey tool',
    price: 49,
    interval: 'month',
    currency: 'usd',
    features: [
      'Unlimited Surveys',
      'Up to 5,000 Survey Responses Per month',
      'Up to 100 AI Generated Surveys Per month',
      'Single user',
      'Email support',
    ],
    isCurrent: false,
  },
  {
    id: '2',
    name: 'Individual Researcher',
    description: 'Individual Researcher tier for survey tool',
    price: 34,
    interval: 'year',
    currency: 'usd',
    features: [
      'Unlimited Surveys',
      'Up to 5,000 Survey Responses Per month',
      'Up to 100 AI Generated Surveys Per month',
      'Single user',
      'Email support',
    ],
    isCurrent: false,
    discount: 30, // Added discount percentage
  },
  {
    id: '3',
    name: 'Research Teams',
    description: 'Research Teams tier for survey tool',
    price: 129,
    interval: 'month',
    currency: 'usd',
    features: [
      'Unlimited Surveys',
      'Up to 20,000 Survey Responses Per month',
      'Up to 1000 AI Generated Surveys Per month',
      'Multiple users',
      'Priority email support',
    ],
    isCurrent: false,
  },
  {
    id: '4',
    name: 'Research Teams',
    description: 'Research Teams tier for survey tool',
    price: 99,
    interval: 'year',
    currency: 'usd',
    features: [
      'Unlimited Surveys',
      'Up to 20,000 Survey Responses Per month',
      'Up to 1000 AI Generated Surveys Per month',
      'Multiple users',
      'Priority email support',
    ],
    isCurrent: false,
    discount: 15, // Added discount percentage
  },
  {
    id: '5',
    name: 'Enterprise',
    description: 'Custom solution for large teams',
    price: null,
    interval: 'custom',
    currency: 'usd',
    features: [
      'Unlimited Surveys',
      'Unlimited Survey Responses',
      'Multiple users',
      'Dedicated support',
      'Custom pricing available',
    ],
    isCurrent: false,
  },
]

export const Primary = () => {
  mockGraphQLQuery('FetchPlans', () => {
    return {
      plans: mockPlans,
    }
  })

  mockGraphQLMutation('CreateCheckoutSession', () => {
    return {
      createCheckoutSession: {
        sessionId: 'session123',
        url: 'https://checkout.stripe.com/pay/session123',
      },
    }
  })

  return <PricingModal isOpen={true} onClose={() => {}} />
}
