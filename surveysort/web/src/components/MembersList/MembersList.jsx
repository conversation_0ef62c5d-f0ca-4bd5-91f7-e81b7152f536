import { useState } from 'react'
import { useMutation, useQuery } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import {
  Table,
  TableHead,
  TableRow,
  TableHeaderCell,
  TableBody,
  TableCell,
} from '@tremor/react'
import { 
  UserPlusIcon,
  ArrowPathIcon,
  XMarkIcon,
  ClipboardIcon,
  CheckIcon
} from '@heroicons/react/24/outline'
import { Badge } from 'src/components/catalyst/badge'
import { Dialog, DialogBody, DialogTitle, DialogActions } from 'src/components/catalyst/dialog'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from 'src/auth'
import { Tab } from '@headlessui/react'
import cn from 'classnames'

const QUERY = gql`
  query GetTeamMembers {
    teamMembers {
      activeMembers {
        id
        email
        roles
        createdAt
      }
      pendingInvites {
        id
        recipientEmail
        inviteUrl
        createdAt
        expiresAt
      }
    }
  }
`

const INVITE_MEMBER = gql`
  mutation InviteMember($email: String!) {
    inviteMember(email: $email) {
      id
      recipientEmail
      token
      inviteUrl
      createdAt
      expiresAt
      accountId
      senderId
    }
  }
`

const RESEND_INVITE = gql`
  mutation ResendInvite($id: String!) {
    resendInvite(id: $id) {
      id
      recipientEmail
      token
      inviteUrl
      createdAt
      expiresAt
      accountId
      senderId
    }
  }
`

const REVOKE_INVITE = gql`
  mutation RevokeInvite($id: String!) {
    revokeInvite(id: $id)
  }
`

const REMOVE_MEMBER = gql`
  mutation RemoveMember($id: String!) {
    removeMember(id: $id)
  }
`

const tabs = [
  { name: 'Active Members', section: 'active' },
  { name: 'Pending Invites', section: 'pending' },
]

const MembersList = () => {
  const {hasRole} = useAuth()
  const [isInviteOpen, setIsInviteOpen] = useState(false)
  const [isResendOpen, setIsResendOpen] = useState(false)
  const [selectedInvite, setSelectedInvite] = useState(null)
  const [email, setEmail] = useState('')
  const [inviting, setInviting] = useState(false)
  const [copiedId, setCopiedId] = useState(null)
  const [selectedTab, setSelectedTab] = useState('active')
  
  const { data, loading, refetch } = useQuery(QUERY)

  const handleCopyLink = (inviteUrl, inviteId) => {
    navigator.clipboard.writeText(inviteUrl)
    setCopiedId(inviteId)
    setTimeout(() => setCopiedId(null), 2000)
  }

  const [inviteMember] = useMutation(INVITE_MEMBER, {
    onCompleted: () => {
      toast.success('Invitation sent successfully!')
      setEmail('')
      setInviting(false)
      setIsInviteOpen(false)
      refetch()
    },
    onError: (error) => {
      setInviting(false)
      if (error.message.includes('already has an account')) {
        toast.error(error.message, { duration: 6000 })
      } else if (error.message.includes('already a member')) {
        toast.error('This user is already a member of your team')
      } else if (error.message.includes('business email')) {
        toast.error('Please use a business email address')
      } else {
        toast.error('Failed to send invitation')
      }
    }
  })

  const [resendInvite] = useMutation(RESEND_INVITE, {
    onCompleted: () => {
      toast.success('Invitation resent successfully!')
      refetch()
    }
  })

  const [revokeInvite] = useMutation(REVOKE_INVITE, {
    onCompleted: () => {
      toast.success('Invitation revoked')
      refetch()
    }
  })

  const [removeMember] = useMutation(REMOVE_MEMBER, {
    onCompleted: () => {
      toast.success('Member removed successfully')
      refetch()
    }
  })

  const handleInvite = async (e) => {
    e.preventDefault()
    setInviting(true)
    inviteMember({ variables: { email } })
  }

  const handleResend = (invite) => {
    setSelectedInvite(invite)
    setIsResendOpen(true)
  }

  const confirmResend = () => {
    resendInvite({ variables: { id: selectedInvite.id } })
    setIsResendOpen(false)
    setSelectedInvite(null)
  }


  return (
    <div className="p-4 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold tracking-tight">Team Members</h2>
          <p className="mt-1 text-sm text-base-content/70">
            Manage your team's access and permissions
          </p>
        </div>
        <button 
          onClick={() => setIsInviteOpen(true)}
          className="btn btn-outline btn-sm"
        >
          <UserPlusIcon className="h-4 w-4 mr-2" />
          Invite Member
        </button>
      </div>

      <div className="hidden sm:block">
        <nav className="flex space-x-1 p-2" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.name}
              onClick={() => setSelectedTab(tab.section)}
              className={cn(
                'rounded-md px-3 py-1.5 text-xs font-medium',
                selectedTab === tab.section
                  ? 'bg-base-200 text-primary'
                  : 'text-base-content/70 hover:text-base-content hover:bg-base-200/50'
              )}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Mobile Tabs */}
      <div className="sm:hidden">
        <label htmlFor="tabs" className="sr-only">
          Select a tab
        </label>
        <select
          id="tabs"
          name="tabs"
          value={selectedTab}
          onChange={(e) => setSelectedTab(e.target.value)}
          className="block w-full rounded-md border-base-300 focus:border-primary focus:ring-primary text-xs"
        >
          {tabs.map((tab) => (
            <option key={tab.name} value={tab.section}>
              {tab.name}
            </option>
          ))}
        </select>
      </div>

      <div className="mt-4">
        {selectedTab === 'active' ? (
          <AnimatePresence mode="wait">
            {loading ? (
              <div className="p-4 space-y-6">
                {/* Header skeleton */}
                <div className="flex justify-between items-center">
                  <div className="space-y-2">
                    <div className="skeleton h-6 w-32"></div>
                    <div className="skeleton h-4 w-48"></div>
                  </div>
                  <div className="skeleton w-32 h-8"></div>
                </div>

                {/* Tabs skeleton */}
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-24"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>

                {/* Table skeleton */}
                <div className="rounded-lg border border-base-300">
                  <div className="p-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-center gap-4 py-3 border-b border-base-200 last:border-0">
                        <div className="skeleton h-4 w-48"></div>
                        <div className="skeleton h-4 w-24"></div>
                        <div className="skeleton h-4 w-24"></div>
                        <div className="skeleton h-4 w-20"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <motion.div
                key="active-members"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="rounded-lg border border-base-300"
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableHeaderCell>Email</TableHeaderCell>
                      <TableHeaderCell>Role</TableHeaderCell>
                      <TableHeaderCell>Joined</TableHeaderCell>
                      <TableHeaderCell>Actions</TableHeaderCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <AnimatePresence>
                      {data?.teamMembers.activeMembers.map((member) => (
                        <motion.tr
                          key={member.id}
     
                          initial="hidden"
                          animate="visible"
                          exit="exit"
                          layout
                        >
                          <TableCell>{member.email}</TableCell>
                          <TableCell>
                            <Badge color={member.roles.includes('ADMIN') ? 'blue' : 'zinc'}>
                              {member.roles.join(', ')}
                            </Badge>
                          </TableCell>
                          <TableCell>{new Date(member.createdAt).toLocaleDateString()}</TableCell>
                          <TableCell>
                            {member.roles.includes('ADMIN') ? (
                              <span className="text-sm text-base-content/70">Admin cannot be removed</span>
                            ) : hasRole('ADMIN') ? (
                              <button
                                onClick={() => {
                                  if (confirm('Are you sure you want to remove this member?')) {
                                    removeMember({ variables: { id: member.id } })
                                  }
                                }}
                                className="btn btn-ghost btn-sm text-error"
                              >
                                Remove
                              </button>
                            ) : null}
                          </TableCell>
                        </motion.tr>
                      ))}
                    </AnimatePresence>
                  </TableBody>
                </Table>
              </motion.div>
            )}
          </AnimatePresence>
        ) : (
          <motion.div
            key="pending-invites"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="rounded-lg border border-base-300"
          >
            {data?.teamMembers.pendingInvites.length > 0 ? (
              <Table>
                <TableHead>
                  <TableRow>
                    <TableHeaderCell>Email</TableHeaderCell>
                    <TableHeaderCell>Invitation Link</TableHeaderCell>
                    <TableHeaderCell>Sent</TableHeaderCell>
                    <TableHeaderCell>Expires</TableHeaderCell>
                    <TableHeaderCell>Actions</TableHeaderCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <AnimatePresence>
                    {data.teamMembers.pendingInvites.map((invite) => (
                      <motion.tr
                        key={invite.id}

                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        layout
                      >
                        <TableCell>{invite.recipientEmail}</TableCell>
                        <TableCell>
                          <button
                            onClick={() => handleCopyLink(invite.inviteUrl, invite.id)}
                            className="btn btn-ghost btn-xs tooltip tooltip-top"
                            data-tip={copiedId === invite.id ? 'Copied!' : 'Copy link'}
                          >
                            {copiedId === invite.id ? (
                              <CheckIcon className="h-4 w-4 text-success" />
                            ) : (
                              <ClipboardIcon className="h-4 w-4" />
                            )}
                          </button>
                        </TableCell>
                        <TableCell>{new Date(invite.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>{new Date(invite.expiresAt).toLocaleDateString()}</TableCell>
                        <TableCell className="space-x-2">
                          <button
                            onClick={() => handleResend(invite)}
                            className="btn btn-ghost btn-xs"
                            title="Resend invitation"
                          >
                            <ArrowPathIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              if (confirm('Are you sure you want to revoke this invitation?')) {
                                revokeInvite({ variables: { id: invite.id } })
                              }
                            }}
                            className="btn btn-ghost btn-xs text-error"
                            title="Revoke invitation"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </TableCell>
                      </motion.tr>
                    ))}
                  </AnimatePresence>
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-base-content/70">
                No pending invitations
              </div>
            )}
          </motion.div>
        )}
      </div>

      <Dialog open={isInviteOpen} onClose={() => setIsInviteOpen(false)}>
        <DialogTitle>Invite Team Member</DialogTitle>
        <DialogBody>
          <form onSubmit={handleInvite} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="input input-bordered w-full"
                required
              />
              <p className="text-xs text-base-content/70 mt-1">
                Only business email addresses are allowed
              </p>
            </div>
            <div className="flex justify-end gap-2">
              <button 
                type="button"
                onClick={() => setIsInviteOpen(false)}
                className="btn btn-ghost btn-sm"
              >
                Cancel
              </button>
              <button 
                type="submit"
                className="btn btn-primary btn-sm"
                disabled={inviting}
              >
                {inviting ? (
                  <>
                    <span className="loading loading-spinner loading-xs"></span>
                    Sending...
                  </>
                ) : 'Send Invitation'}
              </button>
            </div>
          </form>
        </DialogBody>
      </Dialog>

      <Dialog open={isResendOpen} onClose={() => setIsResendOpen(false)}>
        <DialogTitle>Resend Invitation</DialogTitle>
        <DialogBody>
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-full bg-warning/20">
              <ArrowPathIcon className="h-6 w-6 text-warning" />
            </div>
            <div>
              <p>Are you sure you want to resend the invitation to:</p>
              <p className="font-medium">{selectedInvite?.recipientEmail}</p>
            </div>
          </div>
        </DialogBody>
        <DialogActions>
          <button 
            onClick={() => setIsResendOpen(false)}
            className="btn btn-ghost btn-sm"
          >
            Cancel
          </button>
          <button 
            onClick={confirmResend}
            className="btn btn-warning btn-sm"
          >
            Resend Invitation
          </button>
        </DialogActions>
      </Dialog>
    </div>
  )
}

export default MembersList 