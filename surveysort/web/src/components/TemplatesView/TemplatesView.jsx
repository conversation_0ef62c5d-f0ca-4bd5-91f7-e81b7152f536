import { useState } from 'react'
import { useQuery } from '@redwoodjs/web'
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import { motion } from 'framer-motion'
import { Dialog } from '@headlessui/react'
import { Form, Label, TextField, Submit, FieldError, TextAreaField } from '@redwoodjs/forms'
import { SparklesIcon, InformationCircleIcon, BeakerIcon } from '@heroicons/react/24/outline'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { navigate, routes } from '@redwoodjs/router'
import TemplateCustomizeDialog from '../TemplateCustomizeDialog/TemplateCustomizeDialog'

const QUERY = gql`
  query GetSurveyTemplates {
    surveyTemplates {
      id
      name
      description
      questions {
        id
        title
        type
        choices
      }
    }
  }
`

const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[^\w_]+/g, '')
    .replace(/__+/g, '_')
    .replace(/^_+/, '')
    .replace(/_+$/, '')
}

const TemplatesView = ({ onTemplateSelect }) => {
  const { loading, error, data } = useQuery(QUERY)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [showDialog, setShowDialog] = useState(false)

  const handleTemplateClick = (template) => {
    setSelectedTemplate(template)
    setShowDialog(true)
  }

  const filteredTemplates = data?.surveyTemplates?.filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (loading) return (
    <div className="p-4 space-y-6">
      {/* Header skeleton */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <div className="skeleton h-6 w-40"></div>
          <div className="skeleton h-4 w-64"></div>
        </div>
        <div className="skeleton w-72 h-8"></div>
      </div>

      {/* Templates grid skeleton */}
      <div className="grid grid-cols-1 gap-y-4 sm:grid-cols-3 sm:gap-x-4 sm:gap-y-6 lg:grid-cols-4 lg:gap-x-6">
        {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
          <div key={i} className="rounded-lg border border-base-300 overflow-hidden">
            <div className="skeleton h-32 w-full"></div>
            <div className="p-4 space-y-3">
              <div className="skeleton h-4 w-3/4"></div>
              <div className="skeleton h-3 w-full"></div>
              <div className="skeleton h-3 w-1/2"></div>
              <div className="skeleton h-8 w-full"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
  
  if (error) return <div className="p-4 text-error">Error loading templates</div>

  return (
    <>
      <div className="p-4 space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <h2 className="text-lg font-semibold tracking-tight text-gray-900">Survey Templates</h2>
            <p className="text-sm text-gray-500">
              Start with expert-designed templates, customized by our AI for your needs
            </p>
          </div>
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-4 w-4 text-base-content/50" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search templates..."
              className="input input-bordered input-sm pl-10 pr-4 w-72"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <motion.div 
          className="grid grid-cols-1 gap-y-4 sm:grid-cols-3 sm:gap-x-4 sm:gap-y-6 lg:grid-cols-4 lg:gap-x-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {filteredTemplates.map((template) => (
            <motion.div
              key={template.id}
              className="group relative flex flex-col overflow-hidden rounded-lg border border-base-300 bg-base-100 cursor-pointer hover:border-primary/50 hover:shadow-md transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              onClick={() => handleTemplateClick(template)}
            >
              <div className="w-full h-32">
                <img
                  src={`/template_imgs/${slugify(template.name)}.svg`}
                  alt={template.name}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = '/template_imgs/default_template.svg';
                  }}
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="flex flex-1 flex-col space-y-2 p-4">
                <h3 className="text-sm font-medium text-base-content">
                  {template.name}
                </h3>
                <p className="text-xs text-base-content/70">{template.description}</p>
                <div className="flex flex-1 flex-col justify-end">
                  <p className="text-xs italic text-base-content/70">
                    {template.questions.length} questions
                  </p>
                  <button
                    className="mt-2 btn btn-outline btn-xs w-full group-hover:btn-primary transition-colors duration-200"
                  >
                    Use Template
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      <TemplateCustomizeDialog 
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        template={selectedTemplate}
      />
    </>
  )
}

export default TemplatesView 