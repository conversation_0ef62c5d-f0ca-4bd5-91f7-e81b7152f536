import {
  Form,
  Label,
  TextField,
  Submit,
  FormError,
  FieldError,
} from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

const UPDATE_SURVEY_MUTATION = gql`
  mutation EditSurveyMutation($id: String!, $input: UpdateSurveyInput!) {
    updateSurvey(id: $id, input: $input) {
      id
      title
      audience
      surveyObjective
    }
  }
`

const EditSurveyForm = ({ survey, onClose }) => {
  const [updateSurvey, { loading, error }] = useMutation(
    UPDATE_SURVEY_MUTATION,
    {
      onCompleted: () => {
        toast.success('Survey updated successfully')
        onClose()
      },
      onError: (error) => {
        toast.error(error.message)
      },
    }
  )

  const onSubmit = (data) => {
    updateSurvey({ variables: { id: survey.id, input: data } })
  }

  return (
    <Form onSubmit={onSubmit} className="form-control w-full space-y-4">
      <FormError error={error} />

      <div className="alert alert-info">
        <div className="flex-1">
          <label>
            <h3 className="font-bold">Important Notice</h3>
            <p className="text-sm">
              Updating survey details will delete existing questions. The{' '}
              <span className="font-bold">Survey Design AI Agent</span> will
              regenerate questions based on the new information.
            </p>
          </label>
        </div>
      </div>

      <div>
        <Label name="title" className="label">
          <span className="label-text">Survey Title</span>
        </Label>
        <TextField
          name="title"
          defaultValue={survey.title}
          className="input input-bordered w-full"
          validation={{ required: true }}
          placeholder="Enter survey title"
        />
        <FieldError name="title" className="mt-1 text-xs text-error" />
      </div>

      <div>
        <Label name="surveyObjective" className="label">
          <span className="label-text">Survey Objective</span>
        </Label>
        <TextField
          name="surveyObjective"
          defaultValue={survey.surveyObjective}
          className="input input-bordered w-full"
          validation={{ required: true }}
          placeholder="What do you want to learn from this survey?"
        />
        <FieldError
          name="surveyObjective"
          className="mt-1 text-xs text-error"
        />
      </div>

      <div>
        <Label name="audience" className="label">
          <span className="label-text">Target Audience</span>
        </Label>
        <TextField
          name="audience"
          defaultValue={survey.audience}
          className="input input-bordered w-full"
          validation={{ required: true }}
          placeholder="Who will be taking this survey?"
        />
        <FieldError name="audience" className="mt-1 text-xs text-error" />
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-ghost btn-sm"
        >
          Cancel
        </button>
        <Submit className="btn btn-primary btn-sm" disabled={loading}>
          {loading ? (
            <>
              <span className="loading loading-spinner loading-xs"></span>
              Updating...
            </>
          ) : (
            'Update Survey'
          )}
        </Submit>
      </div>
    </Form>
  )
}

export default EditSurveyForm
