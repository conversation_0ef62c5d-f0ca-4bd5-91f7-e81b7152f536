import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { navigate, routes } from '@redwoodjs/router'
import {
  PencilIcon,
  ShareIcon,
  TrashIcon,
  CheckCircleIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline'

const UPDATE_SURVEY_STATUS_MUTATION = gql`
  mutation UpdateSurveyStatusMutation($id: String!, $status: SurveyStatus!) {
    updateSurvey(id: $id, input: { status: $status }) {
      id
      status
    }
  }
`

const SurveyDetailActions = ({ survey, onEdit, onDelete }) => {
  const [updateSurveyStatus] = useMutation(UPDATE_SURVEY_STATUS_MUTATION, {
    onCompleted: () => {
      toast.success(
        survey.status === 'COMPLETED'
          ? 'Survey marked as complete'
          : survey.status === 'PUBLISHED'
          ? 'Survey published successfully!'
          : 'Survey status updated successfully'
      )
    },
    onError: (error) => {
      toast.error(`Failed to update survey status: ${error.message}`)
    },
  })

  const handleStatusChange = async (newStatus) => {

    await updateSurveyStatus({
      variables: {
        id: survey.id,
        status: newStatus,
      },
    })
  }

  const handleShare = () => {
    navigate(routes.surveyDetail({ id: survey.id, tab: 'distribution' }))
  }

  const isDraft = survey.status === 'DRAFT'
  const isPublished = survey.status === 'PUBLISHED'

  return (
    <div className="dropdown dropdown-end z-50">
      <label 
        tabIndex={0} 
        className="btn btn-primary btn-outline btn-sm"
      >
        Actions
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 ml-2">
          <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
        </svg>
      </label>
      
      <ul tabIndex={0} className="dropdown-content menu p-1 shadow-lg bg-base-100 rounded-box w-72 z-[9999]">
        {/* Publish Survey - Only show for drafts */}
        {isDraft && (
          <li >
            <button 
              onClick={() => handleStatusChange('PUBLISHED')}
              className="text-base-content hover:text-primary py-2"
            >
              <RocketLaunchIcon className="h-4 w-4" />
              <div>
                <div className="font-medium">Publish Survey</div>
                <span className="text-xs opacity-70">Make survey available to respondents</span>
              </div>
            </button>
          </li>
        )}

        {/* Complete Survey - Only show for published surveys */}
        {isPublished && (
          <li>
            <button 
              onClick={() => handleStatusChange('COMPLETED')}
              className="text-base-content hover:text-primary py-2"
            >
              <CheckCircleIcon className="h-4 w-4" />
              <div>
                <div className="font-medium">Mark as Complete</div>
                <span className="text-xs opacity-70">Close survey to new responses</span>
              </div>
            </button>
          </li>
        )}

        {/* Share Survey - Only enabled for published surveys */}
        <li className={(!isPublished ) ? 'disabled' : ''}>
          <button 
            onClick={handleShare}
            className="text-base-content hover:text-primary py-2"
          >
            <ShareIcon className="h-4 w-4" />
            <div>
              <div className="font-medium">Share Survey</div>
              <span className="text-xs opacity-70">
                {!isPublished ? 'Publish survey to share' : 'Manage distribution settings'}
              </span>
            </div>
          </button>
        </li>

        {/* Edit Survey */}
        <li >
          <button 
            onClick={onEdit}
            className="text-base-content hover:text-primary py-2"
          >
            <PencilIcon className="h-4 w-4" />
            <div>
              <div className="font-medium">Edit Survey</div>
              <span className="text-xs opacity-70">Modify survey details</span>
            </div>
          </button>
        </li>

        {/* Delete Survey */}
        <li>
          <button 
            onClick={onDelete}
            className="text-error hover:text-error py-2"
          >
            <TrashIcon className="h-4 w-4" />
            <div>
              <div className="font-medium">Delete Survey</div>
              <span className="text-xs opacity-70">Permanently remove this survey</span>
            </div>
          </button>
        </li>
      </ul>
    </div>
  )
}

export default SurveyDetailActions
