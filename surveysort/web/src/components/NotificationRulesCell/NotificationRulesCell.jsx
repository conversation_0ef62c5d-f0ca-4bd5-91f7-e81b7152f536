import { useState } from 'react'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { 
  BellIcon, 
  EnvelopeIcon
} from '@heroicons/react/24/outline'
import { Form, Label, SelectField, Submit } from '@redwoodjs/forms'

export const QUERY = gql`
  query NotificationRulesQuery($surveyId: String!) {
    notificationRules: notificationRulesBySurveyId(surveyId: $surveyId) {
      id
      surveyId
      event
      channel
      frequency
      isActive
      webhookUrl
      createdAt
      updatedAt
    }
  }
`

const CREATE_NOTIFICATION_RULE_MUTATION = gql`
  mutation CreateNotificationRuleMutation($input: CreateNotificationRuleInput!) {
    createNotificationRule(input: $input) {
      id
      channel
      frequency
      isActive
      webhookUrl
    }
  }
`

const UPDATE_NOTIFICATION_RULE_MUTATION = gql`
  mutation UpdateNotificationRuleMutation($id: String!, $input: UpdateNotificationRuleInput!) {
    updateNotificationRule(id: $id, input: $input) {
      id
      isActive
      updatedAt
    }
  }
`

const DELETE_NOTIFICATION_RULE_MUTATION = gql`
  mutation DeleteNotificationRuleMutation($id: String!) {
    deleteNotificationRule(id: $id) {
      id
    }
  }
`

const TEST_NOTIFICATION_RULE_MUTATION = gql`
  mutation TestNotificationRuleMutation($id: String!) {
    testNotificationRule(id: $id)
  }
`

export const Loading = () => (
  <div className="animate-pulse p-4">
    <div className="h-8 w-1/3 bg-gray-200 rounded"></div>
    <div className="mt-4 space-y-3">
      <div className="h-4 bg-gray-200 rounded"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  </div>
)

export const Empty = ({ surveyId }) => {
  const [isCreating, setIsCreating] = useState(false)

  const [createNotificationRule] = useMutation(CREATE_NOTIFICATION_RULE_MUTATION, {
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
    onCompleted: () => {
      setIsCreating(false)
      toast.success('Notification set up successfully')
    }
  })

  const onSubmit = async (data) => {
    try {
      const event = data.frequency === 'IMMEDIATE' 
        ? 'SURVEY_RESPONSE_RECEIVED'
        : data.frequency === 'DAILY' 
          ? 'SURVEY_RESPONSE_DAILY_DIGEST'
          : 'SURVEY_RESPONSE_WEEKLY_DIGEST'

      await createNotificationRule({
        variables: { 
          input: {
            surveyId,
            event,
            channel: 'EMAIL',
            frequency: data.frequency,
            isActive: true
          }
        }
      })
    } catch (error) {
      toast.error(error.message)
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-none px-4 py-3 border-b border-base-300">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold text-base-content">
              Survey Notifications
            </h2>
            <p className="text-sm text-base-content/70">
              Get notified about new survey responses
            </p>
          </div>
          <button 
            onClick={() => setIsCreating(!isCreating)}
            className="btn btn-sm btn-primary"
          >
            <BellIcon className="h-4 w-4 mr-2" />
            Set Up Notifications
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        {isCreating ? (
          <div className="bg-base-200 rounded-lg p-4">
            <Form onSubmit={onSubmit} className="space-y-4">
              <div className="bg-base-100 rounded p-3 text-sm">
                <div className="flex items-start gap-3">
                  <EnvelopeIcon className="h-5 w-5 text-primary flex-shrink-0" />
                  <div>
                    <p className="font-medium">Add to contacts</p>
                    <p className="text-base-content/70 text-xs mt-1">
                      Add <EMAIL> to prevent emails from being marked as spam
                    </p>
                    <div className="mt-2 flex items-center gap-3">
                      <a 
                        href="https://contacts.google.com/new?email=<EMAIL>"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary hover:underline"
                      >
                        Add to Gmail
                      </a>
                      <a 
                        href="https://outlook.office.com/people/new"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary hover:underline"
                      >
                        Add to Outlook
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label name="frequency" className="text-sm font-medium">How Often?</Label>
                <SelectField 
                  name="frequency"
                  className="select select-sm select-bordered w-full mt-1"
                >
                  <option value="IMMEDIATE">For each new response</option>
                  <option value="DAILY">Daily summary</option>
                  <option value="WEEKLY">Weekly summary</option>
                </SelectField>
              </div>

              <div className="flex justify-end gap-2">
                <Submit className="btn btn-sm btn-primary">
                  Save
                </Submit>
              </div>
            </Form>
          </div>
        ) : (
          <div className="text-center py-8">
            <BellIcon className="mx-auto h-12 w-12 text-base-content/30" />
            <h3 className="mt-2 text-sm font-medium text-base-content">No notifications set up</h3>
            <p className="mt-1 text-sm text-base-content/70">
              Get notified when you receive new survey responses
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

const NotificationEvent = {
  IMMEDIATE: 'SURVEY_RESPONSE_RECEIVED',
  DAILY: 'SURVEY_RESPONSE_DAILY_DIGEST', 
  WEEKLY: 'SURVEY_RESPONSE_WEEKLY_DIGEST'
}

export const Success = ({ notificationRules, surveyId }) => {
  const [isCreating, setIsCreating] = useState(false)

  const [createNotificationRule] = useMutation(CREATE_NOTIFICATION_RULE_MUTATION, {
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
    onCompleted: () => {
      setIsCreating(false)
      toast.success('Notification set up successfully')
    }
  })

  const [updateNotificationRule] = useMutation(UPDATE_NOTIFICATION_RULE_MUTATION, {
    refetchQueries: [{ query: QUERY, variables: { surveyId } }],
    onCompleted: () => {
      toast.success('Notification rule updated')
    },
    onError: (error) => {
      toast.error(error.message)
    }
  })

  const [deleteNotificationRule] = useMutation(DELETE_NOTIFICATION_RULE_MUTATION, {
    refetchQueries: [{ query: QUERY, variables: { surveyId } }]
  })

  const [testNotificationRule] = useMutation(TEST_NOTIFICATION_RULE_MUTATION, {
    onCompleted: () => {
      toast.success('Test notification sent')
    }
  })

  const onSubmit = async (data) => {
    try {
      const input = {
        surveyId,
        event: NotificationEvent[data.frequency],
        channel: 'EMAIL',
        frequency: data.frequency,
        isActive: true
      }

      await createNotificationRule({
        variables: { input }
      })
    } catch (error) {
      console.error('Error creating notification rule:', error)
      toast.error(error.message)
    }
  }

  const handleStatusChange = async (rule) => {
    try {
      await updateNotificationRule({
        variables: {
          id: rule.id,
          input: {
            isActive: !rule.isActive,
            event: rule.event,
            channel: rule.channel,
            frequency: rule.frequency
          }
        }
      })
    } catch (error) {
      console.error('Error updating notification rule:', error)
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-none px-4 py-3 border-b border-base-300 bg-base-100/50 backdrop-blur-sm">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold text-base-content">
              Survey Notifications
            </h2>
            <p className="text-sm text-base-content/70">
              Get notified about new survey responses
            </p>
          </div>
          <button 
            onClick={() => setIsCreating(!isCreating)}
            className="btn btn-sm btn-primary"
          >
            <BellIcon className="h-4 w-4 mr-2" />
            {isCreating ? 'Cancel' : 'Add Notification'}
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isCreating && (
          <div className="bg-base-200/50 backdrop-blur-sm rounded-lg p-4 mb-4 border border-base-300">
            <Form onSubmit={onSubmit} className="space-y-4">
              <div className="bg-base-100/50 backdrop-blur-sm rounded p-3 text-sm border border-base-300">
                <div className="flex items-start gap-3">
                  <EnvelopeIcon className="h-5 w-5 text-primary flex-shrink-0" />
                  <div>
                    <p className="font-medium">Add to contacts</p>
                    <p className="text-base-content/70 text-xs mt-1">
                      Add <EMAIL> to prevent emails from being marked as spam
                    </p>
                    <div className="mt-2 flex items-center gap-3">
                      <a 
                        href="https://contacts.google.com/new?email=<EMAIL>"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary hover:underline"
                      >
                        Add to Gmail
                      </a>
                      <a 
                        href="https://outlook.office.com/people/new"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary hover:underline"
                      >
                        Add to Outlook
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label name="frequency" className="text-sm font-medium">How Often?</Label>
                <SelectField 
                  name="frequency"
                  className="select select-sm select-bordered w-full mt-1"
                >
                  <option value="IMMEDIATE">For each new response</option>
                  <option value="DAILY">Daily summary</option>
                  <option value="WEEKLY">Weekly summary</option>
                </SelectField>
              </div>

              <div className="flex justify-end gap-2">
                <Submit className="btn btn-sm btn-primary">
                  Save
                </Submit>
              </div>
            </Form>
          </div>
        )}

        {notificationRules.length > 0 ? (
          <div className="overflow-x-auto bg-base-100/50 backdrop-blur-sm rounded-lg border border-base-300">
            <table className="table table-sm w-full">
              <thead>
                <tr>
                  <th className="text-xs">Send To</th>
                  <th className="text-xs">Frequency</th>
                  <th className="text-xs">Status</th>
                  <th className="text-xs">Actions</th>
                </tr>
              </thead>
              <tbody>
                {notificationRules.map((rule) => (
                  <tr key={rule.id}>
                    <td>
                      <div className="flex items-center gap-2">
                        <EnvelopeIcon className="h-4 w-4 text-primary" />
                        <span className="text-sm">Email</span>
                      </div>
                    </td>
                    <td className="text-sm">
                      {rule.frequency === 'IMMEDIATE' ? 'Each Response' :
                       rule.frequency === 'DAILY' ? 'Daily Summary' :
                       'Weekly Summary'}
                    </td>
                    <td>
                      <input
                        type="checkbox"
                        className="toggle toggle-sm toggle-primary"
                        checked={rule.isActive}
                        onChange={() => handleStatusChange(rule)}
                      />
                    </td>
                    <td>
                      <div className="flex gap-2">
                        <button 
                          className="btn btn-xs"
                          onClick={() => testNotificationRule({ variables: { id: rule.id }})}
                        >
                          Test
                        </button>
                        <button 
                          className="btn btn-xs btn-error"
                          onClick={() => deleteNotificationRule({ variables: { id: rule.id }})}
                        >
                          Remove
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : !isCreating && (
          <div className="text-center py-8 bg-base-100/50 backdrop-blur-sm rounded-lg border border-base-300">
            <p className="text-base-content/70 text-sm">No notification rules set up yet</p>
          </div>
        )}
      </div>
    </div>
  )
} 