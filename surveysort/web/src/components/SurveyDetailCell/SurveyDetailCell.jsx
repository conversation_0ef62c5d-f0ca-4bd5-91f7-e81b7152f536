import { useState, useRef, useEffect, useCallback, createContext } from 'react'
import {
  ChevronRightIcon,
  BriefcaseIcon,
  CalendarIcon,
  PencilIcon,
  LinkIcon,
  ChevronDownIcon,
  QuestionMarkCircleIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  LightBulbIcon,
  CogIcon,
  EllipsisVerticalIcon,
  ShareIcon,
  ChartBarSquareIcon,
  InformationCircleIcon,
  SparklesIcon,
  BellIcon,
  ExclamationTriangleIcon,
  DocumentChartBarIcon,
} from '@heroicons/react/24/outline'

import { navigate, routes } from '@redwoodjs/router'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'

import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react'

import EditSurveyForm from 'src/components/EditSurveyForm/EditSurveyForm'
import DeleteSurveyForm from 'src/components/DeleteSurveyForm/DeleteSurveyForm'
import SurveyDetailActions from 'src/components/SurveyDetailActions/SurveyDetailActions'
import QuestionForm from 'src/components/QuestionForm/QuestionForm'
import SurveySettingsTabCell from 'src/components/SurveySettingsTabCell'
import SRInsightsConvoCell from 'src/components/SRInsightsConvoCell'
import SurveyDistributionTabCell from 'src/components/SurveyDistributionTabCell'
import QuestionAnalyticsCell from 'src/components/QuestionAnalyticsCell'
import UnifiedResponseAnalyticsCell from 'src/components/UnifiedResponseAnalyticsCell'
import NotificationRulesCell from 'src/components/NotificationRulesCell'
import SurveyReportsTabCell from 'src/components/SurveyReportsTabCell'
import { Popover } from '@headlessui/react'

export const QUERY = gql`
  query FindSurveyDetailQuery($id: String!) {
    surveyDetail: surveyDetail(id: $id) {
      id
      title
      description
      audience
      surveyObjective
      additionalContext
      status
      source
      isDeleted
      isSuspended
      createdBy
      createdAt
    }
  }
`

const UPDATE_SURVEY_MUTATION = gql`
  mutation UpdateSurveyMutation($id: String!, $input: UpdateSurveyInput!) {
    updateSurvey(id: $id, input: $input) {
      id
    }
  }
`

export const Loading = () => (
  <div className="px-8 py-6">
    <div className="skeleton mb-4 h-8 w-1/2"></div>
    <div className="skeleton mb-2 h-6 w-full"></div>
    <div className="skeleton mb-2 h-6 w-3/4"></div>
    <div className="skeleton h-10 w-1/4"></div>
  </div>
)

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }) => (
  <div style={{ color: 'red' }}>Error: {error?.message}</div>
)

export const SurveyContext = createContext(null)

export const Success = ({ surveyDetail, currentTab }) => {
  const [isShareDialogOpen, setShareDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const [updateSurvey] = useMutation(UPDATE_SURVEY_MUTATION, {
    onCompleted: () => {
      toast.success('Disclosure updated successfully')
    },
    onError: (error) => {
      toast.error(`Failed to update disclosure: ${error.message}`)
    },
  })

  const displaySurvey = surveyDetail;
  const isImportedSurvey = displaySurvey.source === 'IMPORTED';

  const handleTabClick = (tab) => {
    navigate(routes.surveyDetail({ id: surveyDetail.id, tab }))
  }

  const tabs = [
    {
      name: 'Questions',
      tab: 'questions',
      current: currentTab === 'questions',
      icon: QuestionMarkCircleIcon,
      shouldHideForImported: isImportedSurvey,
    },
    {
      name: 'Question Analytics',
      tab: 'question-analytics',
      current: currentTab === 'question-analytics',
      icon: ChartBarSquareIcon,
      shouldHideForImported: false,
    },
    {
      name: 'Response Analytics',
      tab: 'responses',
      current: currentTab === 'responses',
      icon: ChartBarIcon,
      shouldHideForImported: false,
    },
    {
      name: 'AI Insights',
      tab: 'insights',
      current: currentTab === 'insights',
      icon: SparklesIcon,
      shouldHideForImported: false,
    },
    {
      name: 'Reports',
      tab: 'reports',
      current: currentTab === 'reports',
      icon: DocumentChartBarIcon,
      shouldHideForImported: false,
    },
    {
      name: 'Distribution',
      tab: 'distribution',
      current: currentTab === 'distribution',
      icon: ShareIcon,
      shouldHideForImported: isImportedSurvey,
    },
    {
      name: 'Notifications',
      tab: 'notifications',
      current: currentTab === 'notifications',
      icon: BellIcon,
      shouldHideForImported: isImportedSurvey,
    },
  ]

  const classNames = (...classes) => classes.filter(Boolean).join(' ')

  const openShareDialog = () => setShareDialogOpen(true)
  const closeShareDialog = () => setShareDialogOpen(false)

  const openEditDialog = () => {
    return setIsEditDialogOpen(true)
  }

  const openDeleteDialog = () => {
    return setIsDeleteDialogOpen(true)
  }

  const closeEditDialog = () => setIsEditDialogOpen(false)

  const closeDeleteDialog = () => setIsDeleteDialogOpen(false)


  const getStatusBadge = (status) => {
    const statusColors = {
      DRAFT: 'bg-warning/10 text-warning ring-warning/20',
      PUBLISHED: 'bg-success/10 text-success ring-success/20',
      COMPLETED: 'bg-neutral/10 text-neutral-content ring-neutral/20',
    }

    return (
      <span
        className={`inline-flex items-center rounded-md px-1.5 py-0.5 text-[10px] font-medium ring-1 ring-inset ${statusColors[status]}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
      </span>
    )
  }

  const getSourceBadge = (source) => {
    const sourceColors = {
      CREATED: 'bg-info/10 text-info ring-info/20',
      IMPORTED: 'bg-warning/10 text-warning ring-warning/20',
      INTEGRATED: 'bg-success/10 text-success ring-success/20',
    }

    return (
      <span
        className={`inline-flex items-center rounded-md px-1.5 py-0.5 text-[10px] font-medium ring-1 ring-inset ${sourceColors[source]}`}
      >
        {source.charAt(0).toUpperCase() + source.slice(1).toLowerCase()}
      </span>
    )
  }

  const renderEditDialog = () => (
    <Dialog
      open={isEditDialogOpen}
      onClose={closeEditDialog}
      className="max-w-full lg:max-w-6xl"
    >
      <DialogTitle className="text-center text-2xl font-bold text-gray-900">
        Edit Survey
      </DialogTitle>
      <DialogPanel className="mt-4">
        <EditSurveyForm survey={surveyDetail} onClose={closeEditDialog} />
      </DialogPanel>
    </Dialog>
  )

  const renderDeleteDialog = () => (
    <Dialog 
      open={isDeleteDialogOpen} 
      onClose={closeDeleteDialog} 
      className="relative z-[99999]"
    >
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-base-300/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-[99999] w-screen overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-base-100 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-lg"
          >
            <div className="px-4 pb-4 pt-5 sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-error/10 sm:mx-0 sm:h-10 sm:w-10">
                  <ExclamationTriangleIcon className="h-6 w-6 text-error" aria-hidden="true" />
                </div>
                <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                  <DialogTitle as="h3" className="text-base font-semibold text-base-content">
                    Delete Survey
                  </DialogTitle>
                  <div className="mt-2">
                    <p className="text-sm text-base-content/70">
                      Are you sure you want to delete this survey? All responses and analytics data will be permanently removed.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="border-t border-base-200 px-4 py-3 sm:px-6">
              <DeleteSurveyForm 
                survey={surveyDetail} 
                onClose={closeDeleteDialog}
              />
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )

  const [tooltipText, setTooltipText] = useState('Copy survey link')
  const shareButtonRef = useRef(null)

  const handleShare = () => {
    const surveyLink = routes.endUserSurveySubmit({ id: surveyDetail.id })
    const fullUrl = `${window.location.origin}${surveyLink}`

    navigator.clipboard.writeText(fullUrl).then(() => {
      setTooltipText('Copied!')
      setTimeout(() => setTooltipText('Copy survey link'), 2000)
    })
  }

  const renderTabContent = () => {
    switch (currentTab) {
      case 'questions':
        return <QuestionForm surveyId={surveyDetail.id} />
      case 'question-analytics':
        return <QuestionAnalyticsCell surveyId={surveyDetail.id} />
      case 'responses':
        return <UnifiedResponseAnalyticsCell surveyId={surveyDetail.id} />
      case 'insights':
        return <SRInsightsConvoCell surveyId={surveyDetail.id} />
      case 'reports':
        const params = new URLSearchParams(window.location.search)
        const new_report = params.get('new_report')
        return <SurveyReportsTabCell surveyId={surveyDetail.id} new_report={new_report} />
      case 'distribution':
        return <SurveyDistributionTabCell surveyId={surveyDetail.id} />
      case 'notifications':
        return <NotificationRulesCell surveyId={surveyDetail.id} />
      default:
        return <QuestionForm surveyId={surveyDetail.id} />
    }
  }

  const [showDetails, setShowDetails] = useState(false)

  return (
    <SurveyContext.Provider value={surveyDetail}>
      <div className="flex flex-col h-full">
        <div className="flex-none relative z-40">
          <div className="sticky top-0 z-40 bg-base-100">
            <div className="px-3 py-1.5 lg:flex lg:items-center lg:justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl font-bold text-base-content truncate sm:text-2xl sm:tracking-tight">
                  {displaySurvey.title}
                </h2>
                <div className="mt-1 flex items-center gap-4 text-[11px] text-base-content/70">
                  <div className="flex items-center gap-2">
                    {getStatusBadge(displaySurvey.status)}
                    {getSourceBadge(displaySurvey.source)}
                  </div>
                  <div className="flex items-center">
                    <BriefcaseIcon className="mr-1 h-3.5 w-3.5 flex-shrink-0 text-base-content/50" />
                    {displaySurvey.createdBy}
                  </div>
                  <div className="flex items-center">
                    <CalendarIcon className="mr-1 h-3.5 w-3.5 flex-shrink-0 text-base-content/50" />
                    {new Date(displaySurvey.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </div>

              {displaySurvey.source !== 'IMPORTED' && (
                <div className="flex m-2">
                  <SurveyDetailActions
                    survey={displaySurvey}
                    onEdit={openEditDialog}
                    onDelete={openDeleteDialog}
                  />
                </div>
              )}
            </div>

            {showDetails && (
              <div className="mt-3 p-3 bg-base-200 rounded-lg text-sm">
                <dl className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                  <div>
                    <dt className="font-medium text-base-content">Description</dt>
                    <dd className="mt-1 text-base-content/70">{displaySurvey.description || 'No description'}</dd>
                  </div>
                  <div>
                    <dt className="font-medium text-base-content">Audience</dt>
                    <dd className="mt-1 text-base-content/70">{displaySurvey.audience || 'Not specified'}</dd>
                  </div>
                  <div>
                    <dt className="font-medium text-base-content">Survey Objective</dt>
                    <dd className="mt-1 text-base-content/70">{displaySurvey.surveyObjective || 'Not specified'}</dd>
                  </div>
                  <div>
                    <dt className="font-medium text-base-content">Additional Context</dt>
                    <dd className="mt-1 text-base-content/70">{displaySurvey.additionalContext || 'None'}</dd>
                  </div>
                </dl>
              </div>
            )}

            <div className="mt-2">
              <div className="border-b border-base-300 pb-5 sm:pb-0">
                <div className="sm:hidden">
                  <label htmlFor="current-tab" className="sr-only">
                    Select a tab
                  </label>
                  <select
                    id="current-tab"
                    name="current-tab"
                    defaultValue={tabs.find((tab) => tab.current)?.name}
                    className="select select-bordered w-full"
                    onChange={(e) =>
                      handleTabClick(
                        tabs.find((tab) => tab.name === e.target.value).tab
                      )
                    }
                  >
                    {tabs
                      .filter((tab) => !(tab.shouldHideForImported)) // Filter out tabs to hide
                      .map((tab) => (
                        <option key={tab.name}>{tab.name}</option>
                      ))}
                  </select>
                </div>
                <div className="hidden sm:block">
                  <nav className="-mb-px flex space-x-6 px-3">
                    {tabs
                      .filter((tab) => !(tab.shouldHideForImported)).map((tab) => (
                        <button
                          key={tab.name}
                          onClick={() => handleTabClick(tab.tab)}
                          aria-current={tab.current ? 'page' : undefined}
                          className={classNames(
                            tab.current
                              ? 'border-primary text-primary'
                              : 'border-transparent text-base-content hover:border-base-300 hover:text-base-content',
                            'whitespace-nowrap border-b-2 px-1 pb-2 text-xs font-medium flex items-center'
                          )}
                        >
                          <tab.icon className="mr-1.5 h-4 w-4" aria-hidden="true" />
                          {tab.name}
                        </button>
                      ))}
                  </nav>
                </div>
              </div>
            </div>
          </div>
          {renderEditDialog()}
          {renderDeleteDialog()}
        </div>

        <div className="flex-1 min-h-0 overflow-hidden z-30">
          {renderTabContent()}
        </div>
      </div>
    </SurveyContext.Provider>
  )
}
