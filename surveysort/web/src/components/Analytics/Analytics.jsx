import { useEffect } from 'react'
import { useAuth } from 'src/auth'
import posthog from 'posthog-js'

const POSTHOG_KEY = 'phc_2UUcHSdZYX1452aTm188fHgtjOyNxIE3kGiOPVxhZ9p'
const TAWK_TO_KEY = '6767080b49e2fd8dfefb993e/1ifl7er3t'

const Analytics = ({ 
  enablePostHog = true, 
  enableTawkTo = true,
}) => {
  const { currentUser } = useAuth()

  // PostHog initialization and user identification
  useEffect(() => {
    if (!enablePostHog) return
    
    posthog.init(POSTHOG_KEY, {
      api_host: 'https://us.i.posthog.com',
      person_profiles: 'always',
    })

    if (currentUser) {
      posthog.identify(currentUser.id, {
        email: currentUser.email,
        accountId: currentUser?.accountId,
        isVerified: currentUser?.isVerified,
        roles: currentUser?.roles,
        stripePaidStatus: currentUser?.stripePaidStatus,
        onboardingComplete: currentUser?.props?.onboardingDone,
        isAdmin: currentUser?.roles?.includes('ADMIN'),
      })
    }

  }, [currentUser, enablePostHog])

  // Tawk.to initialization
  useEffect(() => {
    if (!enableTawkTo) return

    var Tawk_API = window.Tawk_API || {}
    var Tawk_LoadStart = new Date()

    if (currentUser) {
      Tawk_API.visitor = {
        name: currentUser.email,
        email: currentUser.email,
        accountId: currentUser?.accountId,
      }
    }

    const s1 = document.createElement('script')
    const s0 = document.getElementsByTagName('script')[0]
    s1.async = true
    s1.src = `https://embed.tawk.to/${TAWK_TO_KEY}`
    s1.charset = 'UTF-8'
    s1.setAttribute('crossorigin', '*')
    s0.parentNode.insertBefore(s1, s0)

    return () => {
      if (s1.parentNode) {
        s1.parentNode.removeChild(s1)
      }
    }
  }, [currentUser, enableTawkTo])

  return null
}

// Export helper functions to use throughout the app
export const trackEvent = (eventName, properties = {}) => {
  if (posthog) {
    const isDev = process.env.NODE_ENV !== 'production'
    posthog.capture(eventName, {
      ...properties,
      location: window.location.pathname,
      isDev,
      isProd: !isDev,
      environment: process.env.NODE_ENV,
    })
  }
}

// Common tracking functions
export const trackUpgradeClick = (location) => {
  trackEvent('clicked_upgrade', { location })
}

export const trackSignUpRedirect = (from) => {
  trackEvent('redirect_to_signup', { from })
}

export const trackOnboardingComplete = (userId, accountId) => {
  trackEvent('completed_onboarding', { userId, accountId })
}

export const trackDemoModeToggle = (newState) => {
  trackEvent('toggled_demo_mode', { newState })
}

export const trackLogout = (location) => {
  trackEvent('user_logged_out', { location })
}

export default Analytics 