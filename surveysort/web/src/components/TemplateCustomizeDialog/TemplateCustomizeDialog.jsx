import { useState } from 'react'
import { Form, Label, TextField, Submit, FieldError, TextAreaField } from '@redwoodjs/forms'
import { useMutation } from '@redwoodjs/web'
import { toast } from '@redwoodjs/web/toast'
import { Dialog } from '@headlessui/react'
import { navigate, routes } from '@redwoodjs/router'
import { SparklesIcon, InformationCircleIcon, BeakerIcon } from '@heroicons/react/24/outline'

const CREATE_SURVEY_WITH_TEMPLATE = gql`
  mutation CreateSurveyWithTemplate($input: CreateSurveyWithTemplateInput!) {
    createSurveyWithTemplate(input: $input) {
      id
    }
  }
`

const TemplateCustomizeDialog = ({ 
  isOpen, 
  onClose, 
  template,
}) => {
  const [isCreating, setIsCreating] = useState(false)

  const [createSurveyWithTemplate] = useMutation(CREATE_SURVEY_WITH_TEMPLATE, {
    onCompleted: (data) => {
      toast.success('Survey created successfully')
      navigate(routes.surveyDetail({ id: data.createSurveyWithTemplate.id }))
      onClose()
    },
    onError: (error) => {
      setIsCreating(false)
      toast.error(error.message)
    },
  })

  const onSubmit = async (formData) => {
    setIsCreating(true)
    await createSurveyWithTemplate({
      variables: {
        input: {
          templateId: template.id,
          productName: formData.productName,
          audience: formData.audience,
          additionalContext: formData.additionalContext,
        },
      },
    })
  }

  return (
    <Dialog 
      open={isOpen} 
      onClose={() => !isCreating && onClose()}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-lg rounded-lg bg-base-100 p-6">
          <div className="mb-6 space-y-3">
            <div className="flex items-center gap-2">
              <SparklesIcon className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-medium">Customize Template</h3>
            </div>
            
            <div className="flex items-start gap-3 p-3 bg-base-200 rounded-lg">
              <BeakerIcon className="h-5 w-5 text-primary mt-1" />
              <div className="space-y-1">
                <p className="text-sm font-medium">SurveySort Question Design Agent</p>
                <p className="text-sm text-base-content/70">
                  Our AI will adapt {template?.name} questions for your specific needs, 
                  ensuring they're relevant and engaging for your audience while maintaining 
                  the template's proven structure.
                </p>
              </div>
            </div>
          </div>
          
          <Form onSubmit={onSubmit} className="space-y-4">
            {/* Form fields remain the same */}
            <div>
              <div className="flex items-center gap-2">
                <Label name="productName">Product Name</Label>
                <div className="tooltip tooltip-right" data-tip="The name of your product or service - we'll customize questions specifically for it">
                  <InformationCircleIcon className="h-4 w-4 text-base-content/70" />
                </div>
              </div>
              <TextField
                name="productName"
                validation={{ required: true }}
                className="input input-bordered w-full"
                placeholder="e.g., SurveySort"
                disabled={isCreating}
              />
              <FieldError name="productName" className="mt-1 text-error text-sm" />
            </div>

            <div>
              <div className="flex items-center gap-2">
                <Label name="audience">Target Audience (Optional)</Label>
                <div className="tooltip tooltip-right" data-tip="Define who should take this survey - helps us tailor the language and context">
                  <InformationCircleIcon className="h-4 w-4 text-base-content/70" />
                </div>
              </div>
              <TextField
                name="audience"
                className="input input-bordered w-full"
                placeholder="e.g., Current customers who have used our product for at least 3 months"
                disabled={isCreating}
              />
            </div>

            <div>
              <div className="flex items-center gap-2">
                <Label name="additionalContext">Additional Context (Optional)</Label>
                <div className="tooltip tooltip-right" data-tip="Any specific areas you want to focus on or recent changes to consider">
                  <InformationCircleIcon className="h-4 w-4 text-base-content/70" />
                </div>
              </div>
              <TextAreaField
                name="additionalContext"
                className="textarea textarea-bordered w-full"
                placeholder="e.g., Recent feature launches, specific feedback areas, or current challenges"
                disabled={isCreating}
              />
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <button 
                type="button"
                onClick={onClose}
                className="btn btn-ghost"
                disabled={isCreating}
              >
                Cancel
              </button>
              <Submit 
                className="btn btn-primary"
                disabled={isCreating}
              >
                {isCreating ? (
                  <>
                    <span className="loading loading-spinner"></span>
                    Creating Survey...
                  </>
                ) : (
                  'Create Survey'
                )}
              </Submit>
            </div>
          </Form>
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}

export default TemplateCustomizeDialog 