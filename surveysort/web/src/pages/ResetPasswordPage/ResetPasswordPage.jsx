import { useEffect, useRef, useState } from 'react'
import {
  Form,
  Label,
  PasswordField,
  Submit,
  FieldError,
} from '@redwoodjs/forms'
import { Link, navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { toast, Toaster } from '@redwoodjs/web/toast'
import { useAuth } from 'src/auth'

const ResetPasswordPage = ({ resetToken }) => {
  const { isAuthenticated, reauthenticate, validateResetToken, resetPassword } =
    useAuth()
  const [enabled, setEnabled] = useState(true)

  useEffect(() => {
    if (isAuthenticated) {
      navigate(routes.dashboard())
    }
  }, [isAuthenticated])

  useEffect(() => {
    const validateToken = async () => {
      if (!resetToken) {
        setEnabled(false)
        toast.error('Reset token is missing')
        return
      }
      console.log('resetToken', resetToken)
      const response = await validateResetToken(resetToken)
      if (response.error) {
        setEnabled(false)
        toast.error('This reset link is invalid or has expired. Please request a new one.')
      } else {
        setEnabled(true)
      }
    }
    validateToken()
  }, [resetToken, validateResetToken])

  const passwordRef = useRef(null)
  useEffect(() => {
    passwordRef.current?.focus()
  }, [])

  const onSubmit = async (data) => {
    try {
      const response = await resetPassword({
        password: data.password,
        resetToken
      })

      if (response?.error) {
        if (response.error.includes('Internal Server Error')) {
          toast.error('Something went wrong. Please try again later.')
        } else {
          toast.error(response.error)
        }
      } else {
        toast.success('Password changed successfully!')
        await reauthenticate()
        setTimeout(() => navigate(routes.login()), 3000)
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again later.')
    }
  }

  return (
    <>
      <Metadata title="Reset Password" />

      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <img
            className="mx-auto h-10 w-auto"
            src="/logo.webp"
            alt="SurveySort"
          />
          <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
            Set new password
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <Toaster toastOptions={{ className: 'rw-toast', duration: 6000 }} />

              <Form onSubmit={onSubmit} className="space-y-6">
                <div>
                  <Label
                    name="password"
                    className="label-text"
                    errorClassName="label-text text-error"
                  >
                    New Password
                  </Label>
                  <PasswordField
                    name="password"
                    autoComplete="new-password"
                    className="input input-bordered w-full"
                    errorClassName="input input-bordered input-error w-full"
                    disabled={!enabled}
                    ref={passwordRef}
                    validation={{
                      required: {
                        value: true,
                        message: 'New Password is required',
                      },
                    }}
                  />
                  <FieldError name="password" className="text-error text-sm" />
                </div>

                <Submit
                  className="btn btn-primary w-full"
                  disabled={!enabled}
                >
                  Reset password
                </Submit>
              </Form>
            </div>
          </div>

          <div className="mt-10 text-center text-sm text-base-content/70 space-y-2">
            <p>
              Remember your password?{' '}
              <Link to={routes.login()} className="link link-primary font-semibold">
                Sign in
              </Link>
            </p>
            <p>
              Don't have an account?{' '}
              <Link to={routes.signup()} className="link link-primary font-semibold">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  )
}

export default ResetPasswordPage
