import { useEffect, useState } from 'react'
import { navigate, routes, useParams } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { toast, Toaster } from '@redwoodjs/web/toast'
import { useAuth } from 'src/auth'
import { useMutation } from '@redwoodjs/web'
import { gql } from 'graphql-tag'

const VERIFY_EMAIL = gql`
  mutation VerifyEmailMutation($token: String!) {
    verifyEmail(token: $token) {
      message
      error
    }
  }
`

const VerifyEmailPage = () => {
  const [verificationStatus, setVerificationStatus] = useState('verifying')
  const { token } = useParams()
  const { client, login, verify } = useAuth()
  const [verifyEmail] = useMutation(VERIFY_EMAIL)

  useEffect(() => {
    const verify = async () => {
      try {
        const { data } = await verifyEmail({ 
          variables: { token } 
        })
        
        if (data?.verifyEmail?.error) {
          // Check for server errors (5xx)
          if (data.verifyEmail.error.includes('Internal Server Error') || 
              data.verifyEmail.error.includes('500')) {
            setVerificationStatus('error')
            toast.error('Something went wrong. Please try again later.')
            return
          }
          setVerificationStatus('error')
          toast.error(data.verifyEmail.error)
          return
        }

        setVerificationStatus('success')
        toast.success(data.verifyEmail.message)
        setTimeout(() => navigate(routes.login()), 3000)
      } catch (error) {
        setVerificationStatus('error')
        toast.error('Something went wrong. Please try again later.')
      }
    }

    if (token) verify()
  }, [token, verifyEmail])

  return (
    <>
      <MetaTags title="Verify Email" description="Email verification page" />
      <Toaster toastOptions={{ className: 'rw-toast', duration: 6000 }} />

      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <img
            className="mx-auto h-10 w-auto"
            src="/logo.webp"
            alt="SurveySort"
          />
          <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
            Email Verification
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              {verificationStatus === 'verifying' && (
                <div className="flex flex-col items-center gap-4">
                  <div className="loading loading-spinner loading-lg"></div>
                  <p>Verifying your email address...</p>
                </div>
              )}

              {verificationStatus === 'success' && (
                <div className="flex flex-col items-center gap-4">
                  <div className="text-success">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <p className="text-lg">Your email has been verified successfully!</p>
                  <p className="text-sm text-base-content/70">
                    Redirecting you to login page...
                  </p>
                </div>
              )}

              {verificationStatus === 'error' && (
                <div className="flex flex-col items-center gap-4">
                  <div className="text-error">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                  <p className="text-lg">Verification failed</p>
                  <p className="text-sm text-base-content/70">
                    The verification link may be expired or invalid.
                  </p>
                  <button
                    className="btn btn-primary"
                    onClick={() => navigate(routes.login())}
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default VerifyEmailPage
