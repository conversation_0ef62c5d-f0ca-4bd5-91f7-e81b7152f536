// import { Link, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'

const TemplatePage = () => {
  return (
    <>
      <Metadata title="Template" description="Template page" />

      <h1>TemplatePage</h1>
      <p>
        Find me in <code>./web/src/pages/TemplatePage/TemplatePage.jsx</code>
      </p>
      {/*
           My default route is named `template`, link to me with:
           `<Link to={routes.template()}>Template</Link>`
        */}
    </>
  )
}

export default TemplatePage
