import { useRef, useEffect } from 'react'
import {
  Form,
  Label,
  TextField,
  PasswordField,
  Submit,
  FieldError,
} from '@redwoodjs/forms'
import { Link, navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { toast, Toaster } from '@redwoodjs/web/toast'
import { useAuth } from 'src/auth'
import { isEmailAllowed } from 'src/utils/emailUtils'

const SignupPage = () => {
  const { isAuthenticated, signUp } = useAuth()

  useEffect(() => {
    if (isAuthenticated) {
      navigate(routes.dashboard())
    }
  }, [isAuthenticated])

  const usernameRef = useRef()
  useEffect(() => {
    usernameRef.current?.focus()
  }, [])

  const onSubmit = async (data) => {
    if (!data.username.includes('@')) {
      toast.error('Please enter a valid email address')
      return
    }

    if (!isEmailAllowed(data.username)) {
      toast.error('Only business email addresses are allowed. Personal email domains (like Gmail, Yahoo, etc.) are not accepted.')
      return
    }

    try {
      const response = await signUp({
        username: data.username,
        password: data.password,
      })

      if (response?.error) {
        if (response.error.includes('Internal Server Error') || response.error.includes('500')) {
          toast.error('Something went wrong. Please try again later.')
          return
        }
        toast.error(response.error)
      } else {
        toast.success('Please check your email for a verification link')
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again later.')
    }
  }

  return (
    <>
      <Metadata title="Sign Up" />

      <div className="relative min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        {/* Gradient Background */}
        <div className="fixed inset-0 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100" />
        
        <div className="relative sm:mx-auto sm:w-full sm:max-w-md">
          <img
            className="mx-auto h-10 w-auto"
            src="/logo.webp"
            alt="SurveySort"
          />
          <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
            Create your account
          </h2>
        </div>

        <div className="relative mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-white/70 backdrop-blur-xl shadow-xl border border-white/20">
            <div className="card-body">
              <Toaster toastOptions={{ className: 'rw-toast', duration: 6000 }} />

              <Form onSubmit={onSubmit} className="space-y-6">
                <div>
                  <Label
                    name="username"
                    className="label-text"
                    errorClassName="label-text text-error"
                  >
                    Email address
                  </Label>
                  <TextField
                    name="username"
                    className="input input-bordered w-full"
                    errorClassName="input input-bordered input-error w-full"
                    ref={usernameRef}
                    validation={{
                      required: {
                        value: true,
                        message: 'Email is required',
                      },
                      pattern: {
                        value: /^[^@]+@[^.]+\..+$/,
                        message: 'Please enter a valid email address',
                      },
                    }}
                  />
                  <FieldError name="username" className="text-error text-sm" />
                </div>

                <div>
                  <Label
                    name="password"
                    className="label-text"
                    errorClassName="label-text text-error"
                  >
                    Password
                  </Label>
                  <PasswordField
                    name="password"
                    className="input input-bordered w-full"
                    errorClassName="input input-bordered input-error w-full"
                    autoComplete="current-password"
                    validation={{
                      required: {
                        value: true,
                        message: 'Password is required',
                      },
                    }}
                  />
                  <FieldError name="password" className="text-error text-sm" />
                </div>

                <Submit className="btn btn-primary w-full">Sign up</Submit>

                <p className="text-center text-xs text-base-content/50">
                  By signing up, you agree to our{' '}
                  <Link to={routes.terms()} className="link link-hover">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link to={routes.privacy()} className="link link-hover">
                    Privacy Policy
                  </Link>
                </p>
              </Form>
            </div>
          </div>

          <p className="mt-10 text-center text-sm text-base-content/70">
            Already have an account?{' '}
            <Link to={routes.login()} className="link link-primary font-semibold">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </>
  )
}

export default SignupPage
