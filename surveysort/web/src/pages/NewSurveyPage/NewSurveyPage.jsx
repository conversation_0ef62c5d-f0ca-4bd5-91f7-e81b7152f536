import { navigate, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import NewSurveyForm from 'src/components/NewSurveyForm'
import AppShellLayout from 'src/layouts/AppShellLayout'

const NewSurveyPage = () => {
  return (
    <AppShellLayout>
      <MetaTags title="Create New Survey" description="Create a new survey" />
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="ml-64 w-[calc(100%-16rem)]"
      >
        <div className="mx-auto max-w-4xl p-6">
          <div className="flex items-center mb-6">
            <button
              onClick={() => navigate(routes.dashboard())}
              className="btn btn-ghost mr-4"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-semibold">Create New Survey</h1>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <NewSurveyForm 
              onClose={() => navigate(routes.dashboard())} 
            />
          </motion.div>
        </div>
      </motion.div>
    </AppShellLayout>
  )
}

export default NewSurveyPage 