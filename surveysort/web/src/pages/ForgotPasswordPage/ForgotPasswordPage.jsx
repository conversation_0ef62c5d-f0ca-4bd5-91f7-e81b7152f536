import { useEffect, useRef } from 'react'

import { Form, Label, TextField, Submit, FieldError } from '@redwoodjs/forms'
import { navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { Link } from '@redwoodjs/router'
import { toast, Toaster } from '@redwoodjs/web/toast'

import { useAuth } from 'src/auth'
import { isEmailAllowed } from 'src/utils/emailUtils'

const ForgotPasswordPage = () => {
  const { isAuthenticated, forgotPassword } = useAuth()

  useEffect(() => {
    if (isAuthenticated) {
      navigate(routes.home())
    }
  }, [isAuthenticated])

  const usernameRef = useRef(null)
  useEffect(() => {
    usernameRef?.current?.focus()
  }, [])

  const onSubmit = async (data) => {
    if (!isEmailAllowed(data.username)) {
      toast.error('Only business email addresses are allowed. Personal email domains (like Gmail, Yahoo, etc.) are not accepted.')
      return
    }

    try {
      const response = await forgotPassword(data.username)
      
      // Clear the form
      usernameRef.current.value = ''
      
      // dbAuth will return { error } for server errors
      if (response?.error) {
        if (response.error.includes('Internal Server Error')) {
          toast.error('Something went wrong. Please try again later.')
        } else {
          // For all other errors, still show generic message
          toast.success('If an account matches that email address, we will send reset instructions.')
        }
      } else {
        toast.success('If an account matches that email address, we will send reset instructions.')
      }
    } catch (error) {
      // Network/connection errors
      toast.error('Something went wrong. Please try again later.')
    }
  }

  return (
    <>
      <Metadata title="Forgot Password" />

      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <img
            className="mx-auto h-10 w-auto"
            src="/logo.webp"
            alt="SurveySort"
          />
          <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
            Reset your password
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <Toaster toastOptions={{ className: 'rw-toast', duration: 6000 }} />

              <Form onSubmit={onSubmit} className="space-y-6">
                <div>
                  <Label
                    name="username"
                    className="label-text"
                    errorClassName="label-text text-error"
                  >
                    Email address
                  </Label>
                  <TextField
                    name="username"
                    className="input input-bordered w-full"
                    errorClassName="input input-bordered input-error w-full"
                    ref={usernameRef}
                    validation={{
                      required: {
                        value: true,
                        message: 'Email is required',
                      },
                      pattern: {
                        value: /^[^@]+@[^.]+\..+$/,
                        message: 'Please enter a valid email address',
                      },
                    }}
                  />
                  <FieldError name="username" className="text-error text-sm" />
                </div>

                <Submit className="btn btn-primary w-full">
                  Send reset instructions
                </Submit>
              </Form>
            </div>
          </div>

          <div className="mt-10 text-center text-sm text-base-content/70 space-y-2">
            <p>
              Remember your password?{' '}
              <Link to={routes.login()} className="link link-primary font-semibold">
                Sign in
              </Link>
            </p>
            <p>
              Don't have an account?{' '}
              <Link to={routes.signup()} className="link link-primary font-semibold">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  )
}

export default ForgotPasswordPage
