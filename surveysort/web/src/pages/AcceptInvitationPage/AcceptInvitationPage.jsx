import { useEffect, useState } from 'react'
import { useLocation, navigate, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Form, TextField, PasswordField, Submit } from '@redwoodjs/forms'
import { useMutation, useQuery } from '@redwoodjs/web'
import { toast, Toaster } from '@redwoodjs/web/toast'
import { useAuth } from 'src/auth'

const QUERY = gql`
  query GetInvitation($token: String!) {
    invitation(token: $token) {
      id
      recipientEmail
      expiresAt
      acceptedAt
    }
  }
`

const ACCEPT_INVITATION = gql`
  mutation AcceptInvitation($input: AcceptInvitationInput!) {
    acceptInvitation(input: $input)
  }
`

const AcceptInvitationPage = () => {
  const { isAuthenticated } = useAuth()
  const { search } = useLocation()
  const token = new URLSearchParams(search).get('token')
  const [error, setError] = useState(null)

  const { data, loading } = useQuery(QUERY, {
    variables: { token },
    onError: (error) => setError(error.message)
  })

  const [acceptInvitation] = useMutation(ACCEPT_INVITATION, {
    onCompleted: (data) => {
      if (data.acceptInvitation === true) {
        toast.success('Account created and joined team successfully!')
        navigate(routes.dashboard())
      } else {
        setError('Failed to accept invitation')
      }
    },
    onError: (error) => {
      if (error.message.includes('already has an account')) {
        toast.error('Please log in with your existing account')
        navigate(routes.login())
      } else {
        setError(error.message)
      }
    },
  })

  useEffect(() => {
    if (isAuthenticated) {
      setError('Please log out first to accept this invitation')
    }
  }, [isAuthenticated])

  return (
    <>
      <MetaTags title="Accept Invitation" description="Team invitation acceptance page" />
      <Toaster toastOptions={{ className: 'rw-toast', duration: 6000 }} />

      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <img
            className="mx-auto h-10 w-auto"
            src="/logo.webp"
            alt="SurveySort"
          />
          <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
            Accept Team Invitation
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              {loading && (
                <div className="flex flex-col items-center gap-4">
                  <div className="loading loading-spinner loading-lg"></div>
                  <p>Verifying invitation...</p>
                </div>
              )}

              {error && (
                <div className="flex flex-col items-center gap-4">
                  <div className="text-error">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                  <p className="text-lg text-center">{error}</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => navigate(routes.login())}
                  >
                    Go to Login
                  </button>
                </div>
              )}

              {!loading && !error && data?.invitation && (
                <Form onSubmit={(data) => {
                  acceptInvitation({
                    variables: {
                      input: {
                        token,
                        password: data.password,
                      },
                    },
                  })
                }}>
                  <div className="space-y-4">
                    <TextField
                      name="email"
                      placeholder="Email"
                      defaultValue={data.invitation.recipientEmail}
                      disabled
                      className="input input-bordered w-full"
                    />
                    
                    <PasswordField
                      name="password"
                      placeholder="Choose a password"
                      validation={{
                        required: true,
                        minLength: 8,
                      }}
                      className="input input-bordered w-full"
                    />

                    <Submit className="btn btn-primary w-full">
                      Create Account & Join Team
                    </Submit>
                  </div>
                </Form>
              )}

              {!loading && !error && !data?.invitation && (
                <div className="flex flex-col items-center gap-4">
                  <div className="text-error">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                  <p className="text-lg">Invalid or expired invitation</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => navigate(routes.login())}
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default AcceptInvitationPage 