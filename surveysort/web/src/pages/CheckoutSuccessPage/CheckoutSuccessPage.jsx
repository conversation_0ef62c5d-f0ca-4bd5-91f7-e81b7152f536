import { useEffect, useState } from 'react'

import { navigate, routes } from '@redwoodjs/router'
import { useQuery, gql } from '@redwoodjs/web'

import { useAuth } from 'src/auth'

const ACCOUNT_QUERY = gql`
  query GetAccount {
    currentAccount {
      id
      checkoutInProgress
    }
  }
`

const CheckoutSuccessPage = () => {
  const { currentUser } = useAuth()
  const { data, refetch } = useQuery(ACCOUNT_QUERY, {
    skip: !currentUser,
  })
  const [polling, setPolling] = useState(true)

  useEffect(() => {
    if (!currentUser) {
      return
    }

    const interval = setInterval(() => {
      refetch()
    }, 1000)

    return () => clearInterval(interval)
  }, [currentUser, refetch])

  useEffect(() => {
    if (data && !data.currentAccount.checkoutInProgress) {
      setPolling(false)
      navigate(routes.dashboard())
    }
  }, [data])

  if (!currentUser) {
    return null
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      {polling && (
        <>
          <h1 className="mb-4 text-lg font-semibold text-gray-700">
            Processing your payment...
          </h1>
          <progress className="progress w-56"></progress>
        </>
      )}
      {!polling && (
        <h1 className="mb-4 text-lg font-semibold text-gray-700">
          Your payment has been processed successfully!
        </h1>
      )}
    </div>
  )
}

export default CheckoutSuccessPage
