import { useState, useEffect } from 'react'
import { Metadata } from '@redwoodjs/web'
import { Dialog, DialogBody } from 'src/components/catalyst/dialog'

import NewSurveyForm from 'src/components/NewSurveyForm'
import SurveyListCell from 'src/components/SurveyListCell'
import { useLocation, routes, navigate, useParams } from '@redwoodjs/router'
import AppShellLayout from 'src/layouts/AppShellLayout/AppShellLayout'
import { 
  DocumentIcon,
  UsersIcon,
  ListBulletIcon,
  QuestionMarkCircleIcon,
  BeakerIcon,
  ArrowLeftIcon 
} from '@heroicons/react/24/outline'
import TrialBanner from 'src/components/TrialBanner'
import MembersList from 'src/components/MembersList/MembersList'
import TemplatesView from 'src/components/TemplatesView/TemplatesView'
import CreateSurveyOption from 'src/components/NewSurveyForm/CreateSurveyOption'
import TemplateCreateSurveyOption from 'src/components/NewSurveyForm/TemplateCreateSurveyOption'
import SurveyCreationMethodSelector from 'src/components/SurveyCreationMethodSelector'

const DashboardContent = ({ view }) => {
  const [selectedOption, setSelectedOption] = useState(null)

  // Reset selection whenever view changes to new-survey
  useEffect(() => {
    if (view === 'new-survey') {
      setSelectedOption(null)
    }
  }, [view])

  const handleCreateClick = (e) => {
    e.preventDefault()
    setSelectedOption(null)
  }

  switch (view) {
    case 'templates':
      return (
        <div className="p-4">
          <TemplatesView />
        </div>
      )
    case 'members':
      return (
        <div className="p-4">
          <MembersList />
        </div>
      )
    case 'new-survey':
      return (
        <div className="px-4 pt-4">
          <div className="flex flex-col space-y-6">
            <div className="text-sm breadcrumbs">
              <ul>
                <li>
                  <a 
                    href="#"
                    onClick={handleCreateClick}
                    className="text-primary hover:text-primary-focus"
                  >
                    Create
                  </a>
                </li>
                <li>
                  {selectedOption === 'scratch' ? 'From Scratch' : 
                   selectedOption === 'template' ? 'From Template' : 
                   'Choose Method'}
                </li>
              </ul>
            </div>

            {!selectedOption ? (
              <div>
                <h1 className="text-lg font-semibold tracking-tight">Create Survey</h1>
                <p className="mt-1 text-sm text-base-content/70">
                  Choose how you want to create your survey
                </p>
                <div className="mt-4">
                  <SurveyCreationMethodSelector onMethodSelect={setSelectedOption} />
                </div>
              </div>
            ) : (
              selectedOption === 'scratch' ? (
                <CreateSurveyOption onClose={() => setSelectedOption(null)} />
              ) : (
                <TemplateCreateSurveyOption 
                  onClose={() => setSelectedOption(null)}
                />
              )
            )}
          </div>
        </div>
      )
    default:
      return (
        <div className="p-4">
          <SurveyListCell 
            onCreateSurvey={() => navigate(routes.dashboardView({ view: 'new-survey' }))} 
          />
        </div>
      )
  }
}

const DashboardPage = () => {
  const { view } = useParams()
  const location = useLocation()

  const isCurrentPath = (path) => {
    if (path === 'dashboard' && !view) return true
    return view === path
  }

  const sidebarContent = (
    <div className="fixed top-12 left-0 h-[calc(100vh-3rem)] w-64 z-40">
      {/* Frosted glass effect */}
      <div className="absolute inset-0 bg-white/40 backdrop-blur-xl border-r border-white/20" />
      
      <div className="relative flex flex-col h-full">
        {/* Main Navigation */}
        <div className="flex-1 p-4">
          <nav className="space-y-2">
            <button 
              onClick={() => navigate(routes.dashboard())}
              className={`
                flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg
                transition-colors duration-150 ease-in-out
                ${isCurrentPath('dashboard')
                  ? 'bg-primary/10 text-primary shadow-sm' 
                  : 'text-base-content hover:bg-base-300'
                }
              `}
            >
              <DocumentIcon className={`h-5 w-5 mr-3 ${
                isCurrentPath('dashboard') ? 'text-primary' : 'text-base-content'
              }`} />
              Surveys
            </button>
            <button 
              onClick={() => navigate(routes.dashboardView({ view: 'templates' }))}
              className={`
                flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg
                transition-colors duration-150 ease-in-out
                ${isCurrentPath('templates')
                  ? 'bg-primary/10 text-primary shadow-sm' 
                  : 'text-base-content hover:bg-base-300'
                }
              `}
            >
              <ListBulletIcon className={`h-5 w-5 mr-3 ${
                isCurrentPath('templates') ? 'text-primary' : 'text-base-content'
              }`} />
              Templates
            </button>
            <button 
              onClick={() => navigate(routes.dashboardView({ view: 'members' }))}
              className={`
                flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg
                transition-colors duration-150 ease-in-out
                ${isCurrentPath('members')
                  ? 'bg-primary/10 text-primary shadow-sm' 
                  : 'text-base-content hover:bg-base-300'
                }
              `}
            >
              <UsersIcon className={`h-5 w-5 mr-3 ${
                isCurrentPath('members') ? 'text-primary' : 'text-base-content'
              }`} />
              Members
            </button>
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="p-4 border-t border-white/20">
        
          {/* <a 
            href="https://docs.surveysort.com"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-base-content hover:bg-base-300 mt-2"
          >
            <QuestionMarkCircleIcon className="h-5 w-5 mr-3" />
            Documentation
          </a> */}
        </div>
      </div>
    </div>
  )

  return (
    <AppShellLayout>
      <TrialBanner />
      <div className="flex">
        {sidebarContent}
        <div className="ml-64 w-full">
          <DashboardContent view={view} />
        </div>
      </div>
    </AppShellLayout>
  )
}

export default DashboardPage
