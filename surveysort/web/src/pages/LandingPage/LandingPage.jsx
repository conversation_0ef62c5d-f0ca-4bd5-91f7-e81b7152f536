import { Link, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'

import Faqs from './Faqs'
import Footer from './Footer'
import Header from './Header'
import PrimaryFeatures from './PrimaryFeatures'
import SecondaryFeatures from './SecondaryFeatures'
import Testimonials from './Testimonials'

// Sample data (you can move this to a separate file later)
const data = {
  header: {
    title: 'SurveySort AI-powered surveys',
    subtitle:
      'Create high-quality surveys effortlessly with AI recommendations tailored to your audience. Our advanced algorithms analyze past successful surveys and current trends.',
    cta: 'Get Started',
  },
  primaryFeatures: [
    {
      title: 'AI-Suggested Survey Design',
      description:
        'Create high-quality surveys effortlessly with AI recommendations tailored to your audience. Our advanced algorithms analyze past successful surveys and current trends to provide you with optimal question structures, flow, and wording that maximizes engagement and data quality.',
      icon: 'fa-solid fa-robot',
    },
    {
      title: 'AI-Assisted Survey Grading',
      description:
        'Grade responses quickly and accurately, identifying fraudulent entries before they skew your results. Our AI-powered system learns from patterns in your data to flag suspicious responses, ensuring the integrity of your survey results and saving you countless hours of manual review.',
      icon: 'fa-solid fa-check-circle',
    },
    {
      title: 'Real-time Analytics Dashboard',
      description:
        'Get instant insights with our real-time analytics dashboard. Watch as responses come in and see trends emerge in real-time. Our AI-driven analysis highlights key findings and anomalies, allowing you to make data-driven decisions faster than ever before.',
      icon: 'fa-solid fa-chart-line',
    },
  ],
  secondaryFeatures: [
    {
      title: 'Seamless Integration',
      description:
        'Integrates with popular survey tools and platforms, ensuring smooth workflows.',
      icon: 'fa-solid fa-sync',
    },
    {
      title: 'Real-time Fraud Detection',
      description:
        'Get alerts as soon as fraudulent responses are detected, allowing immediate action.',
      icon: 'fa-solid fa-shield-alt',
    },
  ],
  faqs: [
    {
      question: 'How does AI improve survey design?',
      answer:
        'Our AI analyzes past survey data to suggest improvements that enhance response quality and engagement.',
    },
    {
      question: 'Can SurveySort integrate with my current survey tools?',
      answer:
        'Yes, SurveySort integrates with most major survey platforms out-of-the-box.',
    },
  ],
  testimonials: [
    {
      name: 'Jane Doe',
      role: 'CEO at Market Insights',
      feedback:
        'SurveySort has revolutionized the way we conduct surveys. The AI-driven insights are incredibly valuable.',
    },
    {
      name: 'John Smith',
      role: 'Research Director at Global Data',
      feedback:
        'The fraud detection capabilities alone make SurveySort indispensable for our research projects.',
    },
  ],
  footer: {
    links: [
      { label: 'Privacy Policy', url: '/privacy' },
      { label: 'Terms of Service', url: '/terms' },
    ],
    contact: {
      email: '<EMAIL>',
      phone: '**************',
    },
  },
}

const LandingPage = () => {
  return (
    <>
      <Metadata
        title="SurveySort - AI-Powered Surveys"
        description="AI-Powered Survey Design and Fraud Detection"
      />

      <div className="min-h-screen bg-base-100 font-sans">
        <Header
          title={data.header.title}
          subtitle={data.header.subtitle}
          cta={data.header.cta}
        />

        <main>
          <PrimaryFeatures features={data.primaryFeatures} />
          <SecondaryFeatures features={data.secondaryFeatures} />
          <Faqs faqs={data.faqs} />
          <Testimonials testimonials={data.testimonials} />
        </main>

        <Footer links={data.footer.links} contact={data.footer.contact} />
      </div>
    </>
  )
}

export default LandingPage
