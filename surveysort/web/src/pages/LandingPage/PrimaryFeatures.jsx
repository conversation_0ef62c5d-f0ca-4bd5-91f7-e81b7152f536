import { useState } from 'react'

import { Tab } from '@headlessui/react'
import clsx from 'clsx'

const PrimaryFeatures = ({ features }) => {
  let [selectedIndex, setSelectedIndex] = useState(0)

  return (
    <section id="features" className="bg-blue-600 py-20 sm:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="font-display text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Primary Features
          </h2>
          <p className="mt-4 text-lg text-blue-100">
            Brief description of your primary features.
          </p>
        </div>
        <Tab.Group
          as="div"
          className="mt-16 grid grid-cols-1 items-center gap-y-2 pt-10 sm:gap-y-6 md:mt-20 lg:grid-cols-12 lg:pt-0"
          selectedIndex={selectedIndex}
          onChange={setSelectedIndex}
        >
          <Tab.List className="-mx-4 flex space-x-4 overflow-x-auto pb-4 sm:mx-0 sm:overflow-visible sm:pb-0 lg:col-span-5">
            {features.map((feature, featureIndex) => (
              <Tab
                key={feature.title}
                className={({ selected }) =>
                  clsx(
                    'flex flex-col rounded-lg px-4 py-3 text-sm font-medium leading-5 focus:outline-none',
                    selected
                      ? 'bg-white/10 text-white'
                      : 'text-blue-100 hover:bg-white/5 hover:text-white'
                  )
                }
              >
                <span className="flex items-center">
                  <i className={`${feature.icon} mr-2`}></i>
                  {feature.title}
                </span>
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels className="lg:col-span-7">
            {features.map((feature) => (
              <Tab.Panel key={feature.title} unmount={false}>
                <div className="relative overflow-hidden rounded-lg bg-white px-6 py-8 shadow-xl">
                  <h3 className="text-2xl font-bold text-gray-900">
                    {feature.title}
                  </h3>
                  <p className="mt-4 text-lg text-gray-700">
                    {feature.description}
                  </p>
                </div>
              </Tab.Panel>
            ))}
          </Tab.Panels>
        </Tab.Group>
      </div>
    </section>
  )
}

export default PrimaryFeatures
