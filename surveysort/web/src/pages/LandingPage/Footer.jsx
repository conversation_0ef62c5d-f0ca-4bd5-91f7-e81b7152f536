import { Link, routes } from '@redwoodjs/router'

const Footer = ({ links, contact }) => {
  return (
    <footer className="footer footer-center rounded bg-base-200 p-10 text-base-content">
      <div className="grid grid-flow-col gap-4">
        {links.map((link, index) => (
          <Link key={index} to={link.url} className="link-hover link">
            {link.label}
          </Link>
        ))}
      </div>
      <div>
        <div className="grid grid-flow-col gap-4">
          <a href={`mailto:${contact.email}`}>
            <i className="fa-solid fa-envelope text-2xl"></i>
          </a>
          <a href={`tel:${contact.phone}`}>
            <i className="fa-solid fa-phone text-2xl"></i>
          </a>
        </div>
      </div>
      <div>
        <p>
          Copyright © {new Date().getFullYear()} - All rights reserved by
          SurveySort
        </p>
      </div>
    </footer>
  )
}

export default Footer
