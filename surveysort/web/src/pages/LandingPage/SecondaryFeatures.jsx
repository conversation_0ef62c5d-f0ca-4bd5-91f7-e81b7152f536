'use client'

import { useId } from 'react'

import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import clsx from 'clsx'

const Feature = ({ feature, isActive, className, ...props }) => {
  return (
    <div
      className={clsx(className, !isActive && 'opacity-75 hover:opacity-100')}
      {...props}
    >
      <div
        className={clsx(
          'w-9 rounded-lg',
          isActive ? 'bg-primary' : 'bg-base-300'
        )}
      >
        <i className={`${feature.icon} text-2xl text-base-content`}></i>
      </div>
      <h3
        className={clsx(
          'mt-6 text-sm font-medium',
          isActive ? 'text-primary' : 'text-base-content'
        )}
      >
        {feature.title}
      </h3>
      <p className="font-display mt-2 text-xl text-base-content">
        {feature.description}
      </p>
    </div>
  )
}

const FeaturesMobile = ({ features }) => {
  return (
    <div className="-mx-4 mt-20 flex flex-col gap-y-10 overflow-hidden px-4 sm:-mx-6 sm:px-6 lg:hidden">
      {features.map((feature, index) => (
        <div key={index}>
          <Feature feature={feature} className="mx-auto max-w-2xl" isActive />
        </div>
      ))}
    </div>
  )
}

const FeaturesDesktop = ({ features }) => {
  return (
    <TabGroup className="hidden lg:mt-20 lg:block">
      {({ selectedIndex }) => (
        <>
          <TabList className="grid grid-cols-3 gap-x-8">
            {features.map((feature, featureIndex) => (
              <Feature
                key={featureIndex}
                feature={{
                  ...feature,
                  title: (
                    <Tab className="ui-not-focus-visible:outline-none">
                      <span className="absolute inset-0" />
                      {feature.title}
                    </Tab>
                  ),
                }}
                isActive={featureIndex === selectedIndex}
                className="relative"
              />
            ))}
          </TabList>
        </>
      )}
    </TabGroup>
  )
}

const SecondaryFeatures = ({ features }) => {
  return (
    <section
      id="secondary-features"
      aria-label="Secondary Features"
      className="bg-base-200 py-20"
    >
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl md:text-center">
          <h2 className="mb-12 text-center text-3xl font-bold">
            Secondary Features
          </h2>
        </div>
        <FeaturesMobile features={features} />
        <FeaturesDesktop features={features} />
      </div>
    </section>
  )
}

export default SecondaryFeatures
