export default function Faqs({ faqs }) {
  return (
    <section
      id="faq"
      aria-labelledby="faq-title"
      className="relative overflow-hidden bg-gray-50 py-20 sm:py-32"
    >
      {/* <Image
        className="absolute left-1/2 top-0 max-w-none -translate-y-1/4 translate-x-[-30%]"
        src={backgroundImage}
        alt=""
        width={1558}
        height={946}
        unoptimized
      /> */}
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:mx-0">
          <h2
            id="faq-title"
            className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
          >
            Frequently asked questions
          </h2>
          <p className="mt-4 text-lg text-gray-700">
            Can't find the answer you're looking for? Reach out to our customer
            support team.
          </p>
        </div>
        <ul className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:grid-cols-2 lg:max-w-none lg:grid-cols-3">
          {faqs.map((faq, index) => (
            <li key={index} className="flex flex-col">
              <h3 className="text-lg font-semibold leading-7 text-gray-900">
                {faq.question}
              </h3>
              <p className="mt-4 text-sm text-gray-700">{faq.answer}</p>
            </li>
          ))}
        </ul>
      </div>
    </section>
  )
}
