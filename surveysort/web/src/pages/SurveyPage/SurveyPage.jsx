import { useState } from 'react'
import { Metadata } from '@redwoodjs/web'
import { Dialog, DialogBody } from 'src/components/catalyst/dialog'
import NewSurveyForm from 'src/components/NewSurveyForm'
import SurveyListCell from 'src/components/SurveyListCell'
import AppShellLayout from 'src/layouts/AppShellLayout'

const SurveyPage = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const isDemoMode = false;

  const openDialog = () => {
    if (!isDemoMode) {
      setIsDialogOpen(true)
    }
  }

  const closeDialog = () => {
    setIsDialogOpen(false)
  }

  return (
    <>
      <AppShellLayout>
        <Metadata title="Survey" description="Survey page" />

        <div className="mx-auto p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-semibold">Surveys</h1>
            <div
              className="tooltip"
              data-tip={
                isDemoMode ? 'Creating surveys is disabled in demo mode' : ''
              }
            >
              <button
                className={`btn btn-primary btn-sm ${isDemoMode ? 'btn-disabled' : ''
                  }`}
                onClick={openDialog}
              >
                Create Survey
              </button>
            </div>
          </div>
          <div className="mt-6">
            <SurveyListCell />
          </div>
        </div>

        <Dialog size="5xl" open={isDialogOpen} onClose={closeDialog}>
          <DialogBody>
            <NewSurveyForm onClose={closeDialog} />
          </DialogBody>
        </Dialog>
      </AppShellLayout>
    </>
  )
}

export default SurveyPage
