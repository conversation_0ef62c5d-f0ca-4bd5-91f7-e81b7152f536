import { useRef, useEffect, useState } from 'react'
import {
  Form,
  Label,
  TextField,
  PasswordField,
  Submit,
  FieldError,
} from '@redwoodjs/forms'
import { Link, navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { toast, Toaster } from '@redwoodjs/web/toast'
import { useAuth } from 'src/auth'
import { isEmailAllowed } from 'src/utils/emailUtils'

const LoginPage = () => {
  const { isAuthenticated, logIn, loading, reauthenticate } = useAuth()
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)

  // Check authentication on mount and when auth state changes
  useEffect(() => {
    const checkAuth = async () => {
      if (!loading && isAuthenticated) {
        navigate(routes.dashboard())
      }
    }
    checkAuth()
  }, [loading, isAuthenticated, reauthenticate])

  const usernameRef = useRef()
  useEffect(() => {
    usernameRef.current?.focus()
  }, [])

  const onSubmit = async (data) => {
    if (!isEmailAllowed(data.username)) {
      toast.error('Only business email addresses are allowed. Personal email domains (like Gmail, Yahoo, etc.) are not accepted.')
      return
    }

    try {
      const response = await logIn({
        username: data.username,
        password: data.password,
      })

      if (response?.error) {
        if (response.error.includes('Internal Server Error') || response.error.includes('500')) {
          toast.error('Something went wrong. Please try again later.')
          return
        }

        if (response.error.includes('verify your email')) {
          toast.error('Please verify your email address. Check your inbox for the verification link.')
        } else {
          toast.error(response.error)
        }
      } else {
        if (response.id) {
          setShowSuccessAlert(true)
          setTimeout(() => {
            window.location.reload()
          }, 3000)
        } else {
          toast.error('Please verify your email address. Check your inbox for the verification link.')
        }
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again later.')
    }
  }

  // Loading state UI
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="fixed inset-0 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100" />

        <div className="relative sm:mx-auto sm:w-full sm:max-w-md">
          <div className="skeleton h-10 w-32 mx-auto" /> {/* Logo placeholder */}
          <div className="skeleton h-8 w-48 mx-auto mt-6" /> {/* Title placeholder */}
        </div>

        <div className="relative mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="card bg-white/70 backdrop-blur-xl shadow-xl border border-white/20">
            <div className="card-body space-y-6">
              <div className="space-y-2">
                <div className="skeleton h-4 w-24" /> {/* Label placeholder */}
                <div className="skeleton h-10 w-full" /> {/* Input placeholder */}
              </div>
              <div className="space-y-2">
                <div className="skeleton h-4 w-24" /> {/* Label placeholder */}
                <div className="skeleton h-10 w-full" /> {/* Input placeholder */}
              </div>
              <div className="skeleton h-10 w-full" /> {/* Button placeholder */}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return null
  }

  return (
    <>
      <Metadata title="Login" />

      {/* Success Alert */}
      {showSuccessAlert ? (
        <div className="alert alert-success fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-auto min-w-[320px] shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm">Welcome back! You'll be redirected to dashboard momentarily...</span>
        </div>
      ) : (
        <>
          {/* Update Toaster position */}
          <Toaster
            toastOptions={{
              className: 'rw-toast',
              duration: 6000,
              position: 'top-center',
              style: {
                zIndex: 50,
                top: '1rem',
              },
            }}
          />

          <div className="relative min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            {/* Gradient Background */}
            <div className="fixed inset-0 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100" />

            <div className="relative sm:mx-auto sm:w-full sm:max-w-md">
              <img
                className="mx-auto h-10 w-auto"
                src="/logo.webp"
                alt="SurveySort"
              />
              <h2 className="mt-6 text-center text-2xl font-semibold tracking-tight">
                Sign in to your account
              </h2>
            </div>

            <div className="relative mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
              <div className="card bg-white/70 backdrop-blur-xl shadow-xl border border-white/20">
                <div className="card-body">
                  <Form onSubmit={onSubmit} className="space-y-6">
                    <div>
                      <Label
                        name="username"
                        className="label-text"
                        errorClassName="label-text text-error"
                      >
                        Email address
                      </Label>
                      <TextField
                        name="username"
                        className="input input-bordered w-full"
                        errorClassName="input input-bordered input-error w-full"
                        ref={usernameRef}
                        validation={{
                          required: {
                            value: true,
                            message: 'Email is required',
                          },
                          pattern: {
                            value: /^[^@]+@[^.]+\..+$/,
                            message: 'Please enter a valid email address',
                          },
                        }}
                      />
                      <FieldError name="username" className="text-error text-sm" />
                    </div>

                    <div>
                      <Label
                        name="password"
                        className="label-text"
                        errorClassName="label-text text-error"
                      >
                        Password
                      </Label>
                      <PasswordField
                        name="password"
                        className="input input-bordered w-full"
                        errorClassName="input input-bordered input-error w-full"
                        autoComplete="current-password"
                        validation={{
                          required: {
                            value: true,
                            message: 'Password is required',
                          },
                        }}
                      />
                      <FieldError name="password" className="text-error text-sm" />
                    </div>

                    <div className="flex items-center justify-end">
                      <Link
                        to={routes.forgotPassword()}
                        className="link link-primary text-sm"
                      >
                        Forgot password?
                      </Link>
                    </div>

                    <Submit className="btn btn-primary w-full">Sign in</Submit>

                    <p className="text-center text-xs text-base-content/50">
                      By continuing, you agree to our{' '}
                      <Link to={routes.terms()} className="link link-hover">
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link to={routes.privacy()} className="link link-hover">
                        Privacy Policy
                      </Link>
                    </p>
                  </Form>
                </div>
              </div>

              <p className="mt-10 text-center text-sm text-base-content/70">
                Not a member?{' '}
                <Link to={routes.signup()} className="link link-primary font-semibold">
                  Create an account
                </Link>
              </p>
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default LoginPage
