import { Metadata } from '@redwoodjs/web'

import SurveyDetailCell from 'src/components/SurveyDetailCell'
import AppShellLayout from 'src/layouts/AppShellLayout'
import { useLocation } from '@redwoodjs/router'
import { useEffect, useState } from 'react'
const SurveyDetailPage = ({ id, tab }) => {
  const location = useLocation()

  const [defaultTab, setDefaultTab] = useState(tab)

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    if (params.get('tab')) {
      setDefaultTab(tab);
    } else if (params.get('source') === 'IMPORTED') {
      setDefaultTab('responses');
    }
  }, [location.search])

  return (
    <AppShellLayout>
      <Metadata title="Survey Detail" description="Survey detail page" />
      <div className="h-[calc(100vh-50px)] flex flex-col overflow-hidden">
        <SurveyDetailCell
          id={id}
          currentTab={defaultTab}
        />
      </div>
    </AppShellLayout>
  )
}

export default SurveyDetailPage
