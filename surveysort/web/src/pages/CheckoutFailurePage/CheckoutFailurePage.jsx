import { Link, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'

const CheckoutFailurePage = () => {
  return (
    <>
      <Metadata title="CheckoutFailure" description="CheckoutFailure page" />

      <h1>CheckoutFailurePage</h1>
      <p>
        Find me in{' '}
        <code>./web/src/pages/CheckoutFailurePage/CheckoutFailurePage.jsx</code>
      </p>
      <p>
        My default route is named <code>checkoutFailure</code>, link to me with
        `<Link to={routes.checkoutFailure()}>CheckoutFailure</Link>`
      </p>
    </>
  )
}

export default CheckoutFailurePage
