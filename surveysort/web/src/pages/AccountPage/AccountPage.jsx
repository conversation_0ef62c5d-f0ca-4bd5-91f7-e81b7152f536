import { useAuth } from 'src/auth'
import { Metadata } from '@redwoodjs/web'
import { useState } from 'react'
import PricingModal from 'src/components/PricingModal/PricingModal'
import AppShellLayout from 'src/layouts/AppShellLayout/AppShellLayout'

const AccountPage = () => {
  const { currentUser ,hasRole} = useAuth()
  const [showPricingModal, setShowPricingModal] = useState(false)

  if (!currentUser) return <div>Loading...</div>

  const { subscription } = currentUser
  const { limits, currentUsage } = subscription
  const isAdmin = hasRole('ADMIN')

  const renderSubscriptionContent = () => {
    if (subscription.isActive) {
      return (
        <>
          <div>
            <h3 className="text-lg font-medium">{subscription.currentPlan || 'Free Trial'}</h3>
            {subscription.currentPeriodEnd && (
              <p className="text-gray-600">
                Current period ends: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
              </p>
            )}
          </div>
          <div>
            <h4 className="font-medium mb-2">Plan Limits</h4>
            <ul className="list-disc pl-5 space-y-1">
              <li>AI Questions: {limits.aiQuestions} per month</li>
              <li>Response Gradings: {limits.responseGradings} per month</li>
              <li>Insight Conversations: {limits.insightConversations} per month</li>
            </ul>
          </div>
          <button
            onClick={() => setShowPricingModal(true)}
            className="inline-block mt-4 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Manage Subscription
          </button>
        </>
      )
    }

    return (
      <div className="text-center">
        <p className="text-gray-600 mb-6">
          Subscribe to access premium features and increase your usage limits.
        </p>
        <button
          onClick={() => setShowPricingModal(true)}
          className="inline-block px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700"
        >
          Subscribe Now
        </button>
      </div>
    )
  }

  const renderUsageMetrics = () => {
    if (!subscription.isActive) {
      return (
        <div className="text-center text-gray-600">
          Subscribe to a plan to view your usage metrics.
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">AI Questions</label>
          <div className="mt-1">
            {currentUsage.aiQuestions} / {limits.aiQuestions}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-indigo-600 h-2.5 rounded-full"
              style={{
                width: `${(currentUsage.aiQuestions / limits.aiQuestions) * 100}%`
              }}
            ></div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Response Gradings</label>
          <div className="mt-1">
            {currentUsage.responseGradings} / {limits.responseGradings}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-indigo-600 h-2.5 rounded-full"
              style={{
                width: `${(currentUsage.responseGradings / limits.responseGradings) * 100}%`
              }}
            ></div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Insight Conversations</label>
          <div className="mt-1">
            {currentUsage.insightConversations} / {limits.insightConversations}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-indigo-600 h-2.5 rounded-full"
              style={{
                width: `${(currentUsage.insightConversations / limits.insightConversations) * 100}%`
              }}
            ></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <AppShellLayout>
      <Metadata title="Account" description="Manage your account settings and subscription" />

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Account Information */}
          <section className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold mb-4">Account Information</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <div className="mt-1">{currentUser.email}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Account Status</label>
                <div className="mt-1">
                  {subscription.isPaid ? (
                    <span className="text-green-600">Active Paid Account</span>
                  ) : subscription.onTrial ? (
                    <span className="text-blue-600">
                      Trial Period ({subscription.trialDaysLeft} days remaining)
                    </span>
                  ) : (
                    <span className="text-red-600">Inactive</span>
                  )}
                </div>
              </div>
            </div>
          </section>

          {/* Current Plan - Only visible to admin */}
          {isAdmin && (
            <section className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold mb-4">Current Plan</h2>
              <div className="space-y-4">
                {renderSubscriptionContent()}
              </div>
            </section>
          )}

          {/* Usage Metrics - Only visible to admin */}
          {isAdmin && (
            <section className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold mb-4">Usage Metrics</h2>
              {renderUsageMetrics()}
            </section>
          )}
        </div>
      </div>

      <PricingModal 
        open={showPricingModal} 
        onClose={() => setShowPricingModal(false)} 
      />
    </AppShellLayout>
  )
}

export default AccountPage
