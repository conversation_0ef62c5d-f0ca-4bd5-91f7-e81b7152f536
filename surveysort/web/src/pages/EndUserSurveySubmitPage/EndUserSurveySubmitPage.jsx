import { useEffect, useState } from 'react'
import { Metadata } from '@redwoodjs/web'
import EndUserSurveyCell from 'src/components/EndUserSurveyCell'

const EndUserSurveySubmitPage = ({ id }) => {
  const [token, setToken] = useState(null)

  useEffect(() => {
    // Get token from URL query parameters
    const urlParams = new URLSearchParams(window.location.search)
    const tokenParam = urlParams.get('token')
    setToken(tokenParam)
  }, [])

  return (
    <>
      <Metadata
        title="Survey"
        description="Survey submission page"
      />
      <EndUserSurveyCell id={id} token={token} />
    </>
  )
}

export default EndUserSurveySubmitPage
