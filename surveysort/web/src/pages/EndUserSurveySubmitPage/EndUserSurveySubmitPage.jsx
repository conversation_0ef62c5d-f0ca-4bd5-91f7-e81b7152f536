import { Metadata } from '@redwoodjs/web'
import { useSearchParams } from '@redwoodjs/router'
import EndUserSurveyCell from 'src/components/EndUserSurveyCell'

const EndUserSurveySubmitPage = ({ id }) => {
  const [searchParams] = useSearchParams()
  const token = searchParams.get('token')

  return (
    <>
      <Metadata
        title="Survey"
        description="Survey submission page"
      />
      <EndUserSurveyCell id={id} token={token} />
    </>
  )
}

export default EndUserSurveySubmitPage
