import { useEffect } from 'react'

import { navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'

import { useAuth } from 'src/auth'
import LoadingIndicator from 'src/components/LoadingIndicator/LoadingIndicator'

const SignoutPage = () => {
  const { logOut } = useAuth()

  useEffect(() => {
    const performSignOut = async () => {
      try {
        await logOut()
        console.log('Signed out')
        // navigate(routes.landing())
      } catch (error) {
        console.error('Error signing out:', error)
        // navigate(routes.landing())
      }
    }

    performSignOut()
  }, [logOut])

  return (
    <>
      <Metadata title="Sign Out" description="Sign out page" />
      <LoadingIndicator message="Signing out..." />
    </>
  )
}

export default SignoutPage
