// In this file, all Page components from 'src/pages` are auto-imported. Nested
// directories are supported, and should be uppercase. Each subdirectory will be
// prepended onto the component name.
//
// Examples:
//
// 'src/pages/HomePage/HomePage.js'         -> HomePage
// 'src/pages/Admin/BooksPage/BooksPage.js' -> AdminBooksPage

import { Router, Route, PrivateSet } from '@redwoodjs/router'

import { useAuth } from './auth'

const Routes = () => {
  return (
    <Router useAuth={useAuth}>
      <Route path="/template" page={TemplatePage} name="template" />
      <Route path="/privacy" page={PrivacyPage} name="privacy" />
      <Route path="/terms" page={TermsPage} name="terms" />
      <Route path="/signout" page={SignoutPage} name="signout" />
      <Route path="/s/{id}" page={EndUserSurveySubmitPage} name="endUserSurveySubmit" />
      <Route path="/s/t/{token}" page={TokenSurveySubmitPage} name="tokenSurveySubmit" />

      <PrivateSet unauthenticated="login">
        <Route path="/subscription-failure" page={CheckoutFailurePage} name="subscriptionFailure" />
        <Route path="/subscription-success" page={CheckoutSuccessPage} name="subscriptionSuccess" />
        <Route path="/survey-detail/{id}" page={SurveyDetailPage} name="surveyDetail" />
        <Route path="/survey-list" page={SurveyPage} name="surveyList" />
        <Route path="/dashboard/{view}" page={DashboardPage} name="dashboardView" />
        <Route path="/dashboard" page={DashboardPage} name="dashboard" />
        <Route path="/account" page={AccountPage} name="account" />
      </PrivateSet>

      <Route path="/login" page={LoginPage} name="login" />
      <Route path="/signup" page={SignupPage} name="signup" />
      <Route path="/forgot-password" page={ForgotPasswordPage} name="forgotPassword" />
      <Route path="/reset-password" page={ResetPasswordPage} name="resetPassword" />
      <Route path="/" page={LoginPage} name="landing" />
      <Route notfound page={NotFoundPage} />
      <Route path="/verify/{token}" page={VerifyEmailPage} name="verifyEmail" />
      <Route path="/accept-invitation" page={AcceptInvitationPage} name="acceptInvitation" />
    </Router>
  )
}

export default Routes
