import React, { useState, useCallback } from 'react'
import {
  ChevronUpIcon,
  BoltIcon,
  CalendarDaysIcon,
  CreditCardIcon,
  ClockIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { navigate, routes, Link } from '@redwoodjs/router'
import { useAuth } from 'src/auth'

import LoadingIndicator from 'src/components/LoadingIndicator/LoadingIndicator'
import OnboardingForm from 'src/components/OnboardingForm/OnboardingForm'
import PricingModal from 'src/components/PricingModal/PricingModal'
import { StackedLayout } from 'src/components/catalyst/stacked-layout'
import { trackUpgradeClick, trackOnboardingComplete, trackLogout } from 'src/components/Analytics/Analytics'
import { useFeatureFlags } from 'src/components/FeatureFlagProvider'
import { Toaster } from '@redwoodjs/web/toast'
import { TrialBanner } from 'src/components/TrialBanner'
import { Navbar, NavbarSection, NavbarSpacer } from 'src/components/catalyst/navbar'
import { Button } from 'src/components/catalyst/button'
import { Popover } from '@headlessui/react'

const AppShellLayout = ({ children }) => {
  const { currentUser, logOut, reauthenticate } = useAuth()

  const [isPricingOpen, setIsPricingOpen] = useState(false)

  const handleLogout = useCallback(async () => {
    try {
      trackLogout()
      await logOut()
      navigate(routes.login())
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }, [logOut, reauthenticate])

  // Show loading state while currentUser is not available
  if (!currentUser) {
    return <LoadingIndicator />
  }

  // Show onboarding for new users
  if (!currentUser.props?.onboardingDone) {
    return (
      <OnboardingForm
        onComplete={async () => {
          trackOnboardingComplete()
          // Reauthenticate after onboarding to get updated user props
          await reauthenticate()
        }}
      />
    )
  }

  const navbarContent = (
    <Navbar className="h-12 border-b border-base-300 bg-base-100/80 backdrop-blur-sm">
      <NavbarSection>
        <Link to={routes.dashboard()}>
          <img
            className="mx-auto h-8 ml-2 w-auto"
            src="/favicon.webp"
            alt="SurveySort"
          />
        </Link>
      </NavbarSection>
      <NavbarSpacer />
      <NavbarSection>
        <div className="dropdown dropdown-end ml-2">
          <label tabIndex={0} className="btn btn-ghost btn-sm">
            <span className="text-sm">{currentUser?.email}</span>
            <ChevronUpIcon className="h-4 w-4 ml-1" />
          </label>
          <ul tabIndex={0} className="menu dropdown-content z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><Link to={routes.account()}>Account Settings</Link></li>
            <li><button onClick={handleLogout}>Sign out</button></li>
          </ul>
        </div>
      </NavbarSection>
    </Navbar>
  )

  return (
    <div className="min-h-screen bg-base-100">
      <Toaster />
      <TrialBanner />
      <div className="fixed inset-0 bg-gradient-to-t from-primary/5 via-accent/5 to-base-100/10" />
      
      <div className="fixed top-0 w-full z-20">
        {navbarContent}
      </div>

      <div className="relative pt-12">
        <div className="relative">
          {children}
        </div>
      </div>

      <PricingModal 
        open={isPricingOpen} 
        onClose={() => setIsPricingOpen(false)} 
      />
    </div>
  )
}

export default AppShellLayout
