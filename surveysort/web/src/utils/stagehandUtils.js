const boxen = require("boxen");

function announce(message, title) {
    console.log(
        boxen(message, {
            padding: 1,
            margin: 3,
            title: title || "Stagehand",
        })
    );
}

/**
 * Get an environment variable and throw an error if it's not found
 * @param {string} name - The name of the environment variable
 * @param {boolean} [required=true] - Whether the variable is required
 * @returns {string|undefined} The value of the environment variable
 */
function getEnvVar(name, required = true) {
    const value = process.env[name];
    if (!value && required) {
        throw new Error(`${name} not found in environment variables`);
    }
    return value;
}

module.exports = {
    announce,
    getEnvVar,
};
