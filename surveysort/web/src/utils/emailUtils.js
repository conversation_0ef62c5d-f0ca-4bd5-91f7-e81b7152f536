export const normalizeEmail = (email) => {
  const [localPart, domain] = email.toLowerCase().split('@')
  // Remove everything after + in local part
  const normalizedLocalPart = localPart.split('+')[0]
  return `${normalizedLocalPart}@${domain}`
}

export const isPublicEmail = (email) => {
  const normalizedEmail = normalizeEmail(email)
  const blockedDomains = process.env.BLOCKED_DOMAINS?.trim().split(',') || []
  const domain = normalizedEmail.split('@')[1]
  return blockedDomains.includes(domain)
}

export const isEmailAllowed = (email) => {
  const normalizedEmail = normalizeEmail(email)
  
  // First check whitelist
const whitelistedEmails = process.env.WHITELISTED_EMAILS?.trim().split(',') || []
  if (whitelistedEmails.includes(normalizedEmail)) {
    return true
  }

  // Only check for public email if not whitelisted
  return !isPublicEmail(email)
} 