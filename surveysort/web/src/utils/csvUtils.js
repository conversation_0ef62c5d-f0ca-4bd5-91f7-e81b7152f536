/**
 * Utility functions for CSV generation and download
 */

/**
 * Generates CSV content from participant data
 * @param {Array} data - Array of participant objects
 * @returns {string} CSV content as string
 */
export const generateParticipantLinksCSV = (data) => {
  if (!data || data.length === 0) return ''

  // Define headers
  const headers = [
    'Survey ID',
    'Survey Name',
    'Email Address',
    'Name',
    'Generated Link',
    'Status',
    'Link Created At',
    'Link Expires At',
  ]

  // Helper function to escape CSV values
  const escapeCSVValue = (value) => {
    if (value === null || value === undefined) return ''
    const stringValue = String(value)
    // If value contains comma, quote, or newline, wrap in quotes and escape quotes
    if (
      stringValue.includes(',') ||
      stringValue.includes('"') ||
      stringValue.includes('\n')
    ) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Create CSV content
  const csvContent = [
    headers.join(','),
    ...data.map((row) =>
      [
        escapeCSVValue(row.surveyId),
        escapeCSVValue(row.surveyName),
        escapeCSVValue(row.email),
        escapeCSVValue(row.name || ''),
        escapeCSVValue(row.generatedLink),
        escapeCSVValue(row.status),
        escapeCSVValue(
          row.linkCreatedAt ? new Date(row.linkCreatedAt).toLocaleString() : ''
        ),
        escapeCSVValue(
          row.linkExpiresAt ? new Date(row.linkExpiresAt).toLocaleString() : ''
        ),
      ].join(',')
    ),
  ].join('\n')

  return csvContent
}

/**
 * Downloads CSV content as a file
 * @param {string} csvContent - The CSV content to download
 * @param {string} filename - The filename for the download
 */
export const downloadCSV = (csvContent, filename) => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console.log('downloadCSV called in non-browser environment')
    return
  }

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } else {
    // Fallback for older browsers
    window.open(`data:text/csv;charset=utf-8,${encodeURIComponent(csvContent)}`)
  }
}

/**
 * Generates filename for participant links export
 * @param {string} surveyId - The survey ID
 * @returns {string} Generated filename
 */
export const generateExportFilename = (surveyId) => {
  const date = new Date().toISOString().split('T')[0]
  return `participant-links-${surveyId}-${date}.csv`
}
