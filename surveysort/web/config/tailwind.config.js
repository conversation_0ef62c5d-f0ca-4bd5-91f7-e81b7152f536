/** @type {import('tailwindcss').Config} */
import colors from 'tailwindcss/colors'

const typography = require('@tailwindcss/typography')
const { fontFamily } = require('tailwindcss/defaultTheme')

const config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',

    // Path to Tremor module
    './node_modules/@tremor/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    transparent: 'transparent',
    current: 'currentColor',
    extend: {
      fontFamily: {
        sans: ['"Figtree"', 'sans-serif'],
      },
      colors: {
        // light mode
        tremor: {
          brand: {
            faint: colors.orange[50],
            muted: colors.orange[200],
            subtle: colors.orange[400],
            DEFAULT: colors.orange[500],
            emphasis: colors.orange[700],
            inverted: colors.white,
          },
          background: {
            muted: colors.slate[50],
            subtle: colors.slate[100],
            DEFAULT: colors.white,
            emphasis: colors.slate[700],
          },
          border: {
            DEFAULT: colors.slate[200],
          },
          ring: {
            DEFAULT: colors.slate[200],
          },
          content: {
            subtle: colors.slate[400],
            DEFAULT: colors.slate[500],
            emphasis: colors.slate[700],
            strong: colors.gray[900],
            inverted: colors.white,
          },
        },
        // dark mode
        'dark-tremor': {
          brand: {
            faint: colors.orange[950],
            muted: colors.orange[950],
            subtle: colors.orange[800],
            DEFAULT: colors.orange[500],
            emphasis: colors.orange[400],
            inverted: colors.orange[950],
          },
          background: {
            muted: '#131A2B',
            subtle: colors.slate[800],
            DEFAULT: colors.slate[900],
            emphasis: colors.slate[300],
          },
          border: {
            DEFAULT: colors.slate[800],
          },
          ring: {
            DEFAULT: colors.slate[800],
          },
          content: {
            subtle: colors.slate[600],
            DEFAULT: colors.slate[500],
            emphasis: colors.slate[200],
            strong: colors.slate[50],
            inverted: colors.slate[950],
          },
        },
      },
      boxShadow: {
        // light
        'tremor-input': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        'tremor-card':
          '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        'tremor-dropdown':
          '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        // dark
        'dark-tremor-input': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        'dark-tremor-card':
          '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        'dark-tremor-dropdown':
          '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      },
      borderRadius: {
        'tremor-small': '0.375rem',
        'tremor-default': '0.5rem',
        'tremor-full': '9999px',
      },
      fontSize: {
        'tremor-label': ['0.75rem', { lineHeight: '1rem' }],
        'tremor-default': ['0.875rem', { lineHeight: '1.25rem' }],
        'tremor-title': ['1.125rem', { lineHeight: '1.75rem' }],
        'tremor-metric': ['1.875rem', { lineHeight: '2.25rem' }],
      },
      keyframes: {
        'gradient-shift': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
      },
      animation: {
        'gradient-shift': 'gradient-shift 8s ease infinite',
      },
      backgroundImage: {
        'primary-gradient': 'linear-gradient(180deg, var(--primary) 10%, var(--primary-focus) 60%)',
      },
      backdropBlur: {
        'sm': '4px',
      },
      backgroundImage: {
        'chat-gradient': 'linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to))',
      },
    },
  },
  safelist: [
    {
      pattern:
        /^(bg-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ['hover', 'ui-selected'],
    },
    {
      pattern:
        /^(text-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ['hover', 'ui-selected'],
    },
    {
      pattern:
        /^(border-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
      variants: ['hover', 'ui-selected'],
    },
    {
      pattern:
        /^(ring-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
    {
      pattern:
        /^(stroke-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
    {
      pattern:
        /^(fill-(?:slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose)-(?:50|100|200|300|400|500|600|700|800|900|950))$/,
    },
  ],
  plugins: [typography, require('daisyui')],
  daisyui: {
    themes: [
      {
        surveysort: {
          // Warm orange primary with gradient support
          primary: 'rgba(255, 124, 0, 1)',  // #ff7c00
          'primary-focus': 'rgba(255, 124, 0, 0.1)',  // Lighter version for gradient
          'primary-content': 'rgba(255, 255, 255, 0.95)',
          
          // Soft blue secondary
          secondary: 'rgba(96, 165, 250, 0.95)',  // #60a5fa
          'secondary-content': 'rgba(255, 255, 255, 0.95)',
          
          // Warm amber accent
          accent: 'rgba(245, 158, 11, 0.95)',  // #f59e0b
          'accent-content': 'rgba(255, 255, 255, 0.95)',
          
          // Warm brown neutral
          neutral: 'rgba(120, 53, 15, 0.95)',  // #78350f
          'neutral-content': 'rgba(254, 243, 199, 0.95)',  // #fef3c7
          
          // Warmer base colors
          'base-100': 'rgba(255, 253, 250, 0.98)',  // Warm white
          'base-200': 'rgba(251, 247, 243, 0.95)',  // Warm off-white
          'base-300': 'rgba(247, 242, 236, 0.90)',  // Warm light beige
          'base-content': 'rgba(41, 37, 36, 0.95)',  // Warm dark gray
          
          // State colors
          info: 'rgba(96, 165, 250, 0.95)',     // Blue-400
          'info-content': 'rgba(255, 255, 255, 0.95)',
          success: 'rgba(34, 197, 94, 0.95)',    // Green-500
          'success-content': 'rgba(255, 255, 255, 0.95)',
          warning: 'rgba(245, 158, 11, 0.95)',   // Amber-500
          'warning-content': 'rgba(255, 255, 255, 0.95)',
          error: 'rgba(239, 68, 68, 0.95)',      // Red-500
          'error-content': 'rgba(255, 255, 255, 0.95)',
          
          '.bar-gradient': {
            'background-image': 'linear-gradient(180deg, var(--primary) 10%, var(--primary-focus) 60%)',
            'fill': 'url(#bar-gradient)'
          },
          
          // Add gradient variables
          '--gradient-from': 'rgba(255, 124, 0, 0.6)',
          '--gradient-to': 'rgba(255, 124, 0, 0.1)',
        },
      },
      'light',
      'dark',
    ],
  },
}

export default config
