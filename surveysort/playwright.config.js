import { defineConfig } from '@playwright/test';
const os = require('os');
const dotenv = require('dotenv');
// Load environment variables from .env.test
dotenv.config({ path: '.env.test' });

export default defineConfig({
  testDir: './e2e-tests',
  use: {
    headless: true,
    storageState: './storage/auth.json',
  },
  projects: [
    {
      name: 'chromium', use: {
        browserName: 'chromium',// Use prepared auth state.
        storageState: './storage/auth.json',
      },
      testIgnore: /.*auth\.js/,
      fullyParallel: true,
    },

  ],
  webServer: {
    command: `yarn dotenv -e .env.test -- yarn rw dev`,
    port: 8910,
    reuseExistingServer: !process.env.CI,
  },
  timeout: 30000,
  expect: {
    timeout: 30000, // Timeout for expect assertions
  },
  workers: Math.max(1, Math.floor(os.cpus().length)), // Use half of the available cores or at least 1
  globalSetup: './e2e-tests/setup/global-setup.js', // Optional: Run global setup tasks
  globalTeardown: './e2e-tests/setup/global-teardown.js', // Optional: Clean up resources
});
