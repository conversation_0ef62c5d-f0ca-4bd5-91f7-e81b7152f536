{"private": true, "workspaces": {"packages": ["api", "web"]}, "devDependencies": {"@redwoodjs/auth-dbauth-setup": "8.4.2", "@redwoodjs/cli-data-migrate": "8.4.2", "@redwoodjs/core": "8.4.2", "@redwoodjs/project-config": "8.4.2", "@redwoodjs/realtime": "8.4.2", "@redwoodjs/studio": "11", "dotenv-cli": "^8.0.0", "prettier-plugin-tailwindcss": "0.4.1"}, "eslintConfig": {"extends": "@redwoodjs/eslint-config", "root": true}, "engines": {"node": "=20.x"}, "prisma": {"seed": "yarn rw exec seed"}, "packageManager": "yarn@4.3.0", "scripts": {"start-workers": "node scripts/startWorkers.js", "e2e:test": "NODE_ENV=test npx playwright test --config playwright.config.js"}, "dependencies": {"node-gyp": "^11.0.0"}}