{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "WaitForDevServer",
      "group": "none",
      "type": "shell",
      "command": "bash",
        "args": [
          "-c",
          "while ! echo -n > /dev/tcp/localhost/18911; do sleep 1; done;"
      ],
      "windows": {
        "command": "powershell",
        "args": [
          "-NoProfile",
          "-ExecutionPolicy", "Bypass",
          "while (-not (Test-NetConnection -ComputerName localhost -Port 18911)) { Start-Sleep -Seconds 1 };"
        ]
      },
      "presentation": {
        "reveal": "silent",
        "revealProblems": "onProblem",
        "panel": "shared",
        "close": true
      }
    },
  ]
}
