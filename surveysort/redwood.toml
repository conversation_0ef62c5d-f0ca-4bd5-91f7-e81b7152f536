# This file contains the configuration settings for your Redwood app.
# This file is also what makes your Redwood app a Redwood app.
# If you remove it and try to run `yarn rw dev`, you'll get an error.
#
# For the full list of options, see the "App Configuration: redwood.toml" doc:
# https://redwoodjs.com/docs/app-configuration-redwood-toml

[web]
  title = "SurveySort"
  port = 8910
  host = "0.0.0.0"
  apiUrl = "/.redwood/functions" # You can customize graphql and dbauth urls individually too: see https://redwoodjs.com/docs/app-configuration-redwood-toml#api-paths
  includeEnvironmentVariables = [
    # Add any ENV vars that should be available to the web side to this array
    # See https://redwoodjs.com/docs/environment-variables#web
    "BLOCKED_DOMAINS",
    "WHITELISTED_EMAILS"
  ]
[api]
  port = 8911
  host = "0.0.0.0"
[browser]
  open = false
[notifications]
  versionUpdates = ["latest"]

[experimental.dockerfile]
	enabled = true
