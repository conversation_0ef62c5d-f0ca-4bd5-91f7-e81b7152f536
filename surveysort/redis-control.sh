#!/bin/bash

CONTAINER_NAME="redwood-redis"

start_redis() {
    echo "Starting Redis..."
    docker run --name $CONTAINER_NAME -p 6379:6379 -d redis
    echo "Redis started."
}

stop_redis() {
    echo "Stopping Redis..."
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
    echo "Redis stopped and container removed."
}

if [ "$(docker ps -q -f name=$CONTAINER_NAME)" ]; then
    echo "Redis is running. Stopping it..."
    stop_redis
else
    echo "Redis is not running. Starting it..."
    start_redis
fi