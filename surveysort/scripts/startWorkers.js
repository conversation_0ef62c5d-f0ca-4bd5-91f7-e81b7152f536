import { logger } from '$api/src/lib/logger'

import transactionalWorker from './transactionalWorker'
import analyticsWorker from './analyticsWorker'
import notificationsWorker from './notificationsWorker'

export default async ({ _args }) => {
  logger.info('Starting workers...')
  console.log('Starting workers...')
  try {
    await Promise.all([
      transactionalWorker({ _args }),
      analyticsWorker({ _args }),
      notificationsWorker({ _args })
    ])
    logger.info('All workers started successfully')
  } catch (error) {
    logger.error('Error starting workers:', error)
    console.error('Error starting workers:', error)
  }
}