import { db } from 'api/src/lib/db'

export default async function () {
  try {
    await db.$transaction([
      db.$executeRaw`INSERT INTO public."Role" (id, name)
                      VALUES ('cm63b7aua0001pzi5l9xwvwq5', 'USER')
                      ON CONFLICT (id) DO NOTHING;`,
      db.$executeRaw`INSERT INTO public."Role" (id, name)
            VALUES ('cm63b7au90000pzi5cepnp96l', 'ADMIN')
            ON CONFLICT (id) DO NOTHING;`,
      db.$executeRaw`INSERT INTO public."User" (
                        id, email, "createdAt", props, "isDeleted", "hashedPassword",
                        "resetToken", "resetTokenExpiresAt", salt, "isVerified",
                        "verificationToken", "verificationTokenExpiresAt"
                      ) VALUES (
                        'cm63b7pjh000212gccka73wdk',
                        '<EMAIL>',
                        '2025-01-19T07:42:18.097Z',
                        '{"title": "Test", "industry": "Education", "organization": "Unso", "preferredName": "Test", "onboardingDone": true, "signupEmailQueued": false}',
                        false,
                        '1476ab07bc1d16637061a19e20e99aa29d7d21981336186439c1a71523fc9764|16384|8|1',
                        null,
                        null,
                        '4aeed83fbc241e6ffeff77cd331f83e47cacb78e759f9810bb820385938fbd57',
                        true,
                        'f6d30894bf8be8b0e1ca1b074505fea0570cfa985f1e72700441ad232bb487fe',
                        '2025-01-21T07:42:18.094Z'
                      )
                      ON CONFLICT (id) DO NOTHING;`,
      db.$executeRaw`INSERT INTO public."Account" (
                        id, name, "createdAt", "isDeleted", "checkoutInProgress",
                        "stripeCustomerId", "accessRestricted", "cancelScheduledFor",
                        "currentPlanId", "hasTrialExpired", "lastUsageReportAt",
                        "trialEndsAt", "trialStartedAt"
                      ) VALUES (
                        'cm63b7piu000112gcdfln90wv',
                        '<EMAIL>\''s Account',
                        '2025-01-19T07:42:18.097Z',
                        false,
                        false,
                        null,
                        false,
                        null,
                        null,
                        false,
                        null,
                        null,
                        '2025-01-19T07:42:18.097Z'
                      )
                      ON CONFLICT (id) DO NOTHING;`,
      db.$executeRaw`INSERT INTO public."UserRole" (id, "userId", "accountId", "roleId", "isDeleted")
                      VALUES (
                        'cm63b7pjh000412gcpqzzwomj',
                        'cm63b7pjh000212gccka73wdk',
                        'cm63b7piu000112gcdfln90wv',
                        'cm63b7au90000pzi5cepnp96l',
                        false
                      )
                      ON CONFLICT (id) DO NOTHING;`

    ]);
    console.log('Database seeding completed successfully.')
  } catch (error) {
    console.error('Error seeding the database:', error)
  }
}
