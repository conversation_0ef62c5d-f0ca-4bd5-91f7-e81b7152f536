import { Worker, Queue } from 'bullmq'
import { logger } from '$api/src/lib/logger'
import { reportBulkUsage } from '$api/src/lib/tasks'
import { connection } from '$api/src/queue/index'
import { generateSignedUrlForReports } from '$api/src/lib/generateSignedUrlForReports'

/* add more job handlers here */
const jobHandlers = {
  reportBulkUsage,
  generateSignedUrlForReportExportRequests: generateSignedUrlForReports,


}
const processJob = async (job) => {
  logger.info(`Processing analytics job: ${job.name}`)
  const handler = jobHandlers[job.name]
  if (handler) {
    await handler(job.data)
  } else {
    logger.error(`No handler found for job type: ${job.name}`)
  }
}

export default async ({ _args }) => {
  logger.info('Analytics Worker started!')

  const analyticsQueue = new Queue('analyticsQueue', { connection })

  const worker = new Worker('analyticsQueue', processJob, {
    connection,
    concurrency: 1,
  })

  worker.on('completed', (job) => {
    logger.info(`Analytics job ${job.id} has completed!`)
  })

  worker.on('failed', (job, err) => {
    logger.error(`Analytics job ${job.id} has failed: ${err.message}`)
  })

  // Schedule the reportBulkUsage job to run daily at 1 AM
  await analyticsQueue.add(
    'reportBulkUsage',
    {},
    {
      repeat: {
        cron: '0 1 * * *', // Run at 1 AM every day
      },
    }
  )

  logger.info('Scheduled reportBulkUsage cron job')
}
