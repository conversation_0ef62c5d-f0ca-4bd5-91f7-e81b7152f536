// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { db } from 'api/src/lib/db'

import seedDemo from './seedDemo'
import seedTestDatabaseData from './seedTestDatabaseData'
import { seedCategories } from './seedQuestionLibrary'

// Manually apply seeds via the `yarn rw prisma db seed` command.
//
// Seeds automatically run the first time you run the `yarn rw prisma migrate dev`
// command and every time you run the `yarn rw prisma migrate reset` command.
//
// See https://redwoodjs.com/docs/database-seeds for more info

async function seedRoles() {
  try {
    if (process.env.NODE_ENV == 'test') {
      return;
    };
    // Define the roles to be seeded
    const roles = [
      { name: 'ADMIN' }, // Assuming 'ADMIN' is a valid value of the RoleName enum
      { name: 'USER' }, // Assuming 'USER' is a valid value of the RoleName enum
    ]

    // Check if roles already exist to prevent duplicates
    const existingRoles = await db.role.findMany({
      where: {
        name: {
          in: roles.map((role) => role.name),
        },
      },
    })

    // Filter roles that need to be added
    const rolesToAdd = roles.filter(
      (role) => !existingRoles.find((r) => r.name === role.name)
    )

    // Seed new roles
    if (rolesToAdd.length > 0) {
      await db.role.createMany({
        data: rolesToAdd,
      })
      console.info(`Seeded roles: ${rolesToAdd.map((r) => r.name).join(', ')}`)
    } else {
      console.info('Roles already exist, no new roles were seeded')
    }
  } catch (error) {
    console.error('Error seeding roles:', error)
    throw error // Re-throw error to handle it further up if necessary
  }
}

export default async () => {
  try {
    await seedRoles() // Call the function to seed roles
    await seedDemo() // Call the function to seed demo data
    await seedCategories() // call the function to seed categories and questions

    if (process.env.NODE_ENV == 'test') {
      await seedTestDatabaseData();
    }
    console.info('Seeding complete.')
  } catch (error) {
    console.error('Error during the seeding process:', error)
  }
}
