import { Worker } from 'bullmq'
import { logger } from '$api/src/lib/logger'
import {
  companySendsPasswordResetLink,
  companySendsVerificationLink,
  companySendsWelcomeEmail,
  companySendsInvitation,
  sendDownloadSurveyResponsesEmailLink,
  adminSendsInvitationToUser,
} from '$api/src/lib/tasks'
import { connection, transactionalQueue, reportExportRequestsQueue } from '$api/src/queue/index'


const processJob = async (job) => {
  logger.info(`Processing transactional job: ${job.name}`)
  const handler = jobHandlers[job.name]

  if (handler) {
    await handler(job.data)
  } else {
    logger.error(`No handler found for job type: ${job.name}`)
  }
}


const jobHandlers = {
  companySendsVerificationLink,
  companySendsWelcomeEmail,
  companySendsPasswordResetLink,
  companySendsInvitation,
  adminSendsInvitationToUser,
  sendDownloadSurveyResponsesEmailLink
}

export default async ({ _args }) => {
  logger.info('Transactional Worker started!')

  // Create worker for transactional queue
  const transactionalWorker = new Worker('transactionalQueue', processJob, {
    lockDuration: 180000,
    stallInterval: 60000,
    maxStalledCount: 3,
    connection,
  })


  transactionalWorker.on('completed', (job) => {
    logger.info(`Transactional job ${job.id} has completed!`)
  })

  transactionalWorker.on('failed', (job, err) => {
    logger.error(`Transactional job ${job.id} has failed with ${err.message}`)
  })


  console.log('Transactional Worker ready!')

  // Return cleanup function for graceful shutdown
  return {
    cleanup: async () => {
      await transactionalWorker.close()
      logger.info('Workers closed')
    }
  }
}
