import { db } from 'api/src/lib/db'

export default async function () {
  const demoAccountName = 'Demo Account'

  // Query for the demo account
  let demoAccount = await db.account.findFirst({
    where: { name: demoAccountName },
  })

  // If the demo account doesn't exist, create it
  if (!demoAccount) {
    demoAccount = await db.account.create({
      data: {
        name: demoAccountName,
      },
    })
  }

  // Clean up existing demo data
  const demoSurveys = await db.survey.findMany({
    where: { accountId: demoAccount.id },
    select: { id: true },
  })
  const demoSurveyIds = demoSurveys.map((survey) => survey.id)

  // Delete SurveyAnalytics records first
  await db.surveyAnalytics.deleteMany({
    where: { surveyId: { in: demoSurveyIds } },
  })

  // Delete SurveyInsightConversation records
  await db.surveyInsightConversation.deleteMany({
    where: { surveyId: { in: demoSurveyIds } },
  })

  await db.surveyResponseEnrichedData.deleteMany({
    where: { surveyResponse: { surveyId: { in: demoSurveyIds } } },
  })
  await db.surveyResponse.deleteMany({
    where: { surveyId: { in: demoSurveyIds } },
  })
  await db.question.deleteMany({
    where: { surveyId: { in: demoSurveyIds } },
  })
  await db.survey.deleteMany({
    where: { id: { in: demoSurveyIds } },
  })

  console.log(`Cleaned up data for ${demoSurveyIds.length} surveys`)

  // Create surveys
  const surveys = await Promise.all([
    db.survey.create({
      data: {
        title: 'Customer Satisfaction Survey',
        accountId: demoAccount.id,
        audience: 'Customers',
        surveyObjective: 'Improve customer experience',
        additionalContext: 'Annual survey for product improvement',
        status: 'PUBLISHED',
        source: 'CREATED',
      },
    }),
    db.survey.create({
      data: {
        title: 'Employee Engagement Survey',
        accountId: demoAccount.id,
        audience: 'Employees',
        surveyObjective: 'Improve workplace culture',
        additionalContext: 'Quarterly employee feedback',
        status: 'DRAFT',
        source: 'CREATED',
      },
    }),
    db.survey.create({
      data: {
        title: 'Product Feature Prioritization',
        accountId: demoAccount.id,
        audience: 'Product Users',
        surveyObjective: 'Prioritize new feature development',
        additionalContext: 'Gathering user input for product roadmap',
        status: 'PUBLISHED',
        source: 'CREATED',
      },
    }),
    db.survey.create({
      data: {
        title: 'Website Usability Feedback',
        accountId: demoAccount.id,
        audience: 'Website Visitors',
        surveyObjective: 'Improve website user experience',
        additionalContext: 'Continuous improvement of our online presence',
        status: 'COMPLETED',
        source: 'CREATED',
      },
    }),
    db.survey.create({
      data: {
        title: 'Post-Purchase Evaluation',
        accountId: demoAccount.id,
        audience: 'Recent Customers',
        surveyObjective:
          'Assess customer satisfaction and identify areas for improvement',
        additionalContext: 'Sent to customers 7 days after purchase',
        status: 'PUBLISHED',
        source: 'CREATED',
      },
    }),
  ])

  console.log('Created surveys:', surveys)

  // Create questions for the survey
  const questions = await Promise.all([
    createQuestion(surveys[0].id, {
      title: 'How often do you purchase from us?',
      type: 'MULTIPLE_CHOICE',
      required: true,
      order: 0,
      questionConfig: {
        choices: [
          'Once a week',
          'Once a month',
          'Once every few months',
          'Once a year',
          'Less than once a year',
        ],
      },
    }),
    createQuestion(surveys[0].id, {
      title: 'Overall, how satisfied are you with our products/services?',
      type: 'RATING',
      required: true,
      order: 1,
      questionConfig: {
        rating: 5,
      },
    }),
    createQuestion(surveys[0].id, {
      title: 'How well do our products/services meet your needs?',
      type: 'SCALE',
      required: true,
      order: 2,
      questionConfig: {
        scale: 10,
      },
    }),
    createQuestion(surveys[0].id, {
      title:
        'Is there anything you would like to see improved or changed in our products/services?',
      type: 'ESSAY',
      required: false,
      order: 3,
      questionConfig: {
        maxLength: 500,
        minLength: 20,
      },
    }),
    createQuestion(surveys[0].id, {
      title:
        'How likely are you to recommend our products/services to your friends?',
      type: 'SCORING',
      required: true,
      order: 4,
      questionConfig: {
        scale: 10,
      },
    }),
    createQuestion(surveys[0].id, {
      title: 'Have you had any problems with our products/services?',
      type: 'MULTIPLE_CHOICE',
      required: true,
      order: 5,
      questionConfig: {
        choices: ['Yes', 'No'],
        allowWriteIn: true,
      },
    }),
    createQuestion(surveys[0].id, {
      title: 'If you had a problem, how quickly was it resolved?',
      type: 'RANGE',
      required: false,
      order: 6,
      questionConfig: {
        scale: 10,
      },
    }),
    createQuestion(surveys[0].id, {
      title: 'How would you rate our customer service?',
      type: 'RATING',
      required: true,
      order: 7,
      questionConfig: {
        rating: 5,
      },
    }),
    createQuestion(surveys[0].id, {
      title:
        'Is there anything else you would like to share about your experience with us?',
      type: 'ESSAY',
      required: false,
      order: 8,
      questionConfig: {
        maxLength: 500,
        minLength: 20,
      },
    }),
    createQuestion(surveys[0].id, {
      title:
        'Please rate your overall satisfaction with your experience with us.',
      type: 'MATRIX',
      required: true,
      order: 9,
      questionConfig: {
        rows: [
          'Product Quality',
          'Customer Service',
          'Delivery Speed',
          'Value for Money',
        ],
        columns: [
          'Very Dissatisfied',
          'Dissatisfied',
          'Neutral',
          'Satisfied',
          'Very Satisfied',
        ],
        cellType: 'radio',
      },
    }),
  ])

  console.log('Created questions:', questions)

  // Helper function to create a question
  async function createQuestion(surveyId, questionData) {
    return db.question.create({
      data: {
        title: questionData.title,
        type: questionData.type,
        required: questionData.required,
        surveyId: surveyId,
        order: questionData.order,
        questionConfig: questionData.questionConfig,
      },
    })
  }

  // Generate more realistic response data
  const generateResponseData = (surveyId, questions, count) => {
    return Array.from({ length: count }, (_, i) => ({
      id: crypto.randomUUID(),
      surveyId,
      participantEmail: '',
      isComplete: Math.random() > 0.1, // 90% complete rate
      ipAddress: 'IP address not found',
      fingerprint: '1401000050',
      screenResolution: 'undefinedxundefined',
      timezone: '-04',
      userAgent:
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      submissionTime: new Date(
        Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000
      ).toISOString(), // Random date within next 30 days
      lastActiveTime: new Date(
        Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000 + 60000
      ).toISOString(), // 1 minute after submission
      questionResponses: questions.map((q, index) => {
        const response = generateResponse(q.type, q.questionConfig)
        return {
          response,
          questionId: q.id,
          timeToSubmit: Math.random() * 10 + 1,
          questionNumber: index,
          submissionTime: new Date(
            Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
          surveyResponseId: crypto.randomUUID(),
          keystrokeTimingData: generateKeystrokeData(response),
          isResponseCopyPasted: Math.random() > 0.95,
        }
      }),
      enrichedData: {
        authenticityScore: Math.random(),
        responseQualityScore: Math.random(),
        overallQualityScore: Math.random(),
        vpnDetected: Math.random() > 0.9,
        proxyDetected: Math.random() > 0.95,
        ipReputationScore: Math.random(),
        fraudRiskScore: Math.random() * 0.3,
        effortScore: Math.random() * 0.7 + 0.3,
        relevanceScore: Math.random() * 0.6 + 0.4,
        straightLiningDetected: Math.random() > 0.9,
        copyPastedDetected: Math.random() > 0.95,
        timeSpentOnSurvey: Math.floor(Math.random() * 600 + 60),
        keystrokePatternScore: Math.random() * 0.7 + 0.3,
        isDuplicate: Math.random() > 0.98,
      },
    }))
  }

  const generateResponse = (type, config) => {
    switch (type) {
      case 'RATING':
      case 'SCALE':
        return Math.floor(
          Math.random() * (config.rating || config.scale) + 1
        ).toString()
      case 'MULTIPLE_CHOICE':
        return config.choices[Math.floor(Math.random() * config.choices.length)]
      case 'ESSAY':
      case 'SHORT':
        return [
          'Lorem ipsum',
          'Dolor sit amet',
          'Consectetur adipiscing',
          'Elit sed do',
        ][Math.floor(Math.random() * 4)]
      default:
        return 'No response'
    }
  }

  const generateKeystrokeData = (response) => {
    if (typeof response !== 'string' || response.length === 0) return []
    let timestamp = Date.now()
    return response.split('').map((key) => {
      timestamp += Math.floor(Math.random() * 300)
      return { key, timestamp }
    })
  }

  const responseData = surveys.flatMap((survey) =>
    generateResponseData(
      survey.id,
      questions.filter((q) => q.surveyId === survey.id),
      10
    )
  )

  // Create survey responses and enriched data separately
  for (const response of responseData) {
    try {
      // Create survey response
      const surveyResponse = await db.surveyResponse.create({
        data: {
          id: response.id,
          surveyId: response.surveyId,
          participantEmail: response.participantEmail,
          isComplete: response.isComplete,
          ipAddress: response.ipAddress,
          fingerprint: response.fingerprint,
          screenResolution: response.screenResolution,
          timezone: response.timezone,
          userAgent: response.userAgent,
          submissionTime: new Date(response.submissionTime),
          lastActiveTime: new Date(response.lastActiveTime),
          questionResponses: response.questionResponses,
        },
      })

      // Create enriched data separately
      await db.surveyResponseEnrichedData.create({
        data: {
          surveyResponseId: surveyResponse.id,
          shouldEnrich: true,
          ...response.enrichedData,
        },
      })
    } catch (error) {
      console.error('Error creating survey response and enriched data:', error)
    }
  }

  console.log('Demo data created:', {
    demoAccount,
    surveys,
    responseCount: responseData.length,
  })

  // Create demo Conversations
  const conversations = await Promise.all(
    surveys.map(async (survey) => {
      const surveyQuestions = questions.filter(q => q.surveyId === survey.id)
      return db.surveyInsightConversation.create({
        data: {
          surveyId: survey.id,
          accountId: demoAccount.id,
          messages: [
            {
              sender: 'user',
              text: `Can you provide an overview of the key insights from the "${survey.title}" survey?`,
              timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            },
            {
              sender: 'assistant',
              text: `Certainly! I'd be happy to provide an overview of the key insights from the "${survey.title}" survey. Here are the main points:\n\n1. Response Rate: The survey has a completion rate of ${Math.floor(Math.random() * 20 + 80)}%, which is quite good.\n2. Overall Satisfaction: On average, respondents rated their satisfaction as ${(Math.random() * 2 + 3).toFixed(1)} out of 5.\n3. Key Strengths: ${generateStrengths(survey)}\n4. Areas for Improvement: ${generateAreasForImprovement(survey)}\n5. Net Promoter Score: The NPS for this survey is ${Math.floor(Math.random() * 60 + 20)}, which is ${Math.random() > 0.5 ? 'above' : 'below'} industry average.\n\nWould you like me to elaborate on any specific area?`,
              timestamp: new Date(Date.now() - 3540000).toISOString(), // 59 minutes ago
            },
            {
              sender: 'user',
              text: "Can you tell me more about the areas for improvement?",
              timestamp: new Date(Date.now() - 3480000).toISOString(), // 58 minutes ago
            },
            {
              sender: 'assistant',
              text: `Certainly! Based on the survey responses, here are more details on the areas for improvement:\n\n${elaborateAreasForImprovement(survey, surveyQuestions)}\n\nAddressing these areas could significantly enhance customer satisfaction and loyalty. Would you like recommendations on how to tackle these issues?`,
              timestamp: new Date(Date.now() - 3420000).toISOString(), // 57 minutes ago
            },
            {
              sender: 'user',
              text: "Yes, please provide some recommendations.",
              timestamp: new Date(Date.now() - 3360000).toISOString(), // 56 minutes ago
            },
            {
              sender: 'assistant',
              text: `Certainly! Here are some recommendations to address the identified areas for improvement:\n\n${generateRecommendations(survey, surveyQuestions)}\n\nImplementing these recommendations should help address the main concerns raised in the survey and improve overall customer satisfaction. Would you like me to prioritize these recommendations based on potential impact and ease of implementation?`,
              timestamp: new Date(Date.now() - 3300000).toISOString(), // 55 minutes ago
            },
          ],
        },
      })
    })
  )

  console.log('Created survey insight conversations:', conversations)

  // Helper functions for generating conversation content
  function generateStrengths(survey) {
    const strengths = [
      "Product quality received high ratings",
      "Customer service was praised for responsiveness",
      "The ordering process was found to be user-friendly",
      "Product variety was appreciated by customers",
      "Delivery speed exceeded expectations for many respondents"
    ]
    return strengths.slice(0, 3).join('. ') + '.'
  }

  function generateAreasForImprovement(survey) {
    const areas = [
      "Some customers reported issues with the website's navigation",
      "There were concerns about the pricing of certain products",
      "A few respondents mentioned delays in response to customer service inquiries",
      "Some users found the mobile app lacking in features compared to the website",
      "There were suggestions for more detailed product descriptions"
    ]
    return areas.slice(0, 3).join('. ') + '.'
  }

  function elaborateAreasForImprovement(survey, questions) {
    const elaborations = [
      "Website Navigation: 15% of respondents rated the website navigation as 'difficult' or 'very difficult'. The main issues mentioned were confusing menu structures and difficulty in finding specific products.",
      "Pricing Concerns: While 60% of respondents found the pricing 'fair', 25% rated it as 'somewhat expensive' or 'very expensive'. This was particularly noted for [specific product category].",
      "Customer Service Response Time: Although overall satisfaction with customer service was high, 20% of respondents reported waiting more than 24 hours for a response to their inquiries.",
      "Mobile App Features: 30% of mobile app users rated it as 'lacking' compared to the website. Specific features mentioned as missing included [feature 1] and [feature 2].",
      "Product Descriptions: 18% of respondents suggested that product descriptions could be more detailed. This was especially noted for technical products and clothing items."
    ]
    return elaborations.slice(0, 3).join('\n\n')
  }

  function generateRecommendations(survey, questions) {
    const recommendations = [
      "1. Website Navigation: Conduct a user experience (UX) audit and implement a more intuitive menu structure. Consider A/B testing different layouts to find the most user-friendly option.",
      "2. Pricing Strategy: Review pricing structure, especially for [specific product category]. Consider introducing a loyalty program or bulk purchase discounts to provide more value to customers.",
      "3. Customer Service Efficiency: Implement an automated ticketing system to ensure no customer inquiry goes unanswered for more than 24 hours. Consider expanding the customer service team or introducing chatbots for handling simple queries.",
      "4. Mobile App Enhancement: Prioritize the development of key features for the mobile app, focusing on [feature 1] and [feature 2]. Conduct user testing to ensure the app meets customer needs.",
      "5. Product Descriptions: Develop a standardized template for product descriptions ensuring all key information is included. For technical products, consider adding specification sheets and comparison tables."
    ]
    return recommendations.slice(0, 3).join('\n\n')
  }

  // Create demo SurveyAnalytics
  const surveyAnalytics = await Promise.all(
    surveys.map(async (survey) => {
      return db.surveyAnalytics.create({
        data: {
          surveyId: survey.id,
          descriptiveStatistics: JSON.stringify({
            questionId1: {
              mean: 4.5,
              median: 4,
              mode: 5,
              standardDeviation: 1.2,
            },
            // Add more mock statistics as needed
          }),
          correlations: JSON.stringify({
            correlation1: {
              questionIds: ['questionId1', 'questionId2'],
              correlationCoefficient: 0.75,
            },
            // Add more mock correlations as needed
          })
        },
      })
    })
  )

  console.log('Created survey analytics:', surveyAnalytics)
}
