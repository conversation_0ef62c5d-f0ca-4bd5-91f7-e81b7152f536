import { Worker } from 'bullmq'
import { logger } from '$api/src/lib/logger'
import { connection } from '$api/src/queue/index'
import { 
  sendImmediateNotification, 
  sendDailyDigest, 
  sendWeeklyDigest,
  getNotificationRule 
} from '$api/src/services/notifications/notifications'

// Define job handlers
const jobHandlers = {
  sendNotification: async (data) => {
    const { ruleId } = data
    const rule = await getNotificationRule(ruleId)
    await sendImmediateNotification(rule)
  },
  sendDailyDigest: async (data) => {
    await sendDailyDigest(data.surveyId, data.surveyTitle)
  },
  sendWeeklyDigest: async (data) => {
    await sendWeeklyDigest(data.surveyId, data.surveyTitle)
  }
}

// Process job based on type
const processJob = async (job) => {
  logger.info(`Processing notification job: ${job.name}`)
  const handler = jobHandlers[job.name]
  
  if (!handler) {
    throw new Error(`No handler found for notification type: ${job.name}`)
  }

  try {
    await handler(job.data)
    logger.info(`Job ${job.id} completed successfully`)
  } catch (error) {
    logger.error(`Job ${job.id} failed:`, error)
    throw error
  }
}

// Start worker
export default async ({ _args }) => {
  try {
    logger.info('Starting Notifications Worker...')
    
    // Create worker with queue name directly
    const worker = new Worker('notificationQueue', processJob, { 
      connection,
      concurrency: 1
    })

    // Add error handler as recommended by BullMQ docs
    worker.on('error', (err) => {
      logger.error('Worker error:', err)
    })

    worker.on('completed', (job) => {
      logger.info(`Notification job ${job.id} completed`)
    })

    worker.on('failed', (job, err) => {
      logger.error(`Notification job ${job.id} failed: ${err.message}`)
    })

    console.log('Notifications Worker ready!')

    // Graceful shutdown
    const shutdown = async () => {
      await worker.close()
      logger.info('Notifications Worker shutdown complete')
      process.exit(0)
    }

    process.on('SIGTERM', shutdown)
    process.on('SIGINT', shutdown)

  } catch (error) {
    logger.error('Failed to start Notifications Worker:', error)
    throw error
  }
} 