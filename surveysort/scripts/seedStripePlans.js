import stripe from 'api/src/lib/stripe'

export const seedPricingPlans = async () => {
  const plans = [
    {
      name: 'Individual Researchers',
      description: 'Designed for academics, independent consultants, and small projects needing professional AI assistance.',
      monthlyPrice: 2499,
      annualPrice: 1900,
      features: [
        'Unlimited Responses',
        '50 AI-Generated Questions / month',
        '2,000 Survey Response Gradings / month',
        '100 Insight Conversations / month',
        'AI-Assisted Data Cleaning',
        'Skip Logic & Branching',
        'Randomization & Meta Tags',
        'Advanced Question Analytics',
        'Import Existing Surveys',
        'Multiple Question Types'
      ],
      meteredComponents: [
        {
          name: 'AI Questions',
          unitAmount: 25,
          includedQuantity: 50
        },
        {
          name: 'Response Gradings',
          unitAmount: 50,
          includedQuantity: 2000
        },
        {
          name: 'Insight Conversations',
          unitAmount: 550,
          includedQuantity: 100
        }
      ]
    },
    {
      name: 'Small Teams',
      description: 'Perfect for research agencies, startups, and companies wanting collaboration and deeper AI capabilities.',
      monthlyPrice: 6499,
      annualPrice: 4900,
      features: [
        'Unlimited Responses',
        '200 AI-Generated Questions / month',
        '10,000 Survey Response Gradings / month',
        '500 Insight Conversations / month',
        'Collaborative Dashboards',
        'Real-Time Analytics',
        'Advanced Question Types',
        'Conditional Branching & Preview',
        'AI-Curated Templates',
        'Enhanced Data Quality Screening'
      ],
      meteredComponents: [
        {
          name: 'AI Questions',
          unitAmount: 20,
          includedQuantity: 200
        },
        {
          name: 'Response Gradings',
          unitAmount: 40,
          includedQuantity: 10000
        },
        {
          name: 'Insight Conversations',
          unitAmount: 400,
          includedQuantity: 500
        }
      ]
    },
    {
      name: 'Enterprise',
      description: 'For large organizations or high-volume research teams requiring custom SLAs, dedicated support, and extensive integrations.',
      monthlyPrice: 25000,
      annualPrice: 20000,
      features: [
        'Custom Pricing (contact sales)',
        'Negotiable or Unlimited AI Usage',
        'Priority Support & dedicated account manager',
        'Full API Access',
        'Human-in-the-Loop AI',
        'Advanced Security & compliance',
        'Tailored Onboarding & training'
      ],
      meteredComponents: [
        {
          name: 'AI Questions',
          unitAmount: 10,
          includedQuantity: 50000
        },
        {
          name: 'Response Gradings',
          unitAmount: 20,
          includedQuantity: 200000
        },
        {
          name: 'Insight Conversations',
          unitAmount: 200,
          includedQuantity: 25000
        }
      ],
      metadata: {
        isEnterprise: true,
        hideFromPricing: true,
        contactSales: true,
        customizableLimits: true,
        defaultLimits: {
          aiQuestions: 50000,
          responseGradings: 200000,
          insightConversations: 25000
        }
      }
    }
  ]

  try {
    // Create products and prices for each tier
    const products = await Promise.all(
      plans.map(async (plan) => {
        const product = await createProduct(plan)

        return product
      })
    )

    console.log('Seeding completed.')
    return products
  } catch (error) {
    console.error('Error seeding plans from Stripe:', error)
    throw error
  }
}

const createMeteredPrice = async (product, component, isEnterprise) => {
  try {
    return await stripe.prices.create({
      product: product.id,
      currency: 'usd',
      recurring: {
        interval: 'month',
        usage_type: 'metered',
        aggregate_usage: 'sum'
      },
      billing_scheme: 'tiered',
      tiers_mode: 'graduated',
      tiers: [
        {
          up_to: component.includedQuantity,
          unit_amount: 0, // Free tier
          flat_amount: 0
        },
        {
          up_to: 'inf',
          unit_amount: component.unitAmount, // Per-unit price after free tier
          flat_amount: 0
        }
      ],
      metadata: {
        type: 'metered',
        component_name: component.name,
        included_quantity: component.includedQuantity.toString(),
        block_size: '100',
        isEnterprise: isEnterprise ? 'true' : 'false',
        customizable: isEnterprise ? 'true' : 'false'
      }
    })
  } catch (error) {
    console.error(`Error creating metered price for ${component.name}:`, error)
    throw error
  }
}

const createProduct = async (plan) => {
  try {
    const product = await stripe.products.create({
      name: plan.name,
      description: plan.description,
      metadata: {
        features: plan.features.join(','),
        plan_type: plan.monthlyPrice ? 'subscription' : 'custom',
        ...(plan.metadata || {}),
        isEnterprise: plan.name === 'Enterprise' ? 'true' : 'false',
        defaultLimits: plan.metadata?.defaultLimits 
          ? JSON.stringify(plan.metadata.defaultLimits)
          : null
      },
      active: true
    })

    const prices = []

    if (plan.monthlyPrice) {
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.monthlyPrice,
        currency: 'usd',
        recurring: { interval: 'month' },
        metadata: { 
          type: 'base', 
          billing_period: 'monthly',
          isEnterprise: plan.name === 'Enterprise' ? 'true' : 'false'
        }
      })
      prices.push(monthlyPrice)
    }

    if (plan.annualPrice) {
      const annualPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.annualPrice * 12,
        currency: 'usd',
        recurring: { interval: 'year' },
        metadata: { 
          type: 'base', 
          billing_period: 'annual',
          isEnterprise: plan.name === 'Enterprise' ? 'true' : 'false'
        }
      })
      prices.push(annualPrice)
    }

    if (plan.meteredComponents) {
      for (const component of plan.meteredComponents) {
        const meteredPrice = await createMeteredPrice(product, component, plan.name === 'Enterprise')
        prices.push(meteredPrice)
      }
    }

    // Set default price
    if (prices.length > 0) {
      const defaultPrice = prices.find(p => p.recurring.interval === 'month') || prices[0]
      await stripe.products.update(product.id, {
        default_price: defaultPrice.id
      })
    }

    return { ...product, prices }
  } catch (error) {
    console.error(`Error creating product ${plan.name}:`, error)
    throw error
  }
}

export default async ({ args }) => {
  console.log('Seeding pricing plans...')
  await seedPricingPlans()
  console.log('Seeding completed.')
  return { success: true }
}
