import { initStagehand, closeStagehand, page, context, stagehand } from './setup/stagehand';
import { beforeAll, afterAll, test, expect } from '@playwright/test';
beforeAll(async () => {
  await initStagehand();
});

afterAll(async () => {
  await closeStagehand();
});

test.skip('Signup Page', () => {

  test('should show email in use message for valid signup', async () => {
    await page.goto('/signup');
    await page.fill('input[name="username"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page.getByText('Username `<EMAIL>` already in use')).toBeVisible();
  });

  test('should show error for invalid signup email', async () => {
    await page.goto('/signup');
    await page.fill('input[name="username"]', 'invalid-email');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    // Assert error message
    await expect(page.getByText('Please enter a valid email address')).toBeVisible();

  });

  test('should show error for disallowed email domain', async () => {
    await page.goto('/signup');
    await page.fill('input[name="username"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    // Assert error message
    await expect(page.getByText('Only business email addresses are allowed. Personal email domains (like Gmail, Yahoo, etc.) are not accepted.')).toBeVisible();

  });
});
