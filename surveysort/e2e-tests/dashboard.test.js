import { initStagehand, closeStagehand, page, context, stagehand } from './setup/stagehand';
import { beforeAll, afterAll, test, expect } from '@playwright/test';
const storageObj = require('../storage/auth.json');

beforeAll(async () => {
  await initStagehand();
  await context.addCookies(storageObj.cookies);
});

afterAll(async () => {
  await closeStagehand();
});


test.describe('Dashboard Page', () => {
  test('should navigate to Surveys view by default', async () => {
    await page.goto('/dashboard');
    await expect(page).toHaveURL('/dashboard');
    await expect(page.getByText('Create Survey')).toBeVisible();
  });

  test('should navigate to Templates view', async () => {
    await page.goto('/dashboard');
    await page.click('button:has-text("Templates")');
    await expect(page).toHaveURL('/dashboard/templates');
    await expect(page.getByText('Survey Templates')).toBeVisible();
  });

  test('should navigate to Members view', async () => {
    await page.goto('/dashboard');
    await page.click('button:has-text("Members")');
    await expect(page).toHaveURL('/dashboard/members');
    await expect(page.getByText('Team Members')).toBeVisible();
  });
});
