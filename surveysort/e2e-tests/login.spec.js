import { initStagehand, closeStagehand, page } from './setup/stagehand';
import { beforeAll, afterAll, test, expect } from '@playwright/test';

beforeAll(async () => {
  await initStagehand();
});

afterAll(async () => {
  await closeStagehand();
});


test.skip('Login Page', () => {
  test('should show error for invalid email', async () => {
    await page.goto('/login');
    await page.fill('input[name="username"]', 'invalid-email');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page.getByText('Please enter a valid email address')).toBeVisible();
  });

  test('should show error for disallowed email', async () => {
    await page.goto('/login');
    await page.fill('input[name="username"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page.getByText('Only business email addresses are allowed. Personal email domains (like Gmail, Yahoo, etc.) are not accepted.')).toBeVisible();
  });

  test('should show error for incorrect credentials', async () => {
    await page.goto('/login');
    await page.fill('input[name="username"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    await expect(page.getByText('Incorrect <NAME_EMAIL>')).toBeVisible();
  });
});

