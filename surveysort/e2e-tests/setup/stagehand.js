import { Stagehand } from '@browserbasehq/stagehand'; // Adjust the path as needed
import { green, red, blue } from 'colorette'; // Import specific color functions
import StagehandConfig from './stagehand.config';

let stagehand, page, context;

export const initStagehand = async () => {
  try {
    stagehand = new Stagehand({
      ...StagehandConfig,
    });
    await stagehand.init();
    page = stagehand.page;
    context = stagehand.context;

    console.log(green('Stagehand initialized for testing!'));
  } catch (error) {
    console.error(red('Error initializing Stagehand for tests:'), error);
    throw error;
  }
};

export const closeStagehand = async () => {
  if (stagehand) {
    await stagehand.close();
    console.log(blue('Stagehand closed after tests.'));
  }
};

export { page, stagehand, context };
