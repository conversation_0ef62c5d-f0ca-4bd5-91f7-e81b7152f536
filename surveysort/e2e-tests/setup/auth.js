const { chromium } = require('@playwright/test');
module.exports = async () => {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  await page.goto('/login');
  await page.fill('input[name="username"]', 'beegal<PERSON><PERSON><PERSON><PERSON>@gmail.com');
  await page.fill('input[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  // Wait for the page to load and the dashboard to be visible
  await page.waitForTimeout(3000); // Wait for 3 seconds
  await page.reload({ waitUntil: 'load' });
  // Save the storage state to a file
  await page.context().storageState({ path: 'storage/auth.json' });
  // Wait for navigation or confirmation of login success
  await page.waitForNavigation();

  // Save storage state to auth.json
  await context.storageState({ path: 'auth.json' });
  console.log('Authentication state saved to auth.json');

  await browser.close();
};
