import { db } from 'api/src/lib/db'

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

module.exports = async () => {
  console.log('Running global teardown...');
  try {
    // Drop the database
    const databaseUrl = process.env.TEST_DATABASE_URL
    const databaseName = databaseUrl.split('/').pop() // Extract database name from DATABASE_URL

    const adminDatabaseUrl = databaseUrl.replace(databaseName, 'postgres') // Switch to the "postgres" database

    const adminDb = new PrismaClient({
      datasources: {
        db: {
          url: adminDatabaseUrl,
        },
      },
    })
    // Terminate all connections to the target database
    await adminDb.$executeRawUnsafe(`
    DO $$ BEGIN
      PERFORM pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE datname = '${databaseName}' AND pid <> pg_backend_pid();
    END $$;
  `)

    // Drop the target database
    await adminDb.$executeRawUnsafe(`DROP DATABASE "${databaseName}";`)

    console.log('✅ Test database dropped successfully.');
  } catch (error) {
    console.error('❌ Failed to drop the test database:', error);
  }
  finally {
    await db.$disconnect();
  }
};
