const { execSync } = require('child_process');

module.exports = async () => {
  console.log('Running global setup with DB:', process.env.TEST_DATABASE_URL);
  // Reset and migrate the test database
  // Seed the test database

  execSync('yarn dotenv -e .env.test -- yarn rw prisma migrate reset --skip-seed --force', { stdio: 'inherit' });
  execSync(`yarn dotenv -e .env.test -- yarn rw prisma db seed`, { stdio: 'inherit' });

  console.log('Test database is ready!');

};
